import YAML from 'yaml';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { BotConfig } from '../types/index.js';

// ES module equivalent of __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export class ConfigManager {
  private static configPath = ConfigManager.findConfigPath();
  private static config: BotConfig | null = null;

  private static findConfigPath(): string {
    // Try multiple possible locations for config.yml
    const possiblePaths = [
      path.join(process.cwd(), 'config.yml'),
      path.join(process.cwd(), '..', '..', 'config.yml'), // From dashboard directory
      path.join(process.cwd(), '..', 'config.yml'),
      path.join(__dirname, '..', '..', 'config.yml'),
      path.join(__dirname, '..', '..', '..', 'config.yml'),
    ];

    for (const configPath of possiblePaths) {
      if (fs.existsSync(configPath)) {
        return configPath;
      }
    }

    // Default fallback
    return path.join(process.cwd(), 'config.yml');
  }

  public static load(configPath?: string): BotConfig {
    if (configPath) {
      ConfigManager.configPath = configPath;
    }

    const fullPath = path.resolve(ConfigManager.configPath);

    if (!fs.existsSync(fullPath)) {
      throw new Error(`Configuration file not found: ${fullPath}`);
    }

    try {
      const fileContent = fs.readFileSync(fullPath, 'utf8');
      const config = YAML.parse(fileContent) as BotConfig;
      
      ConfigManager.validateConfig(config);
      ConfigManager.config = config;
      
      return config;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Failed to load configuration: ${error.message}`);
      }
      throw new Error('Failed to load configuration: Unknown error');
    }
  }

  public static get(): BotConfig {
    if (!ConfigManager.config) {
      throw new Error('Configuration not loaded. Call ConfigManager.load() first.');
    }
    return ConfigManager.config;
  }

  public static reload(): BotConfig {
    ConfigManager.config = null;
    return ConfigManager.load();
  }

  private static validateConfig(config: any): void {
    const requiredFields = [
      'bot.token',
      'bot.clientId',
      'bot.prefix',
      'logging.level',
      'addons.enabled',
      'addons.directory'
    ];

    for (const field of requiredFields) {
      if (!ConfigManager.getNestedValue(config, field)) {
        throw new Error(`Missing required configuration field: ${field}`);
      }
    }

    // Validate bot token format (basic check)
    if (typeof config.bot.token !== 'string' || config.bot.token.length < 50) {
      process.stdout.write('Warning: Bot token appears to be invalid or placeholder\n');
    }

    // Validate client ID
    if (typeof config.bot.clientId !== 'string' || !/^\d+$/.test(config.bot.clientId)) {
      process.stdout.write('Warning: Client ID appears to be invalid\n');
    }

    // Validate logging level
    const validLogLevels = ['error', 'warn', 'info', 'debug'];
    if (!validLogLevels.includes(config.logging.level)) {
      throw new Error(`Invalid logging level: ${config.logging.level}. Must be one of: ${validLogLevels.join(', ')}`);
    }

    // Ensure required directories exist or can be created
    const directories = [
      config.logging.file?.path,
      config.addons.directory,
      path.dirname(config.database?.path || 'data/bot.db')
    ].filter(Boolean);

    for (const dir of directories) {
      try {
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
        }
      } catch (error) {
        process.stdout.write(`Warning: Could not create directory ${dir}: ${error}\n`);
      }
    }
  }

  private static getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  public static watch(callback: (config: BotConfig) => void): void {
    const fullPath = path.resolve(ConfigManager.configPath);
    
    fs.watchFile(fullPath, (curr: fs.Stats, prev: fs.Stats) => {
      if (curr.mtime !== prev.mtime) {
        try {
          const newConfig = ConfigManager.reload();
          callback(newConfig);
        } catch (error) {
          process.stderr.write(`Failed to reload configuration: ${error}\n`);
        }
      }
    });
  }

  public static getConfig(): any {
    if (!this.config) {
      const configFile = fs.readFileSync(this.configPath, 'utf8');
      this.config = YAML.parse(configFile);
    }
    return this.config;
  }

  public static reloadConfig(): void {
    const configFile = fs.readFileSync(this.configPath, 'utf8');
    this.config = YAML.parse(configFile);
  }
} 