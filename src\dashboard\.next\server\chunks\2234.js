"use strict";exports.id=2234,exports.ids=[2234],exports.modules={2234:(e,s,o)=>{o.a(e,async(e,n)=>{try{o.r(s),o.d(s,{default:()=>c});var r=o(8732),t=o(9733),a=o(2015),l=o(8079),i=e([t]);t=(i.then?(await i)():i)[0];let d={welcome:{enabled:!1,channelId:"",messages:[{title:"\uD83C\uDFAE Welcome to {guild}, {userName}!",description:"Pull up a chair and make yourself comfortable. We've got games, chaos, and questionable life choices waiting for you!\n\n\uD83D\uDCC5 Account Created: {UserCreation}\n\uD83C\uDFAF Join Date: {joinDate}",color:"#FF6B35",footer:"You're our {memberCount} member - let's get weird!"},{title:"\uD83D\uDEA8 Error 404: Chill Not Found! \uD83D\uDEA8",description:"Hey {user}, looks like you've stumbled into our chaotic corner of the internet. Hope you brought snacks and a sense of humor!\n\n\uD83C\uDFAE Member #{memberCountNumeric}\n\uD83D\uDCC5 Joined: {joinTime}",color:"#8B0000",footer:"Welcome to the madness!"},{title:"\uD83C\uDFAF Player {userName} has entered the game!",description:"Welcome to {server} where the games are wild and the conversations are... well, let's just say we're not your typical Discord server.\n\n\uD83D\uDCC5 Account Age: {UserCreation}\n\uD83C\uDFAF Member Since: {user-joinedAt}",color:"#1E90FF",footer:"Time to level up your social life!"},{title:"\uD83D\uDD25 New challenger has appeared!",description:"It's {user}! Welcome to our adult gaming sanctuary where we take games seriously but ourselves... not so much.\n\n\uD83C\uDFAE Member #{memberCountNumeric}\n⏰ Joined: {joinTime}",color:"#DC143C",footer:"Ready to game and chill?"},{title:"\uD83C\uDFB2 Welcome to the chaos, {userName}!",description:"You've joined {server} - where good decisions come to die and epic gaming moments are born. Buckle up!\n\n\uD83D\uDCC5 Account Created: {user-createdAt}\n\uD83C\uDFAF Join Time: {joinTime}",color:"#9932CC",footer:"Let the games begin!"},{title:"⚡ Level Up! New member unlocked!",description:"{user} just joined the {guild} crew! We promise we're friendlier than our name suggests... mostly.\n\n\uD83C\uDFAE You're member #{memberCountNumeric}\n⏰ Account Age: {UserCreation}",color:"#FFD700",footer:"Achievement unlocked: Found the cool kids table"},{title:"\uD83C\uDFAA Welcome to the circus, {userName}!",description:"Hope you're ready for some adult gaming fun and conversations that would make your mother question your life choices.\n\n\uD83D\uDCC5 Joined: {joinDate}\n\uD83C\uDFAF Member #{memberCountNumeric}",color:"#FF1493",footer:"We're all mad here, but in a good way!"},{title:"\uD83C\uDFA8 Another wild gamer appears!",description:"Hey {user}! Welcome to {server} where we game hard, laugh harder, and occasionally make sense.\n\n\uD83D\uDCC5 Account Created: {UserCreation}\n⏰ Join Time: {joinTime}",color:"#20B2AA",footer:"Time to make some questionable decisions together!"},{title:"\uD83D\uDE80 Mission accepted: Welcome {userName}!",description:"You've successfully infiltrated {guild}. Your mission: have fun, play games, and embrace the chaos.\n\n\uD83C\uDFAE Member #{memberCountNumeric}\n\uD83D\uDCC5 Join Date: {joinDate}",color:"#4169E1",footer:"Good luck, you'll need it!"},{title:"\uD83C\uDFAF Critical hit! New member joined!",description:"Welcome {user} to our den of gaming degeneracy and adult conversation. Check your sanity at the door!\n\n\uD83D\uDCC5 Account Age: {UserCreation}\n⏰ Member Since: {user-joinedAt}",color:"#32CD32",footer:"RIP your free time"}],autoRole:{enabled:!1,roleIds:[],delay:1e3,retry:{enabled:!0,maxAttempts:3,delayBetweenAttempts:5e3}},nekosGif:{enabled:!0,type:"wave"}},goodbye:{enabled:!1,channelId:"",messages:[{title:"\uD83D\uDC80 Game Over for {userName}",description:"Looks like {userName} has rage quit from {server}.\nThanks for the memories and questionable life choices!\n\n{kickStatus}\n⏰ Member Since: {user-joinedAt}",color:"#8B0000",footer:"Press F to pay respects"},{title:"\uD83D\uDEAA {userName} has left the building",description:"Another one bites the dust! {userName} decided our chaos wasn't for them.\nCan't win 'em all, I guess.\n\n{kickStatus}\n\uD83D\uDD52 Left: {leaveTime}",color:"#696969",footer:"The door's always open... maybe"},{title:"\uD83D\uDCE4 Connection lost: {userName}",description:"{userName} has disconnected from {guild}.\nHope they found what they were looking for!\n\n{kickStatus}\n⏰ Member Since: {user-joinedAt}",color:"#FF6347",footer:"Thanks for gaming with us!"},{title:"\uD83C\uDFAD Plot twist: {userName} vanished!",description:"In a shocking turn of events, {userName} has left {server}.\nThe show must go on!\n\n{kickStatus}\n\uD83D\uDD52 Left: {leaveTime}",color:"#4B0082",footer:"Until we meet again in another lobby"},{title:"\uD83C\uDFC3‍♂️ {userName} speed-ran their exit",description:"Well, that was quick! {userName} decided to bounce from {server}.\nNo hard feelings... probably.\n\n{kickStatus}\n⏰ Member Since: {user-joinedAt}",color:"#FF8C00",footer:"See ya, wouldn't wanna be ya!"},{title:"\uD83C\uDFAA The circus lost a performer",description:"{userName} has left the madness that is {server}.\nHope they find their chill somewhere else!\n\n{kickStatus}\n\uD83D\uDD52 Left: {leaveTime}",color:"#20B2AA",footer:"Thanks for adding to the chaos!"},{title:"\uD83D\uDCA5 {userName} signed off",description:"Another gamer has left {guild}.\nMay your framerate be high and your ping be low!\n\n{kickStatus}\n⏰ Member Since: {user-joinedAt}",color:"#FF69B4",footer:"GG, no re"},{title:"\uD83C\uDFAE Player {userName} has disconnected",description:"Looks like {userName} found the exit door in {server}.\nHope our brand of chaos was entertaining!\n\n{kickStatus}\n\uD83D\uDD52 Left: {leaveTime}",color:"#32CD32",footer:"Thanks for playing with us!"},{title:"\uD83D\uDE80 {userName} has left orbit",description:"Mission complete! {userName} has successfully escaped {server}.\nSafe travels, space cadet!\n\n{kickStatus}\n⏰ Member Since: {user-joinedAt}",color:"#1E90FF",footer:"Houston, we have a departure"},{title:"\uD83C\uDFAF Target eliminated: {userName}",description:"{userName} has been removed from the game... wait, they left voluntarily.\nWell, that's less dramatic than expected.\n\n{kickStatus}\n\uD83D\uDD52 Left: {leaveTime}",color:"#B22222",footer:"Better luck next server!"}],nekosGif:{enabled:!0,type:"cry"}}},m={user:[{name:"{user}",description:"Mentions the user, e.g., @Username"},{name:"{userName}",description:"The user's name, e.g., Username"},{name:"{userTag}",description:"The user's tag, e.g., Username#1234"},{name:"{userId}",description:"The user's ID, e.g., 123456789012345678"},{name:"{userBanner}",description:"URL of the user's banner (if they have one)"},{name:"{user-createdAt}",description:"The date the user's account was created"},{name:"{UserCreation}",description:'How long ago the user\'s account was created (e.g., "2 years ago")'}],guild:[{name:"{server} / {guild}",description:"The server's name"},{name:"{guildIcon}",description:"URL of the server's icon"},{name:"{memberCount}",description:'Total members with ordinal suffix, e.g., "100th"'},{name:"{memberCountNumeric}",description:'Total members as a number, e.g., "100"'}],time:[{name:"{longTime}",description:'Current date and time, e.g., "June 1, 2024 12:00 PM"'},{name:"{shortTime}",description:'Current time, e.g., "12:00 PM"'}],welcome:[{name:"{joinDate}",description:"The date the user joined"},{name:"{joinTime}",description:"The time the user joined"},{name:"{user-joinedAt}",description:"Full date and time the user joined"}],goodbye:[{name:"{leaveDate}",description:"The date the user left"},{name:"{leaveTime}",description:"The time the user left"},{name:"{kickStatus}",description:"If the user was kicked/banned, shows the status. Otherwise, hidden."}]};function c({isOpen:e,onClose:s,channels:o=[],roles:n=[]}){let i=(0,t.useToast)(),[c,h]=(0,a.useState)(d),[u,g]=(0,a.useState)(!0),[x,b]=(0,a.useState)(!1),[p,j]=(0,a.useState)(0),[y,w]=(0,a.useState)(0),[f,C]=(0,a.useState)(!1),k=o.filter(e=>0===e.type||5===e.type||"GUILD_TEXT"===e.type||"GUILD_ANNOUNCEMENT"===e.type),S=n.filter(e=>"@everyone"!==e.name),v=async()=>{b(!0);try{if(!(await fetch("/api/automation/welcome",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(c)})).ok)throw Error("Failed to save settings");i({title:"Settings Saved",status:"success",duration:3e3}),s()}catch(e){i({title:"Error saving settings",description:e.message,status:"error",duration:5e3})}finally{b(!1)}},D=(e,s)=>{h(o=>({...o,welcome:{...o.welcome,[e]:s}}))},T=(e,s)=>{h(o=>({...o,goodbye:{...o.goodbye,[e]:s}}))},F=(e,s)=>{h(o=>({...o,welcome:{...o.welcome,nekosGif:{...o.welcome.nekosGif,[e]:s}}}))},I=(e,s)=>{h(o=>({...o,goodbye:{...o.goodbye,nekosGif:{...o.goodbye.nekosGif,[e]:s}}}))},N=(e,s)=>{h(o=>({...o,welcome:{...o.welcome,autoRole:{...o.welcome.autoRole,[e]:s}}}))},H=(e,s)=>{h(o=>({...o,welcome:{...o.welcome,autoRole:{...o.welcome.autoRole,retry:{...o.welcome.autoRole.retry,[e]:s}}}}))},A=e=>{if(c.welcome.messages.length<=1)return void i({title:"Cannot delete",description:"You must have at least one welcome message.",status:"warning",duration:3e3});h(s=>({...s,welcome:{...s.welcome,messages:s.welcome.messages.filter((s,o)=>o!==e)}})),p>=c.welcome.messages.length-1&&j(Math.max(0,p-1))},B=e=>{if(c.goodbye.messages.length<=1)return void i({title:"Cannot delete",description:"You must have at least one goodbye message.",status:"warning",duration:3e3});h(s=>({...s,goodbye:{...s.goodbye,messages:s.goodbye.messages.filter((s,o)=>o!==e)}})),y>=c.goodbye.messages.length-1&&w(Math.max(0,y-1))},z=(e,s,o)=>{h(n=>({...n,welcome:{...n.welcome,messages:n.welcome.messages.map((n,r)=>r===e?{...n,[s]:o}:n)}}))},G=(e,s,o)=>{h(n=>({...n,goodbye:{...n.goodbye,messages:n.goodbye.messages.map((n,r)=>r===e?{...n,[s]:o}:n)}}))},W=e=>{let s={...c.welcome.messages[e]};s.title=`${s.title} (Copy)`,h(e=>({...e,welcome:{...e.welcome,messages:[...e.welcome.messages,s]}})),j(c.welcome.messages.length)},R=e=>{let s={...c.goodbye.messages[e]};s.title=`${s.title} (Copy)`,h(e=>({...e,goodbye:{...e.goodbye,messages:[...e.goodbye.messages,s]}})),w(c.goodbye.messages.length)};return(0,r.jsxs)(t.Modal,{isOpen:e,onClose:s,size:"6xl",scrollBehavior:"inside",children:[(0,r.jsx)(t.ModalOverlay,{}),(0,r.jsxs)(t.ModalContent,{maxH:"90vh",children:[(0,r.jsx)(t.ModalHeader,{children:(0,r.jsxs)(t.HStack,{children:[(0,r.jsx)(t.Icon,{as:l.mEP}),(0,r.jsx)(t.Text,{children:"Advanced Welcome & Goodbye System"})]})}),(0,r.jsx)(t.ModalCloseButton,{}),(0,r.jsx)(t.ModalBody,{pb:6,children:u?(0,r.jsxs)(t.VStack,{justify:"center",h:"400px",children:[(0,r.jsx)(t.Spinner,{size:"xl"}),(0,r.jsx)(t.Text,{children:"Loading Settings..."})]}):(0,r.jsxs)(t.Tabs,{isFitted:!0,variant:"enclosed",colorScheme:"blue",children:[(0,r.jsxs)(t.TabList,{children:[(0,r.jsxs)(t.Tab,{children:[(0,r.jsx)(t.Icon,{as:l.mEP,mr:2})," Welcome Messages"]}),(0,r.jsxs)(t.Tab,{children:[(0,r.jsx)(t.Icon,{as:l.QeK,mr:2})," Goodbye Messages"]}),(0,r.jsxs)(t.Tab,{children:[(0,r.jsx)(t.Icon,{as:l.VSk,mr:2})," Placeholders"]})]}),(0,r.jsxs)(t.TabPanels,{children:[(0,r.jsx)(t.TabPanel,{children:(0,r.jsxs)(t.VStack,{spacing:6,align:"stretch",children:[(0,r.jsxs)(t.Card,{children:[(0,r.jsx)(t.CardHeader,{children:(0,r.jsxs)(t.HStack,{justify:"space-between",children:[(0,r.jsx)(t.Heading,{size:"md",children:"Welcome System"}),(0,r.jsx)(t.Switch,{size:"lg",isChecked:c.welcome.enabled,onChange:e=>D("enabled",e.target.checked)})]})}),(0,r.jsx)(t.CardBody,{children:(0,r.jsx)(t.VStack,{spacing:4,align:"stretch",children:(0,r.jsxs)(t.FormControl,{isDisabled:!c.welcome.enabled,children:[(0,r.jsx)(t.FormLabel,{children:(0,r.jsxs)(t.HStack,{children:[(0,r.jsx)(t.Icon,{as:l.Qz2}),(0,r.jsx)(t.Text,{children:"Welcome Channel"})]})}),(0,r.jsx)(t.Select,{placeholder:"Select a channel",value:c.welcome.channelId||"",onChange:e=>D("channelId",e.target.value),children:k.map(e=>(0,r.jsxs)("option",{value:e.id,children:["#",e.name]},e.id))}),0===k.length&&(0,r.jsx)(t.Text,{fontSize:"sm",color:"gray.500",mt:1,children:"No text channels found. Make sure the bot has permission to view channels."})]})})})]}),(0,r.jsxs)(t.Card,{opacity:c.welcome.enabled?1:.6,children:[(0,r.jsx)(t.CardHeader,{children:(0,r.jsxs)(t.HStack,{justify:"space-between",children:[(0,r.jsxs)(t.VStack,{align:"start",spacing:1,children:[(0,r.jsx)(t.Heading,{size:"md",children:"Welcome Message Templates"}),(0,r.jsx)(t.Text,{fontSize:"sm",color:"gray.500",children:"Create multiple templates - one will be randomly selected for each new member"})]}),(0,r.jsxs)(t.HStack,{children:[(0,r.jsxs)(t.Badge,{colorScheme:"green",variant:"subtle",children:[c.welcome.messages.length," template",1!==c.welcome.messages.length?"s":""]}),(0,r.jsx)(t.Button,{leftIcon:(0,r.jsx)(l.GGD,{}),colorScheme:"blue",size:"sm",onClick:()=>{let e={title:"\uD83C\uDFAE Welcome to {guild}, {userName}!",description:"Welcome to our server! We're glad to have you here.\n\n\uD83D\uDCC5 Account Created: {UserCreation}\n\uD83C\uDFAF Join Date: {joinDate}",color:"#00FF00",footer:"You're our {memberCount} member!"};h(s=>({...s,welcome:{...s.welcome,messages:[...s.welcome.messages,e]}})),j(c.welcome.messages.length)},isDisabled:!c.welcome.enabled,children:"Add Template"})]})]})}),(0,r.jsx)(t.CardBody,{children:(0,r.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[(0,r.jsx)(t.HStack,{spacing:2,wrap:"wrap",children:c.welcome.messages.map((e,s)=>(0,r.jsxs)(t.Button,{size:"sm",variant:p===s?"solid":"outline",colorScheme:"blue",onClick:()=>j(s),isDisabled:!c.welcome.enabled,children:["Template ",s+1]},s))}),c.welcome.messages[p]&&(0,r.jsx)(t.Box,{p:4,borderWidth:1,borderRadius:"md",bg:"gray.50",_dark:{bg:"gray.800"},children:(0,r.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[(0,r.jsxs)(t.HStack,{justify:"space-between",children:[(0,r.jsxs)(t.Text,{fontWeight:"bold",children:["Template ",p+1]}),(0,r.jsxs)(t.HStack,{children:[(0,r.jsx)(t.Tooltip,{label:"Duplicate Template",children:(0,r.jsx)(t.IconButton,{"aria-label":"Duplicate template",icon:(0,r.jsx)(l.nxz,{}),size:"sm",variant:"ghost",onClick:()=>W(p),isDisabled:!c.welcome.enabled})}),(0,r.jsx)(t.Tooltip,{label:"Delete Template",children:(0,r.jsx)(t.IconButton,{"aria-label":"Delete template",icon:(0,r.jsx)(l.IXo,{}),size:"sm",variant:"ghost",colorScheme:"red",onClick:()=>A(p),isDisabled:!c.welcome.enabled||c.welcome.messages.length<=1})})]})]}),(0,r.jsxs)(t.FormControl,{isDisabled:!c.welcome.enabled,children:[(0,r.jsx)(t.FormLabel,{children:"Embed Title"}),(0,r.jsx)(t.Input,{value:c.welcome.messages[p]?.title||"",onChange:e=>z(p,"title",e.target.value),placeholder:"e.g., \uD83C\uDFAE Welcome to {guild}, {userName}!"})]}),(0,r.jsxs)(t.FormControl,{isDisabled:!c.welcome.enabled,children:[(0,r.jsx)(t.FormLabel,{children:"Embed Description"}),(0,r.jsx)(t.Textarea,{value:c.welcome.messages[p]?.description||"",onChange:e=>z(p,"description",e.target.value),placeholder:"Enter your welcome message description...",rows:6})]}),(0,r.jsxs)(t.HStack,{spacing:4,children:[(0,r.jsxs)(t.FormControl,{isDisabled:!c.welcome.enabled,children:[(0,r.jsx)(t.FormLabel,{children:"Embed Color"}),(0,r.jsx)(t.Input,{type:"color",value:c.welcome.messages[p]?.color||"#00FF00",onChange:e=>z(p,"color",e.target.value),w:"100px"})]}),(0,r.jsxs)(t.FormControl,{isDisabled:!c.welcome.enabled,children:[(0,r.jsx)(t.FormLabel,{children:"Embed Footer"}),(0,r.jsx)(t.Input,{value:c.welcome.messages[p]?.footer||"",onChange:e=>z(p,"footer",e.target.value),placeholder:"e.g., You're our {memberCount} member!"})]})]})]})})]})})]}),(0,r.jsxs)(t.Card,{opacity:c.welcome.enabled?1:.6,children:[(0,r.jsx)(t.CardHeader,{children:(0,r.jsxs)(t.HStack,{justify:"space-between",children:[(0,r.jsxs)(t.VStack,{align:"start",spacing:1,children:[(0,r.jsx)(t.Heading,{size:"md",children:"Auto-Role Assignment"}),(0,r.jsx)(t.Text,{fontSize:"sm",color:"gray.500",children:"Automatically assign roles to new members when they join"})]}),(0,r.jsx)(t.Switch,{isChecked:c.welcome.autoRole.enabled,onChange:e=>N("enabled",e.target.checked),isDisabled:!c.welcome.enabled})]})}),(0,r.jsx)(t.CardBody,{children:(0,r.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[(0,r.jsxs)(t.FormControl,{isDisabled:!c.welcome.enabled||!c.welcome.autoRole.enabled,children:[(0,r.jsx)(t.FormLabel,{children:"Roles to Assign"}),(0,r.jsx)(t.Box,{p:3,borderWidth:1,borderRadius:"md",maxH:"200px",overflowY:"auto",children:(0,r.jsx)(t.CheckboxGroup,{value:c.welcome.autoRole.roleIds,onChange:e=>N("roleIds",e),children:(0,r.jsx)(t.SimpleGrid,{columns:{base:1,md:2},spacing:2,children:S.map(e=>(0,r.jsx)(t.Checkbox,{value:e.id,children:(0,r.jsxs)(t.HStack,{children:[(0,r.jsx)(t.Box,{w:3,h:3,borderRadius:"full",bg:`#${e.color.toString(16).padStart(6,"0")}`}),(0,r.jsx)(t.Text,{children:e.name})]})},e.id))})})}),0===S.length&&(0,r.jsx)(t.Text,{fontSize:"sm",color:"gray.500",mt:1,children:"No manageable roles found. Make sure the bot has permission to manage roles."})]}),(0,r.jsx)(t.Accordion,{allowToggle:!0,children:(0,r.jsxs)(t.AccordionItem,{children:[(0,r.jsxs)(t.AccordionButton,{children:[(0,r.jsx)(t.Box,{flex:"1",textAlign:"left",children:(0,r.jsx)(t.Text,{fontWeight:"medium",children:"Advanced Auto-Role Settings"})}),(0,r.jsx)(t.AccordionIcon,{})]}),(0,r.jsx)(t.AccordionPanel,{pb:4,children:(0,r.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[(0,r.jsxs)(t.FormControl,{isDisabled:!c.welcome.enabled||!c.welcome.autoRole.enabled,children:[(0,r.jsx)(t.FormLabel,{children:"Assignment Delay (milliseconds)"}),(0,r.jsxs)(t.NumberInput,{value:c.welcome.autoRole.delay,onChange:(e,s)=>N("delay",s),min:0,max:6e4,step:100,children:[(0,r.jsx)(t.NumberInputField,{}),(0,r.jsxs)(t.NumberInputStepper,{children:[(0,r.jsx)(t.NumberIncrementStepper,{}),(0,r.jsx)(t.NumberDecrementStepper,{})]})]}),(0,r.jsx)(t.Text,{fontSize:"xs",color:"gray.500",mt:1,children:"Delay before assigning roles (useful if other bots need to process first)"})]}),(0,r.jsxs)(t.FormControl,{display:"flex",alignItems:"center",isDisabled:!c.welcome.enabled||!c.welcome.autoRole.enabled,children:[(0,r.jsx)(t.FormLabel,{htmlFor:"retry-enabled",mb:"0",children:"Enable Retry on Failure"}),(0,r.jsx)(t.Switch,{id:"retry-enabled",isChecked:c.welcome.autoRole.retry.enabled,onChange:e=>H("enabled",e.target.checked)})]}),c.welcome.autoRole.retry.enabled&&(0,r.jsxs)(t.HStack,{spacing:4,children:[(0,r.jsxs)(t.FormControl,{isDisabled:!c.welcome.enabled||!c.welcome.autoRole.enabled,children:[(0,r.jsx)(t.FormLabel,{children:"Max Retry Attempts"}),(0,r.jsxs)(t.NumberInput,{value:c.welcome.autoRole.retry.maxAttempts,onChange:(e,s)=>H("maxAttempts",s),min:1,max:10,children:[(0,r.jsx)(t.NumberInputField,{}),(0,r.jsxs)(t.NumberInputStepper,{children:[(0,r.jsx)(t.NumberIncrementStepper,{}),(0,r.jsx)(t.NumberDecrementStepper,{})]})]})]}),(0,r.jsxs)(t.FormControl,{isDisabled:!c.welcome.enabled||!c.welcome.autoRole.enabled,children:[(0,r.jsx)(t.FormLabel,{children:"Retry Delay (ms)"}),(0,r.jsxs)(t.NumberInput,{value:c.welcome.autoRole.retry.delayBetweenAttempts,onChange:(e,s)=>H("delayBetweenAttempts",s),min:1e3,max:3e4,step:1e3,children:[(0,r.jsx)(t.NumberInputField,{}),(0,r.jsxs)(t.NumberInputStepper,{children:[(0,r.jsx)(t.NumberIncrementStepper,{}),(0,r.jsx)(t.NumberDecrementStepper,{})]})]})]})]})]})})]})})]})})]}),(0,r.jsxs)(t.Card,{opacity:c.welcome.enabled?1:.6,children:[(0,r.jsx)(t.CardHeader,{children:(0,r.jsxs)(t.HStack,{justify:"space-between",children:[(0,r.jsxs)(t.VStack,{align:"start",spacing:1,children:[(0,r.jsx)(t.Heading,{size:"md",children:"Random GIF Images"}),(0,r.jsx)(t.Text,{fontSize:"sm",color:"gray.500",children:"Show random anime GIFs for users without Nitro banners (powered by Nekos.best)"})]}),(0,r.jsx)(t.Switch,{isChecked:c.welcome.nekosGif?.enabled||!1,onChange:e=>F("enabled",e.target.checked),isDisabled:!c.welcome.enabled})]})}),(0,r.jsx)(t.CardBody,{children:(0,r.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[(0,r.jsxs)(t.FormControl,{isDisabled:!c.welcome.enabled||!c.welcome.nekosGif?.enabled,children:[(0,r.jsx)(t.FormLabel,{children:"GIF Type"}),(0,r.jsxs)(t.Select,{value:c.welcome.nekosGif?.type||"wave",onChange:e=>F("type",e.target.value),children:[(0,r.jsx)("option",{value:"wave",children:"\uD83D\uDC4B Wave (Greeting)"}),(0,r.jsx)("option",{value:"hug",children:"\uD83E\uDD17 Hug (Welcoming)"}),(0,r.jsx)("option",{value:"pat",children:"\uD83D\uDC4B Pat (Friendly)"}),(0,r.jsx)("option",{value:"happy",children:"\uD83D\uDE0A Happy (Cheerful)"}),(0,r.jsx)("option",{value:"dance",children:"\uD83D\uDC83 Dance (Celebration)"}),(0,r.jsx)("option",{value:"thumbsup",children:"\uD83D\uDC4D Thumbs Up (Approval)"})]}),(0,r.jsx)(t.Text,{fontSize:"xs",color:"gray.500",mt:1,children:"Choose the type of GIF to show when users don't have a custom banner"})]}),(0,r.jsxs)(t.Alert,{status:"info",size:"sm",children:[(0,r.jsx)(t.AlertIcon,{}),(0,r.jsx)(t.Box,{children:(0,r.jsx)(t.AlertDescription,{fontSize:"sm",children:"GIFs are only shown for users without Nitro banners. Users with custom banners will display their banner instead."})})]})]})})]})]})}),(0,r.jsx)(t.TabPanel,{children:(0,r.jsxs)(t.VStack,{spacing:6,align:"stretch",children:[(0,r.jsxs)(t.Card,{children:[(0,r.jsx)(t.CardHeader,{children:(0,r.jsxs)(t.HStack,{justify:"space-between",children:[(0,r.jsx)(t.Heading,{size:"md",children:"Goodbye System"}),(0,r.jsx)(t.Switch,{size:"lg",isChecked:c.goodbye.enabled,onChange:e=>T("enabled",e.target.checked)})]})}),(0,r.jsx)(t.CardBody,{children:(0,r.jsx)(t.VStack,{spacing:4,align:"stretch",children:(0,r.jsxs)(t.FormControl,{isDisabled:!c.goodbye.enabled,children:[(0,r.jsx)(t.FormLabel,{children:(0,r.jsxs)(t.HStack,{children:[(0,r.jsx)(t.Icon,{as:l.Qz2}),(0,r.jsx)(t.Text,{children:"Goodbye Channel"})]})}),(0,r.jsx)(t.Select,{placeholder:"Select a channel",value:c.goodbye.channelId||"",onChange:e=>T("channelId",e.target.value),children:k.map(e=>(0,r.jsxs)("option",{value:e.id,children:["#",e.name]},e.id))}),0===k.length&&(0,r.jsx)(t.Text,{fontSize:"sm",color:"gray.500",mt:1,children:"No text channels found. Make sure the bot has permission to view channels."})]})})})]}),(0,r.jsxs)(t.Card,{opacity:c.goodbye.enabled?1:.6,children:[(0,r.jsx)(t.CardHeader,{children:(0,r.jsxs)(t.HStack,{justify:"space-between",children:[(0,r.jsxs)(t.VStack,{align:"start",spacing:1,children:[(0,r.jsx)(t.Heading,{size:"md",children:"Goodbye Message Templates"}),(0,r.jsx)(t.Text,{fontSize:"sm",color:"gray.500",children:"Create multiple templates - one will be randomly selected for each departing member"})]}),(0,r.jsxs)(t.HStack,{children:[(0,r.jsxs)(t.Badge,{colorScheme:"red",variant:"subtle",children:[c.goodbye.messages.length," template",1!==c.goodbye.messages.length?"s":""]}),(0,r.jsx)(t.Button,{leftIcon:(0,r.jsx)(l.GGD,{}),colorScheme:"red",size:"sm",onClick:()=>{let e={title:"\uD83D\uDC4B Goodbye {userName}",description:"Thanks for being part of {guild}! We'll miss you.\n\n{kickStatus}\n⏰ Member Since: {user-joinedAt}",color:"#FF0000",footer:"Safe travels!"};h(s=>({...s,goodbye:{...s.goodbye,messages:[...s.goodbye.messages,e]}})),w(c.goodbye.messages.length)},isDisabled:!c.goodbye.enabled,children:"Add Template"})]})]})}),(0,r.jsx)(t.CardBody,{children:(0,r.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[(0,r.jsx)(t.HStack,{spacing:2,wrap:"wrap",children:c.goodbye.messages.map((e,s)=>(0,r.jsxs)(t.Button,{size:"sm",variant:y===s?"solid":"outline",colorScheme:"red",onClick:()=>w(s),isDisabled:!c.goodbye.enabled,children:["Template ",s+1]},s))}),c.goodbye.messages[y]&&(0,r.jsx)(t.Box,{p:4,borderWidth:1,borderRadius:"md",bg:"gray.50",_dark:{bg:"gray.800"},children:(0,r.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[(0,r.jsxs)(t.HStack,{justify:"space-between",children:[(0,r.jsxs)(t.Text,{fontWeight:"bold",children:["Template ",y+1]}),(0,r.jsxs)(t.HStack,{children:[(0,r.jsx)(t.Tooltip,{label:"Duplicate Template",children:(0,r.jsx)(t.IconButton,{"aria-label":"Duplicate template",icon:(0,r.jsx)(l.nxz,{}),size:"sm",variant:"ghost",onClick:()=>R(y),isDisabled:!c.goodbye.enabled})}),(0,r.jsx)(t.Tooltip,{label:"Delete Template",children:(0,r.jsx)(t.IconButton,{"aria-label":"Delete template",icon:(0,r.jsx)(l.IXo,{}),size:"sm",variant:"ghost",colorScheme:"red",onClick:()=>B(y),isDisabled:!c.goodbye.enabled||c.goodbye.messages.length<=1})})]})]}),(0,r.jsxs)(t.FormControl,{isDisabled:!c.goodbye.enabled,children:[(0,r.jsx)(t.FormLabel,{children:"Embed Title"}),(0,r.jsx)(t.Input,{value:c.goodbye.messages[y]?.title||"",onChange:e=>G(y,"title",e.target.value),placeholder:"e.g., \uD83D\uDC4B Goodbye {userName}"})]}),(0,r.jsxs)(t.FormControl,{isDisabled:!c.goodbye.enabled,children:[(0,r.jsx)(t.FormLabel,{children:"Embed Description"}),(0,r.jsx)(t.Textarea,{value:c.goodbye.messages[y]?.description||"",onChange:e=>G(y,"description",e.target.value),placeholder:"Enter your goodbye message description...",rows:6})]}),(0,r.jsxs)(t.HStack,{spacing:4,children:[(0,r.jsxs)(t.FormControl,{isDisabled:!c.goodbye.enabled,children:[(0,r.jsx)(t.FormLabel,{children:"Embed Color"}),(0,r.jsx)(t.Input,{type:"color",value:c.goodbye.messages[y]?.color||"#FF0000",onChange:e=>G(y,"color",e.target.value),w:"100px"})]}),(0,r.jsxs)(t.FormControl,{isDisabled:!c.goodbye.enabled,children:[(0,r.jsx)(t.FormLabel,{children:"Embed Footer"}),(0,r.jsx)(t.Input,{value:c.goodbye.messages[y]?.footer||"",onChange:e=>G(y,"footer",e.target.value),placeholder:"e.g., Safe travels!"})]})]})]})})]})})]}),(0,r.jsxs)(t.Card,{opacity:c.goodbye.enabled?1:.6,children:[(0,r.jsx)(t.CardHeader,{children:(0,r.jsxs)(t.HStack,{justify:"space-between",children:[(0,r.jsxs)(t.VStack,{align:"start",spacing:1,children:[(0,r.jsx)(t.Heading,{size:"md",children:"Random GIF Images"}),(0,r.jsx)(t.Text,{fontSize:"sm",color:"gray.500",children:"Show random anime GIFs for users without Nitro banners (powered by Nekos.best)"})]}),(0,r.jsx)(t.Switch,{isChecked:c.goodbye.nekosGif?.enabled||!1,onChange:e=>I("enabled",e.target.checked),isDisabled:!c.goodbye.enabled})]})}),(0,r.jsx)(t.CardBody,{children:(0,r.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[(0,r.jsxs)(t.FormControl,{isDisabled:!c.goodbye.enabled||!c.goodbye.nekosGif?.enabled,children:[(0,r.jsx)(t.FormLabel,{children:"GIF Type"}),(0,r.jsxs)(t.Select,{value:c.goodbye.nekosGif?.type||"cry",onChange:e=>I("type",e.target.value),children:[(0,r.jsx)("option",{value:"cry",children:"\uD83D\uDE22 Cry (Sad Goodbye)"}),(0,r.jsx)("option",{value:"wave",children:"\uD83D\uDC4B Wave (Farewell)"}),(0,r.jsx)("option",{value:"sad",children:"\uD83D\uDE14 Sad (Melancholy)"}),(0,r.jsx)("option",{value:"sleep",children:"\uD83D\uDE34 Sleep (Peaceful)"}),(0,r.jsx)("option",{value:"pat",children:"\uD83D\uDC4B Pat (Comforting)"}),(0,r.jsx)("option",{value:"hug",children:"\uD83E\uDD17 Hug (Supportive)"})]}),(0,r.jsx)(t.Text,{fontSize:"xs",color:"gray.500",mt:1,children:"Choose the type of GIF to show when users don't have a custom banner"})]}),(0,r.jsxs)(t.Alert,{status:"info",size:"sm",children:[(0,r.jsx)(t.AlertIcon,{}),(0,r.jsx)(t.Box,{children:(0,r.jsx)(t.AlertDescription,{fontSize:"sm",children:"GIFs are only shown for users without Nitro banners. Users with custom banners will display their banner instead."})})]})]})})]})]})}),(0,r.jsx)(t.TabPanel,{children:(0,r.jsxs)(t.VStack,{spacing:6,align:"stretch",children:[(0,r.jsxs)(t.Alert,{status:"info",children:[(0,r.jsx)(t.AlertIcon,{}),(0,r.jsxs)(t.Box,{children:[(0,r.jsx)(t.AlertTitle,{children:"Placeholder Guide"}),(0,r.jsx)(t.AlertDescription,{children:"Use these placeholders in your message templates. They will be automatically replaced with actual values when messages are sent."})]})]}),(0,r.jsxs)(t.SimpleGrid,{columns:{base:1,lg:2},spacing:6,children:[(0,r.jsxs)(t.Card,{children:[(0,r.jsx)(t.CardHeader,{children:(0,r.jsx)(t.Heading,{size:"md",color:"blue.500",children:"\uD83D\uDC64 User Placeholders"})}),(0,r.jsx)(t.CardBody,{children:(0,r.jsx)(t.VStack,{spacing:3,align:"stretch",children:m.user.map((e,s)=>(0,r.jsx)(t.Box,{p:3,borderWidth:1,borderRadius:"md",bg:"blue.50",_dark:{bg:"blue.900"},children:(0,r.jsxs)(t.HStack,{justify:"space-between",children:[(0,r.jsxs)(t.VStack,{align:"start",spacing:1,children:[(0,r.jsx)(t.Text,{fontFamily:"mono",fontWeight:"bold",color:"blue.600",_dark:{color:"blue.300"},children:e.name}),(0,r.jsx)(t.Text,{fontSize:"sm",color:"gray.600",_dark:{color:"gray.400"},children:e.description})]}),(0,r.jsx)(t.IconButton,{"aria-label":"Copy placeholder",icon:(0,r.jsx)(l.nxz,{}),size:"sm",variant:"ghost",onClick:()=>{navigator.clipboard.writeText(e.name),i({title:"Copied!",description:`${e.name} copied to clipboard`,status:"success",duration:2e3})}})]})},s))})})]}),(0,r.jsxs)(t.Card,{children:[(0,r.jsx)(t.CardHeader,{children:(0,r.jsx)(t.Heading,{size:"md",color:"green.500",children:"\uD83C\uDFF0 Server Placeholders"})}),(0,r.jsx)(t.CardBody,{children:(0,r.jsx)(t.VStack,{spacing:3,align:"stretch",children:m.guild.map((e,s)=>(0,r.jsx)(t.Box,{p:3,borderWidth:1,borderRadius:"md",bg:"green.50",_dark:{bg:"green.900"},children:(0,r.jsxs)(t.HStack,{justify:"space-between",children:[(0,r.jsxs)(t.VStack,{align:"start",spacing:1,children:[(0,r.jsx)(t.Text,{fontFamily:"mono",fontWeight:"bold",color:"green.600",_dark:{color:"green.300"},children:e.name}),(0,r.jsx)(t.Text,{fontSize:"sm",color:"gray.600",_dark:{color:"gray.400"},children:e.description})]}),(0,r.jsx)(t.IconButton,{"aria-label":"Copy placeholder",icon:(0,r.jsx)(l.nxz,{}),size:"sm",variant:"ghost",onClick:()=>{navigator.clipboard.writeText(e.name),i({title:"Copied!",description:`${e.name} copied to clipboard`,status:"success",duration:2e3})}})]})},s))})})]}),(0,r.jsxs)(t.Card,{children:[(0,r.jsx)(t.CardHeader,{children:(0,r.jsx)(t.Heading,{size:"md",color:"purple.500",children:"⏰ Time Placeholders"})}),(0,r.jsx)(t.CardBody,{children:(0,r.jsx)(t.VStack,{spacing:3,align:"stretch",children:m.time.map((e,s)=>(0,r.jsx)(t.Box,{p:3,borderWidth:1,borderRadius:"md",bg:"purple.50",_dark:{bg:"purple.900"},children:(0,r.jsxs)(t.HStack,{justify:"space-between",children:[(0,r.jsxs)(t.VStack,{align:"start",spacing:1,children:[(0,r.jsx)(t.Text,{fontFamily:"mono",fontWeight:"bold",color:"purple.600",_dark:{color:"purple.300"},children:e.name}),(0,r.jsx)(t.Text,{fontSize:"sm",color:"gray.600",_dark:{color:"gray.400"},children:e.description})]}),(0,r.jsx)(t.IconButton,{"aria-label":"Copy placeholder",icon:(0,r.jsx)(l.nxz,{}),size:"sm",variant:"ghost",onClick:()=>{navigator.clipboard.writeText(e.name),i({title:"Copied!",description:`${e.name} copied to clipboard`,status:"success",duration:2e3})}})]})},s))})})]}),(0,r.jsxs)(t.Card,{children:[(0,r.jsx)(t.CardHeader,{children:(0,r.jsx)(t.Heading,{size:"md",color:"orange.500",children:"\uD83C\uDF89 Welcome Only"})}),(0,r.jsx)(t.CardBody,{children:(0,r.jsx)(t.VStack,{spacing:3,align:"stretch",children:m.welcome.map((e,s)=>(0,r.jsx)(t.Box,{p:3,borderWidth:1,borderRadius:"md",bg:"orange.50",_dark:{bg:"orange.900"},children:(0,r.jsxs)(t.HStack,{justify:"space-between",children:[(0,r.jsxs)(t.VStack,{align:"start",spacing:1,children:[(0,r.jsx)(t.Text,{fontFamily:"mono",fontWeight:"bold",color:"orange.600",_dark:{color:"orange.300"},children:e.name}),(0,r.jsx)(t.Text,{fontSize:"sm",color:"gray.600",_dark:{color:"gray.400"},children:e.description})]}),(0,r.jsx)(t.IconButton,{"aria-label":"Copy placeholder",icon:(0,r.jsx)(l.nxz,{}),size:"sm",variant:"ghost",onClick:()=>{navigator.clipboard.writeText(e.name),i({title:"Copied!",description:`${e.name} copied to clipboard`,status:"success",duration:2e3})}})]})},s))})})]}),(0,r.jsxs)(t.Card,{children:[(0,r.jsx)(t.CardHeader,{children:(0,r.jsx)(t.Heading,{size:"md",color:"red.500",children:"\uD83D\uDC4B Goodbye Only"})}),(0,r.jsx)(t.CardBody,{children:(0,r.jsx)(t.VStack,{spacing:3,align:"stretch",children:m.goodbye.map((e,s)=>(0,r.jsx)(t.Box,{p:3,borderWidth:1,borderRadius:"md",bg:"red.50",_dark:{bg:"red.900"},children:(0,r.jsxs)(t.HStack,{justify:"space-between",children:[(0,r.jsxs)(t.VStack,{align:"start",spacing:1,children:[(0,r.jsx)(t.Text,{fontFamily:"mono",fontWeight:"bold",color:"red.600",_dark:{color:"red.300"},children:e.name}),(0,r.jsx)(t.Text,{fontSize:"sm",color:"gray.600",_dark:{color:"gray.400"},children:e.description})]}),(0,r.jsx)(t.IconButton,{"aria-label":"Copy placeholder",icon:(0,r.jsx)(l.nxz,{}),size:"sm",variant:"ghost",onClick:()=>{navigator.clipboard.writeText(e.name),i({title:"Copied!",description:`${e.name} copied to clipboard`,status:"success",duration:2e3})}})]})},s))})})]})]})]})})]})]})}),(0,r.jsx)(t.ModalFooter,{children:(0,r.jsxs)(t.HStack,{spacing:3,children:[(0,r.jsx)(t.Button,{variant:"ghost",onClick:s,children:"Cancel"}),(0,r.jsx)(t.Button,{colorScheme:"blue",onClick:v,isLoading:x,isDisabled:u,leftIcon:(0,r.jsx)(l.de5,{}),children:"Save Welcome & Goodbye System"})]})})]})]})}n()}catch(e){n(e)}})}};