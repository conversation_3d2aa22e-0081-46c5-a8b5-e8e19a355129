"use strict";(()=>{var e={};e.id=8679,e.ids=[8679],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2518:e=>{e.exports=require("mongodb")},2634:(e,t,o)=>{o.r(t),o.d(t,{config:()=>P,default:()=>v,routeModule:()=>_});var n={};o.r(n);var s={};o.r(s),o.d(s,{default:()=>A});var i=o(3433),r=o(264),a=o(584),l=o(5806),c=o(8525),d=o(2518);class u{static write(e,t="INFO"){let o=new Date().toISOString(),n=`[${o}] ${t}: ${e}
`;fs.appendFileSync("logs/app.log",n),t.toUpperCase()}static error(e){u.write(e,"ERROR")}static warning(e){u.write(e,"WARNING")}static info(e){u.write(e,"INFO")}}module.exports=u;class m{constructor(e){this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.isConnecting=!1,this.connectionPromise=null,this.config=e;try{this.logger=n.Logger.createLogger()}catch(e){this.logger={info:console.log,error:console.error,warn:console.warn,debug:console.debug}}}async connect(){return this.connectionPromise?this.connectionPromise:this.client&&this.db?Promise.resolve():(this.connectionPromise=this._connect(),this.connectionPromise)}async _connect(){try{this.isConnecting=!0;let e={maxPoolSize:20,minPoolSize:5,maxIdleTimeMS:3e4,connectTimeoutMS:1e4,socketTimeoutMS:45e3,serverSelectionTimeoutMS:1e4,retryWrites:!0,retryReads:!0,compressors:["zlib"],heartbeatFrequencyMS:1e4,...this.config.database.options};this.client=new d.MongoClient(this.config.database.url,e),await this.client.connect(),this.db=this.client.db(this.config.database.name),this.client.on("connectionPoolCreated",()=>{this.logger.debug("MongoDB connection pool created")}),this.client.on("connectionPoolClosed",()=>{this.logger.debug("MongoDB connection pool closed")}),this.client.on("connectionCreated",()=>{this.logger.debug("New MongoDB connection created")}),await this.db.admin().ping(),this.logger.info(`✅ Connected to MongoDB: ${this.config.database.name}`),this.reconnectAttempts=0,await this.createIndexes()}catch(e){if(this.logger.error("Failed to connect to MongoDB:",e),this.reconnectAttempts++,this.reconnectAttempts<this.maxReconnectAttempts)return this.logger.info(`Retrying connection (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`),await new Promise(e=>setTimeout(e,5e3)),this._connect();throw e}finally{this.isConnecting=!1,this.connectionPromise=null}}async disconnect(){this.client&&(await this.client.close(),this.logger.info("Disconnected from MongoDB"))}async ensureConnection(){this.client&&this.db||await this.connect()}async createIndexes(){try{let e=[this.db.collection("command_logs").createIndex({timestamp:-1},{background:!0,name:"timestamp_desc"}),this.db.collection("command_logs").createIndex({commandName:1,timestamp:-1},{background:!0,name:"command_timestamp"}),this.db.collection("command_logs").createIndex({userId:1,timestamp:-1},{background:!0,name:"user_timestamp"}),this.db.collection("error_logs").createIndex({timestamp:-1},{background:!0,name:"error_timestamp_desc"}),this.db.collection("error_logs").createIndex({type:1,timestamp:-1},{background:!0,name:"error_type_timestamp"}),this.db.collection("command_states").createIndex({commandId:1},{unique:!0,background:!0,name:"command_id_unique"}),this.db.collection("bot_status").createIndex({key:1},{unique:!0,background:!0,name:"status_key_unique"}),this.db.collection("guilds").createIndex({guildId:1},{unique:!0,background:!0,name:"guild_id_unique"}),this.db.collection("guild_configs").createIndex({guildId:1},{unique:!0,background:!0,name:"guild_id_unique"}),this.db.collection("users").createIndex({userId:1},{background:!0,name:"user_id"}),this.db.collection("users").createIndex({userId:1,guildId:1},{background:!0,name:"user_guild"})];await Promise.allSettled(e),this.logger.debug("✅ Database indexes created/verified")}catch(e){this.logger.error("Error creating database indexes:",e)}}async findOne(e,t,o){return await this.ensureConnection(),this.db.collection(e).findOne(t,o)}async find(e,t,o){return await this.ensureConnection(),this.db.collection(e).find(t,o).toArray()}async insertOne(e,t,o){return await this.ensureConnection(),this.db.collection(e).insertOne(t,o)}async updateOne(e,t,o,n){return await this.ensureConnection(),this.db.collection(e).updateOne(t,o,n)}async deleteOne(e,t,o){return await this.ensureConnection(),this.db.collection(e).deleteOne(t,o)}async countDocuments(e,t,o){return await this.ensureConnection(),this.db.collection(e).countDocuments(t,o)}getStatus(){return{details:{database:this.config.database.name,connected:null!==this.client&&null!==this.db,reconnectAttempts:this.reconnectAttempts,isConnecting:this.isConnecting,hasClient:!!this.client,hasDatabase:!!this.db}}}async getStats(){await this.ensureConnection();try{let e=await this.db.stats();return{collections:e.collections,documents:e.objects,dataSize:e.dataSize,storageSize:e.storageSize,indexes:e.indexes,indexSize:e.indexSize}}catch(e){throw this.logger.error("Failed to get database stats:",e),e}}async warmupConnections(){await this.ensureConnection();try{let e=Array.from({length:3},()=>this.db.admin().ping());await Promise.all(e),this.logger.debug("Database connection pool warmed up")}catch(e){this.logger.warn("Failed to warm up connection pool:",e)}}}var g=o(2115),h=o.n(g),b=o(9021),f=o.n(b),p=o(3873),y=o.n(p);let w=(0,require("url").fileURLToPath)("file:///D:/Users/<USER>/Desktop/404%20Bot/src/core/ConfigManager.ts"),C=y().dirname(w);class I{static{this.configPath=I.findConfigPath()}static{this.config=null}static findConfigPath(){for(let e of[y().join(process.cwd(),"config.yml"),y().join(process.cwd(),"..","..","config.yml"),y().join(process.cwd(),"..","config.yml"),y().join(C,"..","..","config.yml"),y().join(C,"..","..","..","config.yml")])if(f().existsSync(e))return e;return y().join(process.cwd(),"config.yml")}static load(e){e&&(I.configPath=e);let t=y().resolve(I.configPath);if(!f().existsSync(t))throw Error(`Configuration file not found: ${t}`);try{let e=f().readFileSync(t,"utf8"),o=h().parse(e);return I.validateConfig(o),I.config=o,o}catch(e){if(e instanceof Error)throw Error(`Failed to load configuration: ${e.message}`);throw Error("Failed to load configuration: Unknown error")}}static get(){if(!I.config)throw Error("Configuration not loaded. Call ConfigManager.load() first.");return I.config}static reload(){return I.config=null,I.load()}static validateConfig(e){for(let t of["bot.token","bot.clientId","bot.prefix","logging.level","addons.enabled","addons.directory"])if(!I.getNestedValue(e,t))throw Error(`Missing required configuration field: ${t}`);("string"!=typeof e.bot.token||e.bot.token.length<50)&&process.stdout.write("Warning: Bot token appears to be invalid or placeholder\n"),"string"==typeof e.bot.clientId&&/^\d+$/.test(e.bot.clientId)||process.stdout.write("Warning: Client ID appears to be invalid\n");let t=["error","warn","info","debug"];if(!t.includes(e.logging.level))throw Error(`Invalid logging level: ${e.logging.level}. Must be one of: ${t.join(", ")}`);for(let t of[e.logging.file?.path,e.addons.directory,y().dirname(e.database?.path||"data/bot.db")].filter(Boolean))try{f().existsSync(t)||f().mkdirSync(t,{recursive:!0})}catch(e){process.stdout.write(`Warning: Could not create directory ${t}: ${e}
`)}}static getNestedValue(e,t){return t.split(".").reduce((e,t)=>e?.[t],e)}static watch(e){let t=y().resolve(I.configPath);f().watchFile(t,(t,o)=>{if(t.mtime!==o.mtime)try{let t=I.reload();e(t)}catch(e){process.stderr.write(`Failed to reload configuration: ${e}
`)}})}static getConfig(){if(!this.config){let e=f().readFileSync(this.configPath,"utf8");this.config=h().parse(e)}return this.config}static reloadConfig(){let e=f().readFileSync(this.configPath,"utf8");this.config=h().parse(e)}}var k=o(8580);let S=new m(I.getConfig()),x={welcome:{enabled:!1,channelId:null,messages:[{title:"\uD83C\uDFAE Welcome to {guild}, {userName}!",description:"Pull up a chair and make yourself comfortable. We've got games, chaos, and questionable life choices waiting for you!\n\n\uD83D\uDCC5 Account Created: {UserCreation}\n\uD83C\uDFAF Join Date: {joinDate}",color:"#FF6B35",footer:"You're our {memberCount} member - let's get weird!"},{title:"\uD83D\uDEA8 Error 404: Chill Not Found! \uD83D\uDEA8",description:"Hey {user}, looks like you've stumbled into our chaotic corner of the internet. Hope you brought snacks and a sense of humor!\n\n\uD83C\uDFAE Member #{memberCountNumeric}\n\uD83D\uDCC5 Joined: {joinTime}",color:"#8B0000",footer:"Welcome to the madness!"},{title:"\uD83C\uDFAF Player {userName} has entered the game!",description:"Welcome to {server} where the games are wild and the conversations are... well, let's just say we're not your typical Discord server.\n\n\uD83D\uDCC5 Account Age: {UserCreation}\n\uD83C\uDFAF Member Since: {user-joinedAt}",color:"#1E90FF",footer:"Time to level up your social life!"},{title:"\uD83D\uDD25 New challenger has appeared!",description:"It's {user}! Welcome to our adult gaming sanctuary where we take games seriously but ourselves... not so much.\n\n\uD83C\uDFAE Member #{memberCountNumeric}\n⏰ Joined: {joinTime}",color:"#DC143C",footer:"Ready to game and chill?"},{title:"\uD83C\uDFB2 Welcome to the chaos, {userName}!",description:"You've joined {server} - where good decisions come to die and epic gaming moments are born. Buckle up!\n\n\uD83D\uDCC5 Account Created: {user-createdAt}\n\uD83C\uDFAF Join Time: {joinTime}",color:"#9932CC",footer:"Let the games begin!"}],autoRole:{enabled:!1,roleIds:[],delay:1e3,retry:{enabled:!0,maxAttempts:3,delayBetweenAttempts:5e3}},nekosGif:{enabled:!0,type:"wave"},message:"Welcome {user} to {guild}! You are the {memberCount}th member.",autoRoles:[],embedColor:"#00FF00"},goodbye:{enabled:!1,channelId:null,messages:[{title:"\uD83D\uDC80 Game Over for {userName}",description:"Looks like {userName} has rage quit from {server}.\nThanks for the memories and questionable life choices!\n\n{kickStatus}\n⏰ Member Since: {user-joinedAt}",color:"#8B0000",footer:"Press F to pay respects"},{title:"\uD83D\uDEAA {userName} has left the building",description:"Another one bites the dust! {userName} decided our chaos wasn't for them.\nCan't win 'em all, I guess.\n\n{kickStatus}\n\uD83D\uDD52 Left: {leaveTime}",color:"#696969",footer:"The door's always open... maybe"},{title:"\uD83D\uDCE4 Connection lost: {userName}",description:"{userName} has disconnected from {guild}.\nHope they found what they were looking for!\n\n{kickStatus}\n⏰ Member Since: {user-joinedAt}",color:"#FF6347",footer:"Thanks for gaming with us!"}],nekosGif:{enabled:!0,type:"cry"},message:"Goodbye {user}! We will miss you.",embedColor:"#FF0000"}};async function A(e,t){if(!await (0,l.getServerSession)(e,t,c.authOptions))return t.status(401).json({error:"Unauthorized"});let o=k.dashboardConfig.bot.guildId;if(!o)return t.status(400).json({error:"Guild ID not found in configuration."});try{if(await S.connect(),"GET"===e.method){let e=await S.findOne("guild_configs",{guildId:o}),n={welcome:(e=>{if(!e)return x.welcome;let t={...x.welcome,...e};return e.message&&!e.messages&&(t.messages=[{title:"Welcome to {guild}!",description:e.message,color:e.embedColor||"#00FF00",footer:"Welcome to our server!"}]),e.autoRoles&&!e.autoRole&&(t.autoRole={...x.welcome.autoRole,roleIds:e.autoRoles}),t})(e?.welcome),goodbye:(e=>{if(!e)return x.goodbye;let t={...x.goodbye,...e};return e.message&&!e.messages&&(t.messages=[{title:"Goodbye!",description:e.message,color:e.embedColor||"#FF0000",footer:"Thanks for being part of our server!"}]),t})(e?.goodbye)};return t.status(200).json(n)}if("POST"===e.method){let{welcome:n,goodbye:s}=e.body;if("boolean"!=typeof n?.enabled||"boolean"!=typeof s?.enabled)return t.status(400).json({error:"Invalid data format."});if(n.enabled&&(!n.messages||!Array.isArray(n.messages)||0===n.messages.length))return t.status(400).json({error:"Welcome messages are required when welcome system is enabled."});if(s.enabled&&(!s.messages||!Array.isArray(s.messages)||0===s.messages.length))return t.status(400).json({error:"Goodbye messages are required when goodbye system is enabled."});let i=(e,t)=>{if(!e.description)throw Error(`${t} message template must have a description.`);if(e.color&&!/^#[0-9A-Fa-f]{6}$/.test(e.color))throw Error(`${t} message template color must be a valid hex color.`)};try{n.enabled&&n.messages.forEach((e,t)=>{i(e,`Welcome template ${t+1}`)}),s.enabled&&s.messages.forEach((e,t)=>{i(e,`Goodbye template ${t+1}`)})}catch(e){return t.status(400).json({error:e.message})}let r={guildId:o,welcome:{enabled:n.enabled,channelId:n.channelId||null,messages:n.messages||x.welcome.messages,autoRole:{enabled:n.autoRole?.enabled||!1,roleIds:n.autoRole?.roleIds||[],delay:n.autoRole?.delay||1e3,retry:{enabled:n.autoRole?.retry?.enabled||!0,maxAttempts:n.autoRole?.retry?.maxAttempts||3,delayBetweenAttempts:n.autoRole?.retry?.delayBetweenAttempts||5e3}},nekosGif:{enabled:n.nekosGif?.enabled||!0,type:n.nekosGif?.type||"wave"},message:n.messages?.[0]?.description||x.welcome.message,autoRoles:n.autoRole?.roleIds||[],embedColor:n.messages?.[0]?.color||x.welcome.embedColor},goodbye:{enabled:s.enabled,channelId:s.channelId||null,messages:s.messages||x.goodbye.messages,nekosGif:{enabled:s.nekosGif?.enabled||!0,type:s.nekosGif?.type||"cry"},message:s.messages?.[0]?.description||x.goodbye.message,embedColor:s.messages?.[0]?.color||x.goodbye.embedColor}};return await S.updateOne("guild_configs",{guildId:o},{$set:r},{upsert:!0}),t.status(200).json({message:"Welcome & Goodbye system updated successfully.",welcomeTemplates:n.messages?.length||0,goodbyeTemplates:s.messages?.length||0})}return t.setHeader("Allow",["GET","POST"]),t.status(405).end(`Method ${e.method} Not Allowed`)}catch(e){return t.status(500).json({error:"Internal Server Error"})}}let v=(0,a.M)(s,"default"),P=(0,a.M)(s,"config"),_=new i.PagesAPIRouteModule({definition:{kind:r.A.PAGES_API,page:"/api/automation/welcome",pathname:"/api/automation/welcome",bundlePath:"",filename:""},userland:s})},3433:(e,t,o)=>{e.exports=o(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8525:(e,t,o)=>{o.r(t),o.d(t,{authOptions:()=>l,default:()=>c});var n=o(5542),s=o.n(n);let i=require("next-auth/providers/discord");var r=o.n(i),a=o(8580);let l={providers:[r()({clientId:a.dashboardConfig.bot.clientId,clientSecret:a.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:o})=>(t&&o&&(e.accessToken=t.access_token||null,e.id=o.id||null),e),async session({session:e,token:t}){if(e?.user){let o=t.id||null,n=t.accessToken||null;e.user.id=o,e.user.accessToken=n;let s=!1;if(o)if((a.dashboardConfig.dashboard.admins||[]).includes(o))s=!0;else{let e=a.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&a.dashboardConfig.bot.token&&a.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${a.dashboardConfig.bot.guildId}/members/${o}`,{headers:{Authorization:`Bot ${a.dashboardConfig.bot.token}`}});if(t.ok){let o=await t.json();s=e.some(e=>o.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=s,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let o=new URL(t),n=`${o.protocol}//localhost${o.port?`:${o.port}`:""}`;return e.startsWith(t)||e.startsWith(n)?e:t}},secret:a.dashboardConfig.dashboard.session.secret||a.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},c=s()(l)},8580:(e,t,o)=>{o.r(t),o.d(t,{dashboardConfig:()=>l,default:()=>c});var n=o(9021),s=o(2115),i=o.n(s),r=o(3873);let a={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>r.resolve(process.cwd(),e)).find(e=>n.existsSync(e));if(!e){let t=r.resolve(__dirname,"../../../config.yml");n.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=n.readFileSync(e,"utf8");a=i().parse(t)}catch(e){process.exit(1)}let l={bot:{token:a.bot.token,clientId:a.bot.clientId,clientSecret:a.bot.clientSecret,guildId:a.bot.guildId,ticketCategoryId:a.bot.ticketCategoryId||null,ticketLogChannelId:a.bot.ticketLogChannelId||null,prefix:a.bot.prefix},dashboard:{admins:a.dashboard?.admins||[],adminRoleIds:a.dashboard?.adminRoleIds||[],session:{secret:a.dashboard?.session?.secret||a.bot.clientSecret}},database:{url:a.database.url,name:a.database.name,options:{maxPoolSize:a.database.options?.maxPoolSize||10,minPoolSize:a.database.options?.minPoolSize||1,maxIdleTimeMS:a.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:a.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:a.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:a.database.options?.connectTimeoutMS||1e4,retryWrites:a.database.options?.retryWrites!==!1,retryReads:a.database.options?.retryReads!==!1}}};l.bot.token||process.exit(1),l.bot.clientId&&l.bot.clientSecret||process.exit(1),l.bot.guildId||process.exit(1),l.database.url&&l.database.name||process.exit(1);let c=l},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var o=t(t.s=2634);module.exports=o})();