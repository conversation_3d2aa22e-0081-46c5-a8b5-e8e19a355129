(()=>{var e={};e.id=5449,e.ids=[5449],e.modules={26:(e,t,r)=>{e.exports=r(5947).isPlainObject},123:(e,t,r)=>{"use strict";r.d(t,{CU:()=>s,Lx:()=>c,u3:()=>u});var n=r(2481),i=r(7555),a=(0,n.Z0)({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push((0,i.h4)(t.payload))},removeLegendPayload(e,t){var r=(0,i.ss)(e).payload.indexOf((0,i.h4)(t.payload));r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:o,setLegendSettings:l,addLegendPayload:c,removeLegendPayload:u}=a.actions,s=a.reducer},192:(e,t,r)=>{"use strict";r.d(t,{mZ:()=>l,vE:()=>o});var n=r(2481),i={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},a=(0,n.Z0)({name:"rootProps",initialState:i,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=null!=(r=t.payload.barGap)?r:i.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),o=a.reducer,{updateOptions:l}=a.actions},344:(e,t,r)=>{"use strict";r.d(t,{J:()=>a,U:()=>i});var n=(0,r(2481).Z0)({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:i}=n.actions,a=n.reducer},360:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(874);t.isArrayLike=function(e){return null!=e&&"function"!=typeof e&&n.isLength(e.length)}},361:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},382:(e,t,r)=>{"use strict";r.d(t,{Be:()=>v,Cv:()=>O,D0:()=>S,Gl:()=>g,Dc:()=>j});var n=r(7242),i=r(5336),a=r(3401),o=r(8702),l=r(4504),c={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},u={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},s=r(639),f=r(4397),d={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:c.angleAxisId,includeHidden:!1,name:void 0,reversed:c.reversed,scale:c.scale,tick:c.tick,tickCount:void 0,ticks:void 0,type:c.type,unit:void 0},h={allowDataOverflow:u.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:u.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:u.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:u.scale,tick:u.tick,tickCount:u.tickCount,ticks:void 0,type:u.type,unit:void 0},p={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:c.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:c.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:c.scale,tick:c.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},y={allowDataOverflow:u.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:u.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:u.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:u.scale,tick:u.tick,tickCount:u.tickCount,ticks:void 0,type:"category",unit:void 0},v=(e,t)=>null!=e.polarAxis.angleAxis[t]?e.polarAxis.angleAxis[t]:"radial"===e.layout.layoutType?p:d,g=(e,t)=>null!=e.polarAxis.radiusAxis[t]?e.polarAxis.radiusAxis[t]:"radial"===e.layout.layoutType?y:h,m=e=>e.polarOptions,b=(0,n.Mz)([i.Lp,i.A$,a.HZ],o.lY),x=(0,n.Mz)([m,b],(e,t)=>{if(null!=e)return(0,l.F4)(e.innerRadius,t,0)}),w=(0,n.Mz)([m,b],(e,t)=>{if(null!=e)return(0,l.F4)(e.outerRadius,t,.8*t)}),O=(0,n.Mz)([m],e=>{if(null==e)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]});(0,n.Mz)([v,O],s.I);var j=(0,n.Mz)([b,x,w],(e,t,r)=>{if(null!=e&&null!=t&&null!=r)return[t,r]});(0,n.Mz)([g,j],s.I);var S=(0,n.Mz)([f.fz,m,x,w,i.Lp,i.A$],(e,t,r,n,i,a)=>{if(("centric"===e||"radial"===e)&&null!=t&&null!=r&&null!=n){var{cx:o,cy:c,startAngle:u,endAngle:s}=t;return{cx:(0,l.F4)(o,i,i/2),cy:(0,l.F4)(c,a,a/2),innerRadius:r,outerRadius:n,startAngle:u,endAngle:s,clockWise:!1}}})},433:(e,t,r)=>{e.exports=r(4115).throttle},477:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8782),i=r(3296),a=r(4817);t.orderBy=function(e,t,r,o){if(null==e)return[];r=o?void 0:r,Array.isArray(e)||(e=Object.values(e)),Array.isArray(t)||(t=null==t?[null]:[t]),0===t.length&&(t=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map(e=>String(e));let l=(e,t)=>{let r=e;for(let e=0;e<t.length&&null!=r;++e)r=r[t[e]];return r},c=(e,t)=>null==t||null==e?t:"object"==typeof e&&"key"in e?Object.hasOwn(t,e.key)?t[e.key]:l(t,e.path):"function"==typeof e?e(t):Array.isArray(e)?l(t,e):"object"==typeof t?t[e]:t,u=t.map(e=>(Array.isArray(e)&&1===e.length&&(e=e[0]),null==e||"function"==typeof e||Array.isArray(e)||i.isKey(e))?e:{key:e,path:a.toPath(e)});return e.map(e=>({original:e,criteria:u.map(t=>c(t,e))})).slice().sort((e,t)=>{for(let i=0;i<u.length;i++){let a=n.compareValues(e.criteria[i],t.criteria[i],r[i]);if(0!==a)return a}return 0}).map(e=>e.original)}},594:(e,t,r)=>{"use strict";r.d(t,{P:()=>i});var n=r(7463),i=(e,t)=>{var r=null==e?void 0:e.index;if(null==r)return null;var i=Number(r);if(!(0,n.H)(i))return r;var a=Infinity;return t.length>0&&(a=t.length-1),String(Math.max(0,Math.min(i,a)))}},639:(e,t,r)=>{"use strict";r.d(t,{I:()=>n});var n=(e,t)=>{if(e&&t)return null!=e&&e.reversed?[t[1],t[0]]:t}},672:(e,t,r)=>{"use strict";r.d(t,{p:()=>i}),r(2015),r(192);var n=r(9684);function i(e){return(0,n.j)(),null}},753:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let r=/^(?:0|[1-9]\d*)$/;t.isIndex=function(e,t=Number.MAX_SAFE_INTEGER){switch(typeof e){case"number":return Number.isInteger(e)&&e>=0&&e<t;case"symbol":return!1;case"string":return r.test(e)}}},776:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.argumentsTag="[object Arguments]",t.arrayBufferTag="[object ArrayBuffer]",t.arrayTag="[object Array]",t.bigInt64ArrayTag="[object BigInt64Array]",t.bigUint64ArrayTag="[object BigUint64Array]",t.booleanTag="[object Boolean]",t.dataViewTag="[object DataView]",t.dateTag="[object Date]",t.errorTag="[object Error]",t.float32ArrayTag="[object Float32Array]",t.float64ArrayTag="[object Float64Array]",t.functionTag="[object Function]",t.int16ArrayTag="[object Int16Array]",t.int32ArrayTag="[object Int32Array]",t.int8ArrayTag="[object Int8Array]",t.mapTag="[object Map]",t.numberTag="[object Number]",t.objectTag="[object Object]",t.regexpTag="[object RegExp]",t.setTag="[object Set]",t.stringTag="[object String]",t.symbolTag="[object Symbol]",t.uint16ArrayTag="[object Uint16Array]",t.uint32ArrayTag="[object Uint32Array]",t.uint8ArrayTag="[object Uint8Array]",t.uint8ClampedArrayTag="[object Uint8ClampedArray]"},874:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isLength=function(e){return Number.isSafeInteger(e)&&e>=0}},919:(e,t,r)=>{"use strict";r.d(t,{d:()=>C});var n=r(2015),i=r(6852),a=r(4504),o=r(2661),l=r(9058),c=r(1278),u=r(9713),s=r(4397),f=r(3411),d=r(9684),h=r(7580),p=r(4644),y=["x1","y1","x2","y2","key"],v=["offset"],g=["xAxisId","yAxisId"],m=["xAxisId","yAxisId"];function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function x(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function w(){return(w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var j=e=>{var{fill:t}=e;if(!t||"none"===t)return null;var{fillOpacity:r,x:i,y:a,width:o,height:l,ry:c}=e;return n.createElement("rect",{x:i,y:a,ry:c,width:o,height:l,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function S(e,t){var r;if(n.isValidElement(e))r=n.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var{x1:i,y1:a,x2:l,y2:c,key:u}=t,s=O(t,y),f=(0,o.J9)(s,!1),{offset:d}=f,h=O(f,v);r=n.createElement("line",w({},h,{x1:i,y1:a,x2:l,y2:c,fill:"none",key:u}))}return r}function P(e){var{x:t,width:r,horizontal:i=!0,horizontalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:l}=e,c=O(e,g),u=a.map((e,n)=>S(i,x(x({},c),{},{x1:t,y1:e,x2:t+r,y2:e,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},u)}function M(e){var{y:t,height:r,vertical:i=!0,verticalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:l}=e,c=O(e,m),u=a.map((e,n)=>S(i,x(x({},c),{},{x1:e,y1:t,x2:e,y2:t+r,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},u)}function A(e){var{horizontalFill:t,fillOpacity:r,x:i,y:a,width:o,height:l,horizontalPoints:c,horizontal:u=!0}=e;if(!u||!t||!t.length)return null;var s=c.map(e=>Math.round(e+a-a)).sort((e,t)=>e-t);a!==s[0]&&s.unshift(0);var f=s.map((e,c)=>{var u=s[c+1]?s[c+1]-e:a+l-e;if(u<=0)return null;var f=c%t.length;return n.createElement("rect",{key:"react-".concat(c),y:e,x:i,height:u,width:o,stroke:"none",fill:t[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function E(e){var{vertical:t=!0,verticalFill:r,fillOpacity:i,x:a,y:o,width:l,height:c,verticalPoints:u}=e;if(!t||!r||!r.length)return null;var s=u.map(e=>Math.round(e+a-a)).sort((e,t)=>e-t);a!==s[0]&&s.unshift(0);var f=s.map((e,t)=>{var u=s[t+1]?s[t+1]-e:a+l-e;if(u<=0)return null;var f=t%r.length;return n.createElement("rect",{key:"react-".concat(t),x:e,y:o,width:u,height:c,stroke:"none",fill:r[f],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var k=(e,t)=>{var{xAxis:r,width:n,height:i,offset:a}=e;return(0,l.PW)((0,c.f)(x(x(x({},u.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.left,a.left+a.width,t)},_=(e,t)=>{var{yAxis:r,width:n,height:i,offset:a}=e;return(0,l.PW)((0,c.f)(x(x(x({},u.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.top,a.top+a.height,t)},T={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function C(e){var t=(0,s.yi)(),r=(0,s.rY)(),o=(0,s.W7)(),l=x(x({},(0,p.e)(e,T)),{},{x:(0,a.Et)(e.x)?e.x:o.left,y:(0,a.Et)(e.y)?e.y:o.top,width:(0,a.Et)(e.width)?e.width:o.width,height:(0,a.Et)(e.height)?e.height:o.height}),{xAxisId:c,yAxisId:u,x:y,y:v,width:g,height:m,syncWithTicks:b,horizontalValues:O,verticalValues:S}=l,C=(0,h.r)(),D=(0,d.G)(e=>(0,f.ZB)(e,"xAxis",c,C)),N=(0,d.G)(e=>(0,f.ZB)(e,"yAxis",u,C));if(!(0,a.Et)(g)||g<=0||!(0,a.Et)(m)||m<=0||!(0,a.Et)(y)||y!==+y||!(0,a.Et)(v)||v!==+v)return null;var I=l.verticalCoordinatesGenerator||k,z=l.horizontalCoordinatesGenerator||_,{horizontalPoints:L,verticalPoints:R}=l;if((!L||!L.length)&&"function"==typeof z){var $=O&&O.length,B=z({yAxis:N?x(x({},N),{},{ticks:$?O:N.ticks}):void 0,width:t,height:r,offset:o},!!$||b);(0,i.R)(Array.isArray(B),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof B,"]")),Array.isArray(B)&&(L=B)}if((!R||!R.length)&&"function"==typeof I){var F=S&&S.length,U=I({xAxis:D?x(x({},D),{},{ticks:F?S:D.ticks}):void 0,width:t,height:r,offset:o},!!F||b);(0,i.R)(Array.isArray(U),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof U,"]")),Array.isArray(U)&&(R=U)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(j,{fill:l.fill,fillOpacity:l.fillOpacity,x:l.x,y:l.y,width:l.width,height:l.height,ry:l.ry}),n.createElement(A,w({},l,{horizontalPoints:L})),n.createElement(E,w({},l,{verticalPoints:R})),n.createElement(P,w({},l,{offset:o,horizontalPoints:L,xAxis:D,yAxis:N})),n.createElement(M,w({},l,{offset:o,verticalPoints:R,xAxis:D,yAxis:N})))}C.displayName="CartesianGrid"},1053:(e,t,r)=>{"use strict";e.exports=r(8997)},1078:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.noop=function(){}},1094:(e,t,r)=>{"use strict";r.d(t,{E:()=>k});var n=r(2015),i=r(9486),a=r(4504),o=r(4056),l=r(2661),c=r(8050),u=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,s=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,f=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,d=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,h={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},p=Object.keys(h);class y{static parse(e){var t,[,r,n]=null!=(t=d.exec(e))?t:[];return new y(parseFloat(r),null!=n?n:"")}constructor(e,t){this.num=e,this.unit=t,this.num=e,this.unit=t,(0,a.M8)(e)&&(this.unit=""),""===t||f.test(t)||(this.num=NaN,this.unit=""),p.includes(t)&&(this.num=e*h[t],this.unit="px")}add(e){return this.unit!==e.unit?new y(NaN,""):new y(this.num+e.num,this.unit)}subtract(e){return this.unit!==e.unit?new y(NaN,""):new y(this.num-e.num,this.unit)}multiply(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new y(NaN,""):new y(this.num*e.num,this.unit||e.unit)}divide(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new y(NaN,""):new y(this.num/e.num,this.unit||e.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return(0,a.M8)(this.num)}}function v(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,i,a]=null!=(r=u.exec(t))?r:[],o=y.parse(null!=n?n:""),l=y.parse(null!=a?a:""),c="*"===i?o.multiply(l):o.divide(l);if(c.isNaN())return"NaN";t=t.replace(u,c.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var f,[,d,h,p]=null!=(f=s.exec(t))?f:[],v=y.parse(null!=d?d:""),g=y.parse(null!=p?p:""),m="+"===h?v.add(g):v.subtract(g);if(m.isNaN())return"NaN";t=t.replace(s,m.toString())}return t}var g=/\(([^()]*)\)/;function m(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t,r=e;null!=(t=g.exec(r));){var[,n]=t;r=r.replace(g,v(n))}return r}(t),t=v(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var b=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],x=["dx","dy","angle","className","breakAll"];function w(){return(w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var j=/[ \f\n\r\t\v\u2028\u2029]+/,S=e=>{var{children:t,breakAll:r,style:n}=e;try{var i=[];(0,a.uy)(t)||(i=r?t.toString().split(""):t.toString().split(j));var o=i.map(e=>({word:e,width:(0,c.P)(e,n).width})),l=r?0:(0,c.P)("\xa0",n).width;return{wordsWithComputedWidth:o,spaceWidth:l}}catch(e){return null}},P=(e,t,r,n,i)=>{var o,{maxLines:l,children:c,style:u,breakAll:s}=e,f=(0,a.Et)(l),d=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce((e,t)=>{var{word:a,width:o}=t,l=e[e.length-1];return l&&(null==n||i||l.width+o+r<Number(n))?(l.words.push(a),l.width+=o+r):e.push({words:[a],width:o}),e},[])},h=d(t),p=e=>e.reduce((e,t)=>e.width>t.width?e:t);if(!f||i||!(h.length>l||p(h).width>Number(n)))return h;for(var y=e=>{var t=d(S({breakAll:s,style:u,children:c.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>l||p(t).width>Number(n),t]},v=0,g=c.length-1,m=0;v<=g&&m<=c.length-1;){var b=Math.floor((v+g)/2),[x,w]=y(b-1),[O]=y(b);if(x||O||(v=b+1),x&&O&&(g=b-1),!x&&O){o=w;break}m++}return o||h},M=e=>[{words:(0,a.uy)(e)?[]:e.toString().split(j)}],A=e=>{var{width:t,scaleToFit:r,children:n,style:i,breakAll:a,maxLines:l}=e;if((t||r)&&!o.m.isSsr){var c=S({breakAll:a,children:n,style:i});if(!c)return M(n);var{wordsWithComputedWidth:u,spaceWidth:s}=c;return P({breakAll:a,children:n,maxLines:l,style:i},u,s,t,r)}return M(n)},E="#808080",k=(0,n.forwardRef)((e,t)=>{var r,{x:o=0,y:c=0,lineHeight:u="1em",capHeight:s="0.71em",scaleToFit:f=!1,textAnchor:d="start",verticalAnchor:h="end",fill:p=E}=e,y=O(e,b),v=(0,n.useMemo)(()=>A({breakAll:y.breakAll,children:y.children,maxLines:y.maxLines,scaleToFit:f,style:y.style,width:y.width}),[y.breakAll,y.children,y.maxLines,f,y.style,y.width]),{dx:g,dy:j,angle:S,className:P,breakAll:M}=y,k=O(y,x);if(!(0,a.vh)(o)||!(0,a.vh)(c))return null;var _=o+((0,a.Et)(g)?g:0),T=c+((0,a.Et)(j)?j:0);switch(h){case"start":r=m("calc(".concat(s,")"));break;case"middle":r=m("calc(".concat((v.length-1)/2," * -").concat(u," + (").concat(s," / 2))"));break;default:r=m("calc(".concat(v.length-1," * -").concat(u,")"))}var C=[];if(f){var D=v[0].width,{width:N}=y;C.push("scale(".concat((0,a.Et)(N)?N/D:1,")"))}return S&&C.push("rotate(".concat(S,", ").concat(_,", ").concat(T,")")),C.length&&(k.transform=C.join(" ")),n.createElement("text",w({},(0,l.J9)(k,!0),{ref:t,x:_,y:T,className:(0,i.$)("recharts-text",P),textAnchor:d,fill:p.includes("url")?E:p}),v.map((e,t)=>{var i=e.words.join(M?"":" ");return n.createElement("tspan",{x:_,dy:0===t?r:u,key:"".concat(i,"-").concat(t)},i)}))});k.displayName="Text"},1278:(e,t,r)=>{"use strict";r.d(t,{f:()=>p});var n=r(4504),i=r(8050),a=r(4056);function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class c{static create(e){return new c(e)}constructor(e){this.scale=e}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(e){var{bandAware:t,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==e){if(r)switch(r){case"start":default:return this.scale(e);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+n;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(t){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}isInRange(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}}l(c,"EPS",1e-4);var u=function(e){var{width:t,height:r}=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,a=Math.atan(r/t);return Math.abs(i>a&&i<Math.PI-a?r/Math.sin(i):t/Math.cos(i))};function s(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],i=0;i<e.length;i+=t)if(void 0!==r&&!0!==r(e[i]))return;else n.push(e[i]);return n}function f(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function p(e,t,r){var o,{tick:l,ticks:c,viewBox:d,minTickGap:p,orientation:y,interval:v,tickFormatter:g,unit:m,angle:b}=e;if(!c||!c.length||!l)return[];if((0,n.Et)(v)||a.m.isSsr)return null!=(o=s(c,((0,n.Et)(v)?v:0)+1))?o:[];var x=[],w="top"===y||"bottom"===y?"width":"height",O=m&&"width"===w?(0,i.P)(m,{fontSize:t,letterSpacing:r}):{width:0,height:0},j=(e,n)=>{var a,o="function"==typeof g?g(e.value,n):e.value;return"width"===w?(a=(0,i.P)(o,{fontSize:t,letterSpacing:r}),u({width:a.width+O.width,height:a.height+O.height},b)):(0,i.P)(o,{fontSize:t,letterSpacing:r})[w]},S=c.length>=2?(0,n.sA)(c[1].coordinate-c[0].coordinate):1,P=function(e,t,r){var n="width"===r,{x:i,y:a,width:o,height:l}=e;return 1===t?{start:n?i:a,end:n?i+o:a+l}:{start:n?i+o:a+l,end:n?i:a}}(d,S,w);return"equidistantPreserveStart"===v?function(e,t,r,n,i){for(var a,o=(n||[]).slice(),{start:l,end:c}=t,u=0,d=1,h=l;d<=o.length;)if(a=function(){var t,a=null==n?void 0:n[u];if(void 0===a)return{v:s(n,d)};var o=u,p=()=>(void 0===t&&(t=r(a,o)),t),y=a.coordinate,v=0===u||f(e,y,p,h,c);v||(u=0,h=l,d+=1),v&&(h=y+e*(p()/2+i),u+=d)}())return a.v;return[]}(S,P,j,c,p):("preserveStart"===v||"preserveStartEnd"===v?function(e,t,r,n,i,a){var o=(n||[]).slice(),l=o.length,{start:c,end:u}=t;if(a){var s=n[l-1],d=r(s,l-1),p=e*(s.coordinate+e*d/2-u);o[l-1]=s=h(h({},s),{},{tickCoord:p>0?s.coordinate-p*e:s.coordinate}),f(e,s.tickCoord,()=>d,c,u)&&(u=s.tickCoord-e*(d/2+i),o[l-1]=h(h({},s),{},{isShow:!0}))}for(var y=a?l-1:l,v=function(t){var n,a=o[t],l=()=>(void 0===n&&(n=r(a,t)),n);if(0===t){var s=e*(a.coordinate-e*l()/2-c);o[t]=a=h(h({},a),{},{tickCoord:s<0?a.coordinate-s*e:a.coordinate})}else o[t]=a=h(h({},a),{},{tickCoord:a.coordinate});f(e,a.tickCoord,l,c,u)&&(c=a.tickCoord+e*(l()/2+i),o[t]=h(h({},a),{},{isShow:!0}))},g=0;g<y;g++)v(g);return o}(S,P,j,c,p,"preserveStartEnd"===v):function(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,{start:l}=t,{end:c}=t,u=function(t){var n,u=a[t],s=()=>(void 0===n&&(n=r(u,t)),n);if(t===o-1){var d=e*(u.coordinate+e*s()/2-c);a[t]=u=h(h({},u),{},{tickCoord:d>0?u.coordinate-d*e:u.coordinate})}else a[t]=u=h(h({},u),{},{tickCoord:u.coordinate});f(e,u.tickCoord,s,l,c)&&(c=u.tickCoord-e*(s()/2+i),a[t]=h(h({},u),{},{isShow:!0}))},s=o-1;s>=0;s--)u(s);return a}(S,P,j,c,p)).filter(e=>e.isShow)}},1413:(e,t,r)=>{"use strict";r.d(t,{CA:()=>y,MC:()=>u,QG:()=>p,Vi:()=>c,cU:()=>s,fR:()=>f});var n=r(2481),i=r(7555);function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var l=(0,n.Z0)({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=(0,i.h4)(t.payload)},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=(0,i.h4)(t.payload)},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=(0,i.h4)(t.payload)},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=o(o({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:c,removeXAxis:u,addYAxis:s,removeYAxis:f,addZAxis:d,removeZAxis:h,updateYAxisWidth:p}=l.actions,y=l.reducer},1433:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var n=r(4504),i=r(9058);function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var l=(e,t,r,a,l,c,u)=>{if(null!=t&&null!=c){var{chartData:s,computedData:f,dataStartIndex:d,dataEndIndex:h}=r;return e.reduce((e,r)=>{var p,y,v,g,m,{dataDefinedOnItem:b,settings:x}=r,w=function(e,t,r){return Array.isArray(e)&&e&&t+r!==0?e.slice(t,r+1):e}((p=b,y=s,null!=p?p:y),d,h),O=null!=(v=null==x?void 0:x.dataKey)?v:null==a?void 0:a.dataKey,j=null==x?void 0:x.nameKey;return Array.isArray(g=null!=a&&a.dataKey&&Array.isArray(w)&&!Array.isArray(w[0])&&"axis"===u?(0,n.eP)(w,a.dataKey,l):c(w,t,f,j))?g.forEach(t=>{var r=o(o({},x),{},{name:t.name,unit:t.unit,color:void 0,fill:void 0});e.push((0,i.GF)({tooltipEntrySettings:r,dataKey:t.dataKey,payload:t.payload,value:(0,i.kr)(t.payload,t.dataKey),name:t.name}))}):e.push((0,i.GF)({tooltipEntrySettings:x,dataKey:O,payload:g,value:(0,i.kr)(g,O),name:null!=(m=(0,i.kr)(g,j))?m:null==x?void 0:x.name})),e},[])}}},1442:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(3667),i=r(8923),a=r(776),o=r(2284),l=r(9322);function c(e,t,r,n=new Map,s){let f=s?.(e,t,r,n);if(null!=f)return f;if(o.isPrimitive(e))return e;if(n.has(e))return n.get(e);if(Array.isArray(e)){let t=Array(e.length);n.set(e,t);for(let i=0;i<e.length;i++)t[i]=c(e[i],i,r,n,s);return Object.hasOwn(e,"index")&&(t.index=e.index),Object.hasOwn(e,"input")&&(t.input=e.input),t}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){let t=new RegExp(e.source,e.flags);return t.lastIndex=e.lastIndex,t}if(e instanceof Map){let t=new Map;for(let[i,a]of(n.set(e,t),e))t.set(i,c(a,i,r,n,s));return t}if(e instanceof Set){let t=new Set;for(let i of(n.set(e,t),e))t.add(c(i,void 0,r,n,s));return t}if("undefined"!=typeof Buffer&&Buffer.isBuffer(e))return e.subarray();if(l.isTypedArray(e)){let t=new(Object.getPrototypeOf(e)).constructor(e.length);n.set(e,t);for(let i=0;i<e.length;i++)t[i]=c(e[i],i,r,n,s);return t}if(e instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){let t=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return n.set(e,t),u(t,e,r,n,s),t}if("undefined"!=typeof File&&e instanceof File){let t=new File([e],e.name,{type:e.type});return n.set(e,t),u(t,e,r,n,s),t}if(e instanceof Blob){let t=new Blob([e],{type:e.type});return n.set(e,t),u(t,e,r,n,s),t}if(e instanceof Error){let t=new e.constructor;return n.set(e,t),t.message=e.message,t.name=e.name,t.stack=e.stack,t.cause=e.cause,u(t,e,r,n,s),t}if("object"==typeof e&&function(e){switch(i.getTag(e)){case a.argumentsTag:case a.arrayTag:case a.arrayBufferTag:case a.dataViewTag:case a.booleanTag:case a.dateTag:case a.float32ArrayTag:case a.float64ArrayTag:case a.int8ArrayTag:case a.int16ArrayTag:case a.int32ArrayTag:case a.mapTag:case a.numberTag:case a.objectTag:case a.regexpTag:case a.setTag:case a.stringTag:case a.symbolTag:case a.uint8ArrayTag:case a.uint8ClampedArrayTag:case a.uint16ArrayTag:case a.uint32ArrayTag:return!0;default:return!1}}(e)){let t=Object.create(Object.getPrototypeOf(e));return n.set(e,t),u(t,e,r,n,s),t}return e}function u(e,t,r=e,i,a){let o=[...Object.keys(t),...n.getSymbols(t)];for(let n=0;n<o.length;n++){let l=o[n],u=Object.getOwnPropertyDescriptor(e,l);(null==u||u.writable)&&(e[l]=c(t[l],l,r,i,a))}}t.cloneDeepWith=function(e,t){return c(e,void 0,e,new Map,t)},t.cloneDeepWithImpl=c,t.copyProperties=u},1501:(e,t,r)=>{"use strict";r.d(t,{u:()=>f});var n=r(9486),i=r(2015),a=r(433),o=r.n(a),l=r(4504),c=r(6852);function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var f=(0,i.forwardRef)((e,t)=>{var{aspect:r,initialDimension:a={width:-1,height:-1},width:u="100%",height:f="100%",minWidth:d=0,minHeight:h,maxHeight:p,children:y,debounce:v=0,id:g,className:m,onResize:b,style:x={}}=e,w=(0,i.useRef)(null),O=(0,i.useRef)();O.current=b,(0,i.useImperativeHandle)(t,()=>w.current);var[j,S]=(0,i.useState)({containerWidth:a.width,containerHeight:a.height}),P=(0,i.useCallback)((e,t)=>{S(r=>{var n=Math.round(e),i=Math.round(t);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,i.useEffect)(()=>{var e=e=>{var t,{width:r,height:n}=e[0].contentRect;P(r,n),null==(t=O.current)||t.call(O,r,n)};v>0&&(e=o()(e,v,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),{width:r,height:n}=w.current.getBoundingClientRect();return P(r,n),t.observe(w.current),()=>{t.disconnect()}},[P,v]);var M=(0,i.useMemo)(()=>{var{containerWidth:e,containerHeight:t}=j;if(e<0||t<0)return null;(0,c.R)((0,l._3)(u)||(0,l._3)(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",u,f),(0,c.R)(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=(0,l._3)(u)?e:u,a=(0,l._3)(f)?t:f;return r&&r>0&&(n?a=n/r:a&&(n=a*r),p&&a>p&&(a=p)),(0,c.R)(n>0||a>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,a,u,f,d,h,r),i.Children.map(y,e=>(0,i.cloneElement)(e,{width:n,height:a,style:s({width:n,height:a},e.props.style)}))},[r,y,f,p,h,d,j,u]);return i.createElement("div",{id:g?"".concat(g):void 0,className:(0,n.$)("recharts-responsive-container",m),style:s(s({},x),{},{width:u,height:f,minWidth:d,minHeight:h,maxHeight:p}),ref:w},i.createElement("div",{style:{width:0,height:0,overflow:"visible"}},M))})},1506:(e,t,r)=>{"use strict";r.d(t,{h:()=>y});var n=r(2015),i=r(9486),a=r(2661),o=r(8702),l=r(4504),c=r(4644);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var s=(e,t)=>(0,l.sA)(t-e)*Math.min(Math.abs(t-e),359.999),f=e=>{var{cx:t,cy:r,radius:n,angle:i,sign:a,isExternal:l,cornerRadius:c,cornerIsExternal:u}=e,s=c*(l?1:-1)+n,f=Math.asin(c/s)/o.Kg,d=u?i:i+a*f,h=(0,o.IZ)(t,r,s,d);return{center:h,circleTangency:(0,o.IZ)(t,r,n,d),lineTangency:(0,o.IZ)(t,r,s*Math.cos(f*o.Kg),u?i-a*f:i),theta:f}},d=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:a,endAngle:l}=e,c=s(a,l),u=a+c,f=(0,o.IZ)(t,r,i,a),d=(0,o.IZ)(t,r,i,u),h="M ".concat(f.x,",").concat(f.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(a>u),",\n    ").concat(d.x,",").concat(d.y,"\n  ");if(n>0){var p=(0,o.IZ)(t,r,n,a),y=(0,o.IZ)(t,r,n,u);h+="L ".concat(y.x,",").concat(y.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(a<=u),",\n            ").concat(p.x,",").concat(p.y," Z")}else h+="L ".concat(t,",").concat(r," Z");return h},h=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,cornerRadius:a,forceCornerRadius:o,cornerIsExternal:c,startAngle:u,endAngle:s}=e,h=(0,l.sA)(s-u),{circleTangency:p,lineTangency:y,theta:v}=f({cx:t,cy:r,radius:i,angle:u,sign:h,cornerRadius:a,cornerIsExternal:c}),{circleTangency:g,lineTangency:m,theta:b}=f({cx:t,cy:r,radius:i,angle:s,sign:-h,cornerRadius:a,cornerIsExternal:c}),x=c?Math.abs(u-s):Math.abs(u-s)-v-b;if(x<0)return o?"M ".concat(y.x,",").concat(y.y,"\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*a,",0\n        a").concat(a,",").concat(a,",0,0,1,").concat(-(2*a),",0\n      "):d({cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:u,endAngle:s});var w="M ".concat(y.x,",").concat(y.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(h<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(x>180),",").concat(+(h<0),",").concat(g.x,",").concat(g.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(h<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var{circleTangency:O,lineTangency:j,theta:S}=f({cx:t,cy:r,radius:n,angle:u,sign:h,isExternal:!0,cornerRadius:a,cornerIsExternal:c}),{circleTangency:P,lineTangency:M,theta:A}=f({cx:t,cy:r,radius:n,angle:s,sign:-h,isExternal:!0,cornerRadius:a,cornerIsExternal:c}),E=c?Math.abs(u-s):Math.abs(u-s)-S-A;if(E<0&&0===a)return"".concat(w,"L").concat(t,",").concat(r,"Z");w+="L".concat(M.x,",").concat(M.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(h<0),",").concat(P.x,",").concat(P.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(E>180),",").concat(+(h>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(h<0),",").concat(j.x,",").concat(j.y,"Z")}else w+="L".concat(t,",").concat(r,"Z");return w},p={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},y=e=>{var t,r=(0,c.e)(e,p),{cx:o,cy:s,innerRadius:f,outerRadius:y,cornerRadius:v,forceCornerRadius:g,cornerIsExternal:m,startAngle:b,endAngle:x,className:w}=r;if(y<f||b===x)return null;var O=(0,i.$)("recharts-sector",w),j=y-f,S=(0,l.F4)(v,j,0,!0);return t=S>0&&360>Math.abs(b-x)?h({cx:o,cy:s,innerRadius:f,outerRadius:y,cornerRadius:Math.min(S,j/2),forceCornerRadius:g,cornerIsExternal:m,startAngle:b,endAngle:x}):d({cx:o,cy:s,innerRadius:f,outerRadius:y,startAngle:b,endAngle:x}),n.createElement("path",u({},(0,a.J9)(r,!0),{className:O,d:t}))}},1516:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(4980),i=r(4919),a=r(9841),o=r(6614);t.iteratee=function(e){if(null==e)return n.identity;switch(typeof e){case"function":return e;case"object":if(Array.isArray(e)&&2===e.length)return o.matchesProperty(e[0],e[1]);return a.matches(e);case"string":case"symbol":case"number":return i.property(e)}}},1590:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.debounce=function(e,t=0,r={}){let n;"object"!=typeof r&&(r={});let i=null,a=null,o=null,l=0,c=null,{leading:u=!1,trailing:s=!0,maxWait:f}=r,d="maxWait"in r,h=d?Math.max(Number(f)||0,t):0,p=t=>(null!==i&&(n=e.apply(a,i)),i=a=null,l=t,n),y=e=>(l=e,c=setTimeout(b,t),u&&null!==i)?p(e):n,v=e=>(c=null,s&&null!==i)?p(e):n,g=e=>{if(null===o)return!0;let r=e-o,n=d&&e-l>=h;return r>=t||r<0||n},m=e=>{let r=t-(null===o?0:e-o),n=h-(e-l);return d?Math.min(r,n):r},b=()=>{let e=Date.now();if(g(e))return v(e);c=setTimeout(b,m(e))},x=function(...e){let r=Date.now(),l=g(r);if(i=e,a=this,o=r,l){if(null===c)return y(r);if(d)return clearTimeout(c),c=setTimeout(b,t),p(r)}return null===c&&(c=setTimeout(b,t)),n};return x.cancel=()=>{null!==c&&clearTimeout(c),l=0,o=i=a=c=null},x.flush=()=>null===c?n:v(Date.now()),x}},1626:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8097),i=r(9751),a=r(360);t.last=function(e){if(a.isArrayLike(e))return n.last(i.toArray(e))}},2004:(e,t,r)=>{"use strict";function n(e){return function(){return e}}r.d(t,{A:()=>n})},2015:e=>{"use strict";e.exports=require("react")},2111:(e,t)=>{"use strict";var r,n=Symbol.for("react.element"),i=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),u=Symbol.for("react.context"),s=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),p=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),t.isFragment=function(e){return function(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case a:case l:case o:case d:case h:return e;default:switch(e=e&&e.$$typeof){case s:case u:case f:case y:case p:case c:return e;default:return t}}case i:return t}}}(e)===a}},2115:e=>{"use strict";e.exports=require("yaml")},2157:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var n=e=>e.tooltip},2219:(e,t,r)=>{"use strict";r.d(t,{M:()=>f});var n=r(2015),i=r(9486),a=r(2661),o=r(4644),l=r(6326);function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var u=(e,t,r,n,i)=>{var a,o=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,c=r>=0?1:-1,u=+(n>=0&&r>=0||n<0&&r<0);if(o>0&&i instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=i[f]>o?o:i[f];a="M".concat(e,",").concat(t+l*s[0]),s[0]>0&&(a+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(u,",").concat(e+c*s[0],",").concat(t)),a+="L ".concat(e+r-c*s[1],",").concat(t),s[1]>0&&(a+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(u,",\n        ").concat(e+r,",").concat(t+l*s[1])),a+="L ".concat(e+r,",").concat(t+n-l*s[2]),s[2]>0&&(a+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(u,",\n        ").concat(e+r-c*s[2],",").concat(t+n)),a+="L ".concat(e+c*s[3],",").concat(t+n),s[3]>0&&(a+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(u,",\n        ").concat(e,",").concat(t+n-l*s[3])),a+="Z"}else if(o>0&&i===+i&&i>0){var d=Math.min(o,i);a="M ".concat(e,",").concat(t+l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(u,",").concat(e+c*d,",").concat(t,"\n            L ").concat(e+r-c*d,",").concat(t,"\n            A ").concat(d,",").concat(d,",0,0,").concat(u,",").concat(e+r,",").concat(t+l*d,"\n            L ").concat(e+r,",").concat(t+n-l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(u,",").concat(e+r-c*d,",").concat(t+n,"\n            L ").concat(e+c*d,",").concat(t+n,"\n            A ").concat(d,",").concat(d,",0,0,").concat(u,",").concat(e,",").concat(t+n-l*d," Z")}else a="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return a},s={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},f=e=>{var t=(0,o.e)(e,s),r=(0,n.useRef)(null),[f,d]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&d(e)}catch(e){}},[]);var{x:h,y:p,width:y,height:v,radius:g,className:m}=t,{animationEasing:b,animationDuration:x,animationBegin:w,isAnimationActive:O,isUpdateAnimationActive:j}=t;if(h!==+h||p!==+p||y!==+y||v!==+v||0===y||0===v)return null;var S=(0,i.$)("recharts-rectangle",m);return j?n.createElement(l.i,{canBegin:f>0,from:{width:y,height:v,x:h,y:p},to:{width:y,height:v,x:h,y:p},duration:x,animationEasing:b,isActive:j},e=>{var{width:i,height:o,x:s,y:d}=e;return n.createElement(l.i,{canBegin:f>0,from:"0px ".concat(-1===f?1:f,"px"),to:"".concat(f,"px 0px"),attributeName:"strokeDasharray",begin:w,duration:x,isActive:O,easing:b},n.createElement("path",c({},(0,a.J9)(t,!0),{className:S,d:u(s,d,i,o,g),ref:r})))}):n.createElement("path",c({},(0,a.J9)(t,!0),{className:S,d:u(h,p,y,v,g)}))}},2284:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPrimitive=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e}},2326:e=>{"use strict";e.exports=require("react-dom")},2339:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(7634);t.toNumber=function(e){return n.isSymbol(e)?NaN:Number(e)}},2383:(e,t,r)=>{"use strict";r.d(t,{i:()=>o});var n=r(3497);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var o=(e,t,r,i)=>{if(null==t)return n.k_;var o=function(e,t,r){return"axis"===t?"click"===r?e.axisInteraction.click:e.axisInteraction.hover:"click"===r?e.itemInteraction.click:e.itemInteraction.hover}(e,t,r);if(null==o)return n.k_;if(o.active)return o;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&null!=e.syncInteraction.index)return e.syncInteraction;var l=!0===e.settings.active;if(null!=o.index){if(l)return a(a({},o),{},{active:!0})}else if(null!=i)return{active:!0,coordinate:void 0,dataKey:void 0,index:i};return a(a({},n.k_),{},{coordinate:o.coordinate})}},2428:(e,t,r)=>{"use strict";r.d(t,{$7:()=>f,Ru:()=>s,uZ:()=>u});var n=r(2481),i=r(3497),a=r(6113),o=r(6973),l=r(3411),c=r(594),u=(0,n.VP)("keyDown"),s=(0,n.VP)("focus"),f=(0,n.Nc)();f.startListening({actionCreator:u,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,u=e.payload;if("ArrowRight"===u||"ArrowLeft"===u||"Enter"===u){var s=Number((0,c.P)(n,(0,a.n4)(r))),f=(0,a.R4)(r);if("Enter"===u){var d=(0,o.pg)(r,"axis","hover",String(n.index));t.dispatch((0,i.o4)({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:d}));return}var h=s+("ArrowRight"===u?1:-1)*("left-to-right"===(0,l._y)(r)?1:-1);if(null!=f&&!(h>=f.length)&&!(h<0)){var p=(0,o.pg)(r,"axis","hover",String(h));t.dispatch((0,i.o4)({active:!0,activeIndex:h.toString(),activeDataKey:void 0,activeCoordinate:p}))}}}}}),f.startListening({actionCreator:s,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var a=(0,o.pg)(r,"axis","hover",String("0"));t.dispatch((0,i.o4)({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:a}))}}}})},2481:(e,t,r)=>{"use strict";r.d(t,{U1:()=>m,VP:()=>u,Nc:()=>ey,Z0:()=>T});var n=r(6912);function i(e){return({dispatch:t,getState:r})=>n=>i=>"function"==typeof i?i(t,r,e):n(i)}var a=i(),o=r(7555),l="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?n.Zz:n.Zz.apply(null,arguments)};"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var c=e=>e&&"function"==typeof e.match;function u(e,t){function r(...n){if(t){let r=t(...n);if(!r)throw Error(ew(0));return{type:e,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>(0,n.ve)(t)&&t.type===e,r}function s(e){return["type","payload","error","meta"].indexOf(e)>-1}var f=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function d(e){return(0,o.a6)(e)?(0,o.jM)(e,()=>{}):e}function h(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var p=()=>function(e){let{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:o=!0}=e??{},l=new f;return t&&("boolean"==typeof t?l.push(a):l.push(i(t.extraArgument))),l},y=e=>t=>{setTimeout(t,e)},v=(e={type:"raf"})=>t=>(...r)=>{let n=t(...r),i=!0,a=!1,o=!1,l=new Set,c="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:y(10):"callback"===e.type?e.queueNotification:y(e.timeout),u=()=>{o=!1,a&&(a=!1,l.forEach(e=>e()))};return Object.assign({},n,{subscribe(e){let t=n.subscribe(()=>i&&e());return l.add(e),()=>{t(),l.delete(e)}},dispatch(e){try{return(a=!(i=!e?.meta?.RTK_autoBatch))&&!o&&(o=!0,c(u)),n.dispatch(e)}finally{i=!0}}})},g=e=>function(t){let{autoBatch:r=!0}=t??{},n=new f(e);return r&&n.push(v("object"==typeof r?r:void 0)),n};function m(e){let t,r,i=p(),{reducer:a,middleware:o,devTools:c=!0,duplicateMiddlewareCheck:u=!0,preloadedState:s,enhancers:f}=e||{};if("function"==typeof a)t=a;else if((0,n.Qd)(a))t=(0,n.HY)(a);else throw Error(ew(1));r="function"==typeof o?o(i):i();let d=n.Zz;c&&(d=l({trace:!1,..."object"==typeof c&&c}));let h=g((0,n.Tw)(...r)),y=d(..."function"==typeof f?f(h):h());return(0,n.y$)(t,s,y)}function b(e){let t,r={},n=[],i={addCase(e,t){let n="string"==typeof e?e:e.type;if(!n)throw Error(ew(28));if(n in r)throw Error(ew(29));return r[n]=t,i},addMatcher:(e,t)=>(n.push({matcher:e,reducer:t}),i),addDefaultCase:e=>(t=e,i)};return e(i),[r,n,t]}var x=(e,t)=>c(e)?e.match(t):e(t);function w(...e){return t=>e.some(e=>x(e,t))}var O=(e=21)=>{let t="",r=e;for(;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t},j=["name","message","stack","code"],S=class{constructor(e,t){this.payload=e,this.meta=t}_type},P=class{constructor(e,t){this.payload=e,this.meta=t}_type},M=e=>{if("object"==typeof e&&null!==e){let t={};for(let r of j)"string"==typeof e[r]&&(t[r]=e[r]);return t}return{message:String(e)}},A="External signal was aborted";function E(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}var k=Symbol.for("rtk-slice-createasyncthunk"),_=(e=>(e.reducer="reducer",e.reducerWithPrepare="reducerWithPrepare",e.asyncThunk="asyncThunk",e))(_||{}),T=function({creators:e}={}){let t=e?.asyncThunk?.[k];return function(e){let r,{name:n,reducerPath:i=n}=e;if(!n)throw Error(ew(11));let a=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},l=Object.keys(a),c={},s={},f={},p=[],y={addCase(e,t){let r="string"==typeof e?e:e.type;if(!r)throw Error(ew(12));if(r in s)throw Error(ew(13));return s[r]=t,y},addMatcher:(e,t)=>(p.push({matcher:e,reducer:t}),y),exposeAction:(e,t)=>(f[e]=t,y),exposeCaseReducer:(e,t)=>(c[e]=t,y)};function v(){let[t={},r=[],n]="function"==typeof e.extraReducers?b(e.extraReducers):[e.extraReducers],i={...t,...s};return function(e,t){let r,[n,i,a]=b(t);if("function"==typeof e)r=()=>d(e());else{let t=d(e);r=()=>t}function l(e=r(),t){let c=[n[t.type],...i.filter(({matcher:e})=>e(t)).map(({reducer:e})=>e)];return 0===c.filter(e=>!!e).length&&(c=[a]),c.reduce((e,r)=>{if(r)if((0,o.Qx)(e)){let n=r(e,t);return void 0===n?e:n}else{if((0,o.a6)(e))return(0,o.jM)(e,e=>r(e,t));let n=r(e,t);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}return e},e)}return l.getInitialState=r,l}(e.initialState,e=>{for(let t in i)e.addCase(t,i[t]);for(let t of p)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)})}l.forEach(r=>{let i=a[r],o={reducerName:r,type:`${n}/${r}`,createNotation:"function"==typeof e.reducers};"asyncThunk"===i._reducerDefinitionType?function({type:e,reducerName:t},r,n,i){if(!i)throw Error(ew(18));let{payloadCreator:a,fulfilled:o,pending:l,rejected:c,settled:u,options:s}=r,f=i(e,a,s);n.exposeAction(t,f),o&&n.addCase(f.fulfilled,o),l&&n.addCase(f.pending,l),c&&n.addCase(f.rejected,c),u&&n.addMatcher(f.settled,u),n.exposeCaseReducer(t,{fulfilled:o||C,pending:l||C,rejected:c||C,settled:u||C})}(o,i,y,t):function({type:e,reducerName:t,createNotation:r},n,i){let a,o;if("reducer"in n){if(r&&"reducerWithPrepare"!==n._reducerDefinitionType)throw Error(ew(17));a=n.reducer,o=n.prepare}else a=n;i.addCase(e,a).exposeCaseReducer(t,a).exposeAction(t,o?u(e,o):u(e))}(o,i,y)});let g=e=>e,m=new Map,x=new WeakMap;function w(e,t){return r||(r=v()),r(e,t)}function O(){return r||(r=v()),r.getInitialState()}function j(t,r=!1){function n(e){let i=e[t];return void 0===i&&r&&(i=h(x,n,O)),i}function i(t=g){let n=h(m,r,()=>new WeakMap);return h(n,t,()=>{let n={};for(let[i,a]of Object.entries(e.selectors??{}))n[i]=function(e,t,r,n){function i(a,...o){let l=t(a);return void 0===l&&n&&(l=r()),e(l,...o)}return i.unwrapped=e,i}(a,t,()=>h(x,t,O),r);return n})}return{reducerPath:t,getSelectors:i,get selectors(){return i(n)},selectSlice:n}}let S={name:n,reducer:w,actions:f,caseReducers:c,getInitialState:O,...j(i),injectInto(e,{reducerPath:t,...r}={}){let n=t??i;return e.inject({reducerPath:n,reducer:w},r),{...S,...j(n,!0)}}};return S}}();function C(){}function D(e){return function(t,r){let n=t=>{isAction(r)&&Object.keys(r).every(s)?e(r.payload,t):e(r,t)};return(null)(t)?(n(t),t):createNextState3(t,n)}}function N(e,t){return t(e)}function I(e){return Array.isArray(e)||(e=Object.values(e)),e}var z="listener",L="completed",R="cancelled",$=`task-${R}`,B=`task-${L}`,F=`${z}-${R}`,U=`${z}-${L}`,K=class{constructor(e){this.code=e,this.message=`task ${R} (reason: ${e})`}name="TaskAbortError";message},H=(e,t)=>{if("function"!=typeof e)throw TypeError(ew(32))},G=()=>{},W=(e,t=G)=>(e.catch(t),e),Z=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),q=(e,t)=>{let r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},V=e=>{if(e.aborted){let{reason:t}=e;throw new K(t)}};function Y(e,t){let r=G;return new Promise((n,i)=>{let a=()=>i(new K(e.reason));if(e.aborted)return void a();r=Z(e,a),t.finally(()=>r()).then(n,i)}).finally(()=>{r=G})}var J=async(e,t)=>{try{await Promise.resolve();let t=await e();return{status:"ok",value:t}}catch(e){return{status:e instanceof K?"cancelled":"rejected",error:e}}finally{t?.()}},X=e=>t=>W(Y(e,t).then(t=>(V(e),t))),Q=e=>{let t=X(e);return e=>t(new Promise(t=>setTimeout(t,e)))},{assign:ee}=Object,et={},er="listenerMiddleware",en=(e,t)=>{let r=t=>Z(e,()=>q(t,e.reason));return(n,i)=>{H(n,"taskExecutor");let a=new AbortController;r(a);let o=J(async()=>{V(e),V(a.signal);let t=await n({pause:X(a.signal),delay:Q(a.signal),signal:a.signal});return V(a.signal),t},()=>q(a,B));return i?.autoJoin&&t.push(o.catch(G)),{result:X(e)(o),cancel(){q(a,$)}}}},ei=(e,t)=>{let r=async(r,n)=>{V(t);let i=()=>{},a=[new Promise((t,n)=>{let a=e({predicate:r,effect:(e,r)=>{r.unsubscribe(),t([e,r.getState(),r.getOriginalState()])}});i=()=>{a(),n()}})];null!=n&&a.push(new Promise(e=>setTimeout(e,n,null)));try{let e=await Y(t,Promise.race(a));return V(t),e}finally{i()}};return(e,t)=>W(r(e,t))},ea=e=>{let{type:t,actionCreator:r,matcher:n,predicate:i,effect:a}=e;if(t)i=u(t).match;else if(r)t=r.type,i=r.match;else if(n)i=n;else if(i);else throw Error(ew(21));return H(a,"options.listener"),{predicate:i,type:t,effect:a}},eo=ee(e=>{let{type:t,predicate:r,effect:n}=ea(e);return{id:O(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw Error(ew(22))}}},{withTypes:()=>eo}),el=(e,t)=>{let{type:r,effect:n,predicate:i}=ea(t);return Array.from(e.values()).find(e=>("string"==typeof r?e.type===r:e.predicate===i)&&e.effect===n)},ec=e=>{e.pending.forEach(e=>{q(e,F)})},eu=e=>()=>{e.forEach(ec),e.clear()},es=(e,t,r)=>{try{e(t,r)}catch(e){setTimeout(()=>{throw e},0)}},ef=ee(u(`${er}/add`),{withTypes:()=>ef}),ed=u(`${er}/removeAll`),eh=ee(u(`${er}/remove`),{withTypes:()=>eh}),ep=(...e)=>{console.error(`${er}/error`,...e)},ey=(e={})=>{let t=new Map,{extra:r,onError:i=ep}=e;H(i,"onError");let a=e=>(e.unsubscribe=()=>t.delete(e.id),t.set(e.id,e),t=>{e.unsubscribe(),t?.cancelActive&&ec(e)}),o=e=>a(el(t,e)??eo(e));ee(o,{withTypes:()=>o});let l=e=>{let r=el(t,e);return r&&(r.unsubscribe(),e.cancelActive&&ec(r)),!!r};ee(l,{withTypes:()=>l});let c=async(e,n,a,l)=>{let c=new AbortController,u=ei(o,c.signal),s=[];try{e.pending.add(c),await Promise.resolve(e.effect(n,ee({},a,{getOriginalState:l,condition:(e,t)=>u(e,t).then(Boolean),take:u,delay:Q(c.signal),pause:X(c.signal),extra:r,signal:c.signal,fork:en(c.signal,s),unsubscribe:e.unsubscribe,subscribe:()=>{t.set(e.id,e)},cancelActiveListeners:()=>{e.pending.forEach((e,t,r)=>{e!==c&&(q(e,F),r.delete(e))})},cancel:()=>{q(c,F),e.pending.delete(c)},throwIfCancelled:()=>{V(c.signal)}})))}catch(e){e instanceof K||es(i,e,{raisedBy:"effect"})}finally{await Promise.all(s),q(c,U),e.pending.delete(c)}},u=eu(t);return{middleware:e=>r=>a=>{let s;if(!(0,n.ve)(a))return r(a);if(ef.match(a))return o(a.payload);if(ed.match(a))return void u();if(eh.match(a))return l(a.payload);let f=e.getState(),d=()=>{if(f===et)throw Error(ew(23));return f};try{if(s=r(a),t.size>0){let r=e.getState();for(let n of Array.from(t.values())){let t=!1;try{t=n.predicate(a,r,f)}catch(e){t=!1,es(i,e,{raisedBy:"predicate"})}t&&c(n,a,e,d)}}}finally{f=et}return s},startListening:o,stopListening:l,clearListeners:u}},ev=e=>"reducerPath"in e&&"string"==typeof e.reducerPath,eg=Symbol.for("rtk-state-proxy-original"),em=e=>!!e&&!!e[eg],eb=new WeakMap,ex={};function ew(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}},2505:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(2339);t.toFinite=function(e){return e?(e=n.toNumber(e))===1/0||e===-1/0?(e<0?-1:1)*Number.MAX_VALUE:e==e?e:0:0===e?e:0}},2579:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var n=r(9684),i=()=>(0,n.G)(e=>e.rootProps.accessibilityLayer)},2623:(e,t,r)=>{"use strict";r.d(t,{y:()=>V});var n=r(2015),i=r(26),a=r.n(i),o=r(2219),l=r(9486),c=r(2661),u=r(4644),s=r(6326);function f(){return(f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var d=(e,t,r,n,i)=>{var a,o=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-o/2,",").concat(t+i)+"L ".concat(e+r-o/2-n,",").concat(t+i)+"L ".concat(e,",").concat(t," Z")},h={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},p=e=>{var t=(0,u.e)(e,h),r=(0,n.useRef)(),[i,a]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&a(e)}catch(e){}},[]);var{x:o,y:p,upperWidth:y,lowerWidth:v,height:g,className:m}=t,{animationEasing:b,animationDuration:x,animationBegin:w,isUpdateAnimationActive:O}=t;if(o!==+o||p!==+p||y!==+y||v!==+v||g!==+g||0===y&&0===v||0===g)return null;var j=(0,l.$)("recharts-trapezoid",m);return O?n.createElement(s.i,{canBegin:i>0,from:{upperWidth:0,lowerWidth:0,height:g,x:o,y:p},to:{upperWidth:y,lowerWidth:v,height:g,x:o,y:p},duration:x,animationEasing:b,isActive:O},e=>{var{upperWidth:a,lowerWidth:o,height:l,x:u,y:h}=e;return n.createElement(s.i,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:w,duration:x,easing:b},n.createElement("path",f({},(0,c.J9)(t,!0),{className:j,d:d(u,h,a,o,l),ref:r})))}):n.createElement("g",null,n.createElement("path",f({},(0,c.J9)(t,!0),{className:j,d:d(o,p,y,v,g)})))},y=r(1506),v=r(7269);let g=Math.cos,m=Math.sin,b=Math.sqrt,x=Math.PI,w=2*x,O={draw(e,t){let r=b(t/x);e.moveTo(r,0),e.arc(0,0,r,0,w)}},j=b(1/3),S=2*j,P=m(x/10)/m(7*x/10),M=m(w/10)*P,A=-g(w/10)*P,E=b(3),k=b(3)/2,_=1/b(12),T=(_/2+1)*3;var C=r(2004),D=r(8232);b(3),b(3);var N=r(4504),I=["type","size","sizeType"];function z(){return(z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function L(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function R(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?L(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):L(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var $={symbolCircle:O,symbolCross:{draw(e,t){let r=b(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=b(t/S),n=r*j;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=b(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=b(.8908130915292852*t),n=M*r,i=A*r;e.moveTo(0,-r),e.lineTo(n,i);for(let t=1;t<5;++t){let a=w*t/5,o=g(a),l=m(a);e.lineTo(l*r,-o*r),e.lineTo(o*n-l*i,l*n+o*i)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-b(t/(3*E));e.moveTo(0,2*r),e.lineTo(-E*r,-r),e.lineTo(E*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=b(t/T),n=r/2,i=r*_,a=r*_+r,o=-n;e.moveTo(n,i),e.lineTo(n,a),e.lineTo(o,a),e.lineTo(-.5*n-k*i,k*n+-.5*i),e.lineTo(-.5*n-k*a,k*n+-.5*a),e.lineTo(-.5*o-k*a,k*o+-.5*a),e.lineTo(-.5*n+k*i,-.5*i-k*n),e.lineTo(-.5*n+k*a,-.5*a-k*n),e.lineTo(-.5*o+k*a,-.5*a-k*o),e.closePath()}}},B=Math.PI/180,F=e=>$["symbol".concat((0,N.Zb)(e))]||O,U=(e,t,r)=>{if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*B;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.tan(n)**2);case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},K=e=>{var{type:t="circle",size:r=64,sizeType:i="area"}=e,a=R(R({},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,I)),{},{type:t,size:r,sizeType:i}),{className:o,cx:u,cy:s}=a,f=(0,c.J9)(a,!0);return u===+u&&s===+s&&r===+r?n.createElement("path",z({},f,{className:(0,l.$)("recharts-symbols",o),transform:"translate(".concat(u,", ").concat(s,")"),d:(()=>{var e=F(t);return(function(e,t){let r=null,n=(0,D.i)(i);function i(){let i;if(r||(r=i=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),i)return r=null,i+""||null}return e="function"==typeof e?e:(0,C.A)(e||O),t="function"==typeof t?t:(0,C.A)(void 0===t?64:+t),i.type=function(t){return arguments.length?(e="function"==typeof t?t:(0,C.A)(t),i):e},i.size=function(e){return arguments.length?(t="function"==typeof e?e:(0,C.A)(+e),i):t},i.context=function(e){return arguments.length?(r=null==e?null:e,i):r},i})().type(e).size(U(r,i,t))()})()})):null};K.registerSymbol=(e,t)=>{$["symbol".concat((0,N.Zb)(e))]=t};var H=["option","shapeType","propTransformer","activeClassName","isActive"];function G(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function W(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?G(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):G(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Z(e,t){return W(W({},t),e)}function q(e){var{shapeType:t,elementProps:r}=e;switch(t){case"rectangle":return n.createElement(o.M,r);case"trapezoid":return n.createElement(p,r);case"sector":return n.createElement(y.h,r);case"symbols":if("symbols"===t)return n.createElement(K,r);break;default:return null}}function V(e){var t,{option:r,shapeType:i,propTransformer:o=Z,activeClassName:l="recharts-active-shape",isActive:c}=e,u=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,H);if((0,n.isValidElement)(r))t=(0,n.cloneElement)(r,W(W({},u),(0,n.isValidElement)(r)?r.props:r));else if("function"==typeof r)t=r(u);else if(a()(r)&&"boolean"!=typeof r){var s=o(r,u);t=n.createElement(q,{shapeType:i,elementProps:s})}else t=n.createElement(q,{shapeType:i,elementProps:u});return c?n.createElement(v.W,{className:l},t):t}},2641:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(477),i=r(3385),a=r(8708);t.sortBy=function(e,...t){let r=t.length;return r>1&&a.isIterateeCall(e,t[0],t[1])?t=[]:r>2&&a.isIterateeCall(t[0],t[1],t[2])&&(t=[t[0]]),n.orderBy(e,i.flatten(t),["asc"])}},2661:(e,t,r)=>{"use strict";r.d(t,{J9:()=>y,aS:()=>h});var n=r(7063),i=r.n(n),a=r(2015),o=r(6851),l=r(4504),c=r(9044),u=e=>"string"==typeof e?e:e?e.displayName||e.name||"Component":"",s=null,f=null,d=e=>{if(e===s&&Array.isArray(f))return f;var t=[];return a.Children.forEach(e,e=>{(0,l.uy)(e)||((0,o.isFragment)(e)?t=t.concat(d(e.props.children)):t.push(e))}),f=t,s=e,t};function h(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(e=>u(e)):[u(t)],d(e).forEach(e=>{var t=i()(e,"type.displayName")||i()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}var p=(e,t,r,n)=>{var i,a=null!=(i=n&&(null===c.VU||void 0===c.VU?void 0:c.VU[n]))?i:[];return t.startsWith("data-")||"function"!=typeof e&&(n&&a.includes(t)||c.QQ.includes(t))||r&&c.j2.includes(t)},y=(e,t,r)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,a.isValidElement)(e)&&(n=e.props),"object"!=typeof n&&"function"!=typeof n)return null;var i={};return Object.keys(n).forEach(e=>{var a;p(null==(a=n)?void 0:a[e],e,t,r)&&(i[e]=n[e])}),i}},2739:(e,t,r)=>{"use strict";r.d(t,{q:()=>n});var n=(e,t,r,n)=>{var i;return"axis"===t?e.tooltipItemPayloads:0===e.tooltipItemPayloads.length?[]:null==(i="hover"===r?e.itemInteraction.hover.dataKey:e.itemInteraction.click.dataKey)&&null!=n?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter(e=>{var t;return(null==(t=e.settings)?void 0:t.dataKey)===i})}},2764:(e,t,r)=>{"use strict";r.d(t,{I:()=>K});var n=r(2015);function i(){}function a(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function o(e){this._context=e}function l(e){this._context=e}function c(e){this._context=e}o.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:a(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},l.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},c.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class u{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function s(e){this._context=e}function f(e){this._context=e}function d(e){return new f(e)}s.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e*=1,t*=1,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function h(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0);return((a<0?-1:1)+(o<0?-1:1))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs((a*i+o*n)/(n+i)))||0}function p(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function y(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,l=(a-n)/3;e._context.bezierCurveTo(n+l,i+l*t,a-l,o-l*r,a,o)}function v(e){this._context=e}function g(e){this._context=new m(e)}function m(e){this._context=e}function b(e){this._context=e}function x(e){var t,r,n=e.length-1,i=Array(n),a=Array(n),o=Array(n);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<n-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[n-1]=2,a[n-1]=7,o[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=i[t]/a[t-1],a[t]-=r,o[t]-=r*o[t-1];for(i[n-1]=o[n-1]/a[n-1],t=n-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(t=0,a[n-1]=(e[n]+i[n-1])/2;t<n-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function w(e,t){this._context=e,this._t=t}f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},v.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:y(this,this._t0,p(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t*=1,(e*=1)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,y(this,p(this,r=h(this,e,t)),r);break;default:y(this,this._t0,r=h(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(g.prototype=Object.create(v.prototype)).point=function(e,t){v.prototype.point.call(this,t,e)},m.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}},b.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=x(e),i=x(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},w.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var O=r(2835),j=r(2004),S=r(8232);function P(e){return e[0]}function M(e){return e[1]}function A(e,t){var r=(0,j.A)(!0),n=null,i=d,a=null,o=(0,S.i)(l);function l(l){var c,u,s,f=(l=(0,O.A)(l)).length,d=!1;for(null==n&&(a=i(s=o())),c=0;c<=f;++c)!(c<f&&r(u=l[c],c,l))===d&&((d=!d)?a.lineStart():a.lineEnd()),d&&a.point(+e(u,c,l),+t(u,c,l));if(s)return a=null,s+""||null}return e="function"==typeof e?e:void 0===e?P:(0,j.A)(e),t="function"==typeof t?t:void 0===t?M:(0,j.A)(t),l.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),l):e},l.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),l):t},l.defined=function(e){return arguments.length?(r="function"==typeof e?e:(0,j.A)(!!e),l):r},l.curve=function(e){return arguments.length?(i=e,null!=n&&(a=i(n)),l):i},l.context=function(e){return arguments.length?(null==e?n=a=null:a=i(n=e),l):n},l}function E(e,t,r){var n=null,i=(0,j.A)(!0),a=null,o=d,l=null,c=(0,S.i)(u);function u(u){var s,f,d,h,p,y=(u=(0,O.A)(u)).length,v=!1,g=Array(y),m=Array(y);for(null==a&&(l=o(p=c())),s=0;s<=y;++s){if(!(s<y&&i(h=u[s],s,u))===v)if(v=!v)f=s,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),d=s-1;d>=f;--d)l.point(g[d],m[d]);l.lineEnd(),l.areaEnd()}v&&(g[s]=+e(h,s,u),m[s]=+t(h,s,u),l.point(n?+n(h,s,u):g[s],r?+r(h,s,u):m[s]))}if(p)return l=null,p+""||null}function s(){return A().defined(i).curve(o).context(a)}return e="function"==typeof e?e:void 0===e?P:(0,j.A)(+e),t="function"==typeof t?t:void 0===t?(0,j.A)(0):(0,j.A)(+t),r="function"==typeof r?r:void 0===r?M:(0,j.A)(+r),u.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),n=null,u):e},u.x0=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),u):e},u.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:(0,j.A)(+e),u):n},u.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),r=null,u):t},u.y0=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),u):t},u.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:(0,j.A)(+e),u):r},u.lineX0=u.lineY0=function(){return s().x(e).y(t)},u.lineY1=function(){return s().x(e).y(r)},u.lineX1=function(){return s().x(n).y(t)},u.defined=function(e){return arguments.length?(i="function"==typeof e?e:(0,j.A)(!!e),u):i},u.curve=function(e){return arguments.length?(o=e,null!=a&&(l=o(a)),u):o},u.context=function(e){return arguments.length?(null==e?a=l=null:l=o(a=e),u):a},u}var k=r(9486),_=r(9044),T=r(2661),C=r(4504),D=r(7463);function N(){return(N=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function I(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function z(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?I(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):I(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var L={curveBasisClosed:function(e){return new l(e)},curveBasisOpen:function(e){return new c(e)},curveBasis:function(e){return new o(e)},curveBumpX:function(e){return new u(e,!0)},curveBumpY:function(e){return new u(e,!1)},curveLinearClosed:function(e){return new s(e)},curveLinear:d,curveMonotoneX:function(e){return new v(e)},curveMonotoneY:function(e){return new g(e)},curveNatural:function(e){return new b(e)},curveStep:function(e){return new w(e,.5)},curveStepAfter:function(e){return new w(e,1)},curveStepBefore:function(e){return new w(e,0)}},R=e=>(0,D.H)(e.x)&&(0,D.H)(e.y),$=e=>e.x,B=e=>e.y,F=(e,t)=>{if("function"==typeof e)return e;var r="curve".concat((0,C.Zb)(e));return("curveMonotone"===r||"curveBump"===r)&&t?L["".concat(r).concat("vertical"===t?"Y":"X")]:L[r]||d},U=e=>{var t,{type:r="linear",points:n=[],baseLine:i,layout:a,connectNulls:o=!1}=e,l=F(r,a),c=o?n.filter(R):n;if(Array.isArray(i)){var u=o?i.filter(e=>R(e)):i,s=c.map((e,t)=>z(z({},e),{},{base:u[t]}));return(t="vertical"===a?E().y(B).x1($).x0(e=>e.base.x):E().x($).y1(B).y0(e=>e.base.y)).defined(R).curve(l),t(s)}return(t="vertical"===a&&(0,C.Et)(i)?E().y(B).x1($).x0(i):(0,C.Et)(i)?E().x($).y1(B).y0(i):A().x($).y(B)).defined(R).curve(l),t(c)},K=e=>{var{className:t,points:r,path:i,pathRef:a}=e;if((!r||!r.length)&&!i)return null;var o=r&&r.length?U(e):i;return n.createElement("path",N({},(0,T.J9)(e,!1),(0,_._U)(e),{className:(0,k.$)("recharts-curve",t),d:null===o?void 0:o,ref:a}))}},2779:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(5411),i=r(1078);t.isEqual=function(e,t){return n.isEqualWith(e,t,i.noop)}},2835:(e,t,r)=>{"use strict";function n(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}r.d(t,{A:()=>n}),Array.prototype.slice},2878:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{default:()=>A,getServerSideProps:()=>E});var i=r(8732),a=r(9733),o=r(8079),l=r(1011),c=r(5806),u=r(3762),s=r(5928),f=r(2015),d=r(1501),h=r(3547),p=r(3532),y=r(3993),v=r(5292),g=r(7294),m=r(919),b=r(6762),x=r(5524),w=r(4638),O=r(4722),j=r(7672),S=r(6281),P=r.n(S),M=e([a,l,j]);function A(){let{data:e}=(0,O.useSession)(),[t,r]=(0,f.useState)(null),[n,c]=(0,f.useState)(!0);(0,a.useToast)();let u=['"Talk is cheap. Show me the code." – Linus Torvalds','"Programs must be written for people to read, and only incidentally for machines to execute." – Harold Abelson','"Any fool can write code that a computer can understand. Good programmers write code that humans can understand." – Martin Fowler','"First, solve the problem. Then, write the code." – John Johnson','"404 Chill Not Found? Keep calm and debug on." – Unknown',"It's not a bug – it's an undocumented feature.",'"The best error message is the one that never shows up." – Thomas Fuchs',"Code is like humor. When you have to explain it, it's bad.",'"Experience is the name everyone gives to their mistakes." – Oscar Wilde','"In order to be irreplaceable, one must always be different." – Coco Chanel'];u[new Date().getDate()%u.length];let S=s.H.filter(t=>"admin"===t.requiredRole?e?.user?.isAdmin:"moderator"===t.requiredRole?e?.user?.isModerator:!["overview","experimental","gameservers","applications","tickets"].includes(t.id)),M=t?[{name:"Text",value:t.serverStats.textChannels||0,color:"#4299E1"},{name:"Voice",value:t.serverStats.voiceChannels||0,color:"#48BB78"},{name:"Categories",value:t.serverStats.categories||0,color:"#9F7AEA"}]:[],A=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],E=A.map(e=>({day:e,commands:0,joins:0,leaves:0})),k=t?A.map(e=>{let r=t.botStats?.weeklyActivity?.find(t=>t.day===e)||{},n=t.serverStats?.weeklyMembers?.find(t=>t.day===e)||{};return{day:e,commands:r.commands||0,joins:n.joins||0,leaves:n.leaves||0}}):E;return(0,i.jsx)(l.A,{children:(0,i.jsxs)(a.Box,{p:8,position:"relative",_before:{content:'""',position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",width:"100%",height:"100%",background:"radial-gradient(circle at center, rgba(66, 153, 225, 0.1) 0%, transparent 70%)",pointerEvents:"none"},children:[(0,i.jsxs)(a.VStack,{spacing:8,mb:8,children:[(0,i.jsxs)(a.Heading,{size:"lg",textAlign:"center",display:"flex",alignItems:"center",justifyContent:"center",children:[(0,i.jsx)("span",{style:{marginRight:"0.5rem"},role:"img","aria-label":"chart",children:"\uD83D\uDCCA"}),(0,i.jsx)(a.Box,{as:"span",bgGradient:"linear(to-r, blue.300, purple.400)",bgClip:"text",children:"Server Analytics"})]}),(0,i.jsx)(a.SimpleGrid,{columns:{base:1,md:2,lg:4},spacing:6,w:"full",children:n?Array.from({length:4}).map((e,t)=>(0,i.jsx)(a.Card,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,i.jsx)(a.CardBody,{children:(0,i.jsx)(a.Skeleton,{height:"80px"})})},t)):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(a.Card,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,i.jsx)(a.CardBody,{children:(0,i.jsxs)(a.Stat,{children:[(0,i.jsxs)(a.HStack,{children:[(0,i.jsx)(a.Icon,{as:o.cfS,color:"blue.400",boxSize:6}),(0,i.jsx)(a.StatLabel,{color:"gray.300",children:"Total Members"})]}),(0,i.jsx)(a.StatNumber,{color:"white",fontSize:"2xl",children:t?.serverStats.totalMembers.toLocaleString()||"0"}),(0,i.jsxs)(a.StatHelpText,{color:"green.400",children:[(0,i.jsx)(a.Icon,{as:o.ARf,mr:1}),t?.serverStats.onlineMembers||"0"," online"]}),(0,i.jsxs)(a.StatHelpText,{color:"green.300",children:["+",t?.serverStats.newMembersToday||0," joined"]}),(0,i.jsxs)(a.StatHelpText,{color:"red.400",children:["-",t?.serverStats.leftMembersToday||0," left"]})]})})}),(0,i.jsx)(a.Card,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,i.jsx)(a.CardBody,{children:(0,i.jsxs)(a.Stat,{children:[(0,i.jsxs)(a.HStack,{children:[(0,i.jsx)(a.Icon,{as:o.mEP,color:"green.400",boxSize:6}),(0,i.jsx)(a.StatLabel,{color:"gray.300",children:"Channels"})]}),(0,i.jsx)(a.StatNumber,{color:"white",fontSize:"2xl",children:t?.serverStats.totalChannels||"0"}),(0,i.jsxs)(a.StatHelpText,{color:"gray.400",children:[t?.serverStats.totalRoles||"0"," roles"]})]})})}),(0,i.jsx)(a.Card,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,i.jsx)(a.CardBody,{children:(0,i.jsxs)(a.Stat,{children:[(0,i.jsxs)(a.HStack,{children:[(0,i.jsx)(a.Icon,{as:o.z1n,color:"purple.400",boxSize:6}),(0,i.jsx)(a.StatLabel,{color:"gray.300",children:"Commands Today"})]}),(0,i.jsx)(a.StatNumber,{color:"white",fontSize:"2xl",children:t?.botStats.commandsToday||"0"}),(0,i.jsxs)(a.StatHelpText,{color:"gray.400",children:[t?.botStats.responseTime||"0ms"," avg response"]})]})})}),(0,i.jsx)(a.Card,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,i.jsx)(a.CardBody,{children:(0,i.jsxs)(a.Stat,{children:[(0,i.jsxs)(a.HStack,{children:[(0,i.jsx)(a.Icon,{as:o.LIi,color:"orange.400",boxSize:6}),(0,i.jsx)(a.StatLabel,{color:"gray.300",children:"Bot Uptime"})]}),(0,i.jsx)(a.StatNumber,{color:"white",fontSize:"xl",children:t?.botStats.uptime||"Unknown"}),(0,i.jsxs)(a.StatHelpText,{color:"green.400",children:[t?.botStats.activeAddons||"0"," addons active"]}),(0,i.jsxs)(a.StatHelpText,{color:"red.400",children:[t?.botStats.inactiveAddons||"0"," addons inactive"]})]})})}),t?.botStats.errorsToday>0&&(0,i.jsx)(a.Link,{as:P(),href:"/admin/errors",_hover:{textDecoration:"none"},w:"full",children:(0,i.jsx)(a.Card,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",borderColor:"red.400",borderWidth:"1px",cursor:"pointer",_hover:{transform:"translateY(-4px)",boxShadow:"0 4px 12px rgba(0,0,0,0.2)",borderColor:"red.500"},children:(0,i.jsx)(a.CardBody,{children:(0,i.jsxs)(a.Stat,{children:[(0,i.jsxs)(a.HStack,{children:[(0,i.jsx)(a.Icon,{as:o.y3G,color:"red.400",boxSize:6}),(0,i.jsx)(a.StatLabel,{color:"gray.300",children:"Errors Today"})]}),(0,i.jsx)(a.StatNumber,{color:"red.400",fontSize:"2xl",children:t.botStats.errorsToday}),(0,i.jsx)(a.StatHelpText,{color:"red.300",children:"Needs attention"})]})})})})]})})]}),!n&&t&&(0,i.jsxs)(a.VStack,{spacing:8,mb:8,children:[(0,i.jsxs)(a.Heading,{size:"lg",textAlign:"center",display:"flex",alignItems:"center",justifyContent:"center",children:[(0,i.jsx)("span",{style:{marginRight:"0.5rem"},role:"img","aria-label":"graph",children:"\uD83D\uDCC8"}),(0,i.jsx)(a.Box,{as:"span",bgGradient:"linear(to-r, blue.300, purple.400)",bgClip:"text",children:"Activity Overview"})]}),(0,i.jsxs)(a.SimpleGrid,{columns:{base:1,lg:2},spacing:8,w:"full",children:[(0,i.jsx)(a.Card,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,i.jsx)(a.CardBody,{children:(0,i.jsxs)(a.VStack,{spacing:4,children:[(0,i.jsx)(a.Heading,{size:"md",color:"white",children:"Channel Distribution"}),(0,i.jsx)(a.Box,{h:"200px",w:"full",children:(0,i.jsx)(d.u,{width:"100%",height:"100%",children:(0,i.jsxs)(h.r,{children:[(0,i.jsx)(p.F,{data:M,cx:"50%",cy:"50%",innerRadius:40,outerRadius:80,paddingAngle:5,dataKey:"value",children:M.map((e,t)=>(0,i.jsx)(y.f,{fill:e.color},`cell-${t}`))}),(0,i.jsx)(v.m,{wrapperStyle:{backgroundColor:"transparent"},contentStyle:{backgroundColor:"rgba(26, 32, 44, 0.9)",border:"1px solid rgba(255,255,255,0.2)",borderRadius:"8px",color:"#fff"},itemStyle:{color:"#fff"},labelStyle:{color:"#fff"}})]})})}),(0,i.jsx)(a.HStack,{spacing:4,justify:"center",children:M.map((e,t)=>(0,i.jsxs)(a.HStack,{spacing:2,children:[(0,i.jsx)(a.Box,{w:"3",h:"3",bg:e.color,rounded:"full"}),(0,i.jsxs)(a.Text,{fontSize:"sm",color:"gray.300",children:[e.name,": ",e.value]})]},t))})]})})}),(0,i.jsx)(a.Card,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,i.jsx)(a.CardBody,{children:(0,i.jsxs)(a.VStack,{spacing:4,children:[(0,i.jsx)(a.Heading,{size:"md",color:"white",children:"Weekly Activity"}),(0,i.jsx)(a.Box,{h:"200px",w:"full",children:(0,i.jsx)(d.u,{width:"100%",height:"100%",children:(0,i.jsxs)(g.E,{data:k,children:[(0,i.jsx)(m.d,{strokeDasharray:"3 3",stroke:"rgba(255,255,255,0.1)"}),(0,i.jsx)(b.W,{dataKey:"day",axisLine:!1,tickLine:!1,tick:{fill:"#A0AEC0",fontSize:12}}),(0,i.jsx)(x.h,{axisLine:!1,tickLine:!1,tick:{fill:"#A0AEC0",fontSize:12}}),(0,i.jsx)(v.m,{wrapperStyle:{backgroundColor:"transparent"},contentStyle:{backgroundColor:"rgba(26, 32, 44, 0.9)",border:"1px solid rgba(255,255,255,0.2)",borderRadius:"8px",color:"#fff"},itemStyle:{color:"#fff"},labelStyle:{color:"#fff"},cursor:{fill:"rgba(255,255,255,0.08)"}}),(0,i.jsx)(w.y,{dataKey:"commands",fill:"#4299E1",name:"Commands"}),(0,i.jsx)(w.y,{dataKey:"joins",fill:"#48BB78",name:"Joins"}),(0,i.jsx)(w.y,{dataKey:"leaves",fill:"#E53E3E",name:"Leaves"})]})})})]})})})]})]}),(0,i.jsx)(a.Wrap,{spacing:"24px",justify:"start",children:S.map(e=>(0,i.jsx)(a.WrapItem,{flex:"1 0 260px",children:(0,i.jsx)(j.o,{...e})},e.id))})]})})}[a,l,j]=M.then?(await M)():M;let E=async e=>await (0,c.getServerSession)(e.req,e.res,u.N)?{props:{}}:{redirect:{destination:"/signin",permanent:!1}};n()}catch(e){n(e)}})},2915:(e,t,r)=>{"use strict";var n=r(2015);"function"==typeof Object.is&&Object.is,n.useSyncExternalStore,n.useRef,n.useEffect,n.useMemo,n.useDebugValue},3084:(e,t,r)=>{"use strict";r.d(t,{F0:()=>n,tQ:()=>a,um:()=>i});var n="data-recharts-item-index",i="data-recharts-item-data-key",a=60},3121:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(1442),i=r(776);t.cloneDeepWith=function(e,t){return n.cloneDeepWith(e,(r,a,o,l)=>{let c=t?.(r,a,o,l);if(null!=c)return c;if("object"==typeof e)switch(Object.prototype.toString.call(e)){case i.numberTag:case i.stringTag:case i.booleanTag:{let t=new e.constructor(e?.valueOf());return n.copyProperties(t,e),t}case i.argumentsTag:{let t={};return n.copyProperties(t,e),t.length=e.length,t[Symbol.iterator]=e[Symbol.iterator],t}default:return}})}},3207:(e,t,r)=>{"use strict";r.d(t,{n:()=>a});var n=r(2015),i=r(4504);function a(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",r=(0,n.useRef)((0,i.NF)(t)),a=(0,n.useRef)(e);return a.current!==e&&(r.current=(0,i.NF)(t),a.current=e),r.current}},3251:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var n=(0,r(2015).createContext)(null)},3296:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(7634),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.isKey=function(e,t){return!Array.isArray(e)&&(!!("number"==typeof e||"boolean"==typeof e||null==e||n.isSymbol(e))||"string"==typeof e&&(a.test(e)||!i.test(e))||null!=t&&Object.hasOwn(t,e))}},3361:(e,t,r)=>{"use strict";r.d(t,{JN:()=>n,_5:()=>i,eC:()=>l,gY:()=>a,hX:()=>s,iO:()=>c,lZ:()=>u,pH:()=>f,x3:()=>o});var n=e=>e.rootProps.maxBarSize,i=e=>e.rootProps.barGap,a=e=>e.rootProps.barCategoryGap,o=e=>e.rootProps.barSize,l=e=>e.rootProps.stackOffset,c=e=>e.options.chartName,u=e=>e.rootProps.syncId,s=e=>e.rootProps.syncMethod,f=e=>e.options.eventEmitter},3362:(e,t,r)=>{"use strict";r.d(t,{oM:()=>h,ZI:()=>f,gi:()=>d});var n=r(3411),i=r(9684),a=r(7580),o=r(7242),l=r(3401),c=(0,o.Mz)([l.HZ],e=>{if(e)return{top:e.top,bottom:e.bottom,left:e.left,right:e.right}}),u=r(5336),s=(0,o.Mz)([c,u.Lp,u.A$],(e,t,r)=>{if(e&&null!=t&&null!=r)return{x:e.left,y:e.top,width:Math.max(0,t-e.left-e.right),height:Math.max(0,r-e.top-e.bottom)}}),f=e=>{var t=(0,a.r)();return(0,i.G)(r=>(0,n.Gx)(r,"xAxis",e,t))},d=e=>{var t=(0,a.r)();return(0,i.G)(r=>(0,n.Gx)(r,"yAxis",e,t))},h=()=>(0,i.G)(s)},3385:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.flatten=function(e,t=1){let r=[],n=Math.floor(t),i=(e,t)=>{for(let a=0;a<e.length;a++){let o=e[a];Array.isArray(o)&&t<n?i(o,t+1):r.push(o)}};return i(e,0),r}},3401:(e,t,r)=>{"use strict";r.d(t,{c2:()=>g,HZ:()=>y,Ds:()=>v});var n=r(7242),i=r(7063),a=r.n(i),o=r(7206),l=r.n(o),c=e=>e.legend.settings;(0,n.Mz)([e=>e.legend.payload,c],(e,t)=>{var{itemSorter:r}=t,n=e.flat(1);return r?l()(n,r):n});var u=r(9058),s=r(5336),f=r(4305),d=r(3084);function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y=(0,n.Mz)([s.Lp,s.A$,s.HK,e=>e.brush.height,f.h,f.W,c,e=>e.legend.size],(e,t,r,n,i,o,l,c)=>{var s=o.reduce((e,t)=>{var{orientation:r}=t;if(!t.mirror&&!t.hide){var n="number"==typeof t.width?t.width:d.tQ;return p(p({},e),{},{[r]:e[r]+n})}return e},{left:r.left||0,right:r.right||0}),f=i.reduce((e,t)=>{var{orientation:r}=t;return t.mirror||t.hide?e:p(p({},e),{},{[r]:a()(e,"".concat(r))+t.height})},{top:r.top||0,bottom:r.bottom||0}),h=p(p({},f),s),y=h.bottom;h.bottom+=n;var v=e-(h=(0,u.s0)(h,l,c)).left-h.right,g=t-h.top-h.bottom;return p(p({brushBottom:y},h),{},{width:Math.max(v,0),height:Math.max(g,0)})}),v=(0,n.Mz)(y,e=>({x:e.left,y:e.top,width:e.width,height:e.height})),g=(0,n.Mz)(s.Lp,s.A$,(e,t)=>({x:0,y:0,width:e,height:t}))},3411:(e,t,r)=>{"use strict";r.d(t,{kz:()=>ia,fb:()=>n6,q:()=>iO,tP:()=>iT,g1:()=>iR,iv:()=>an,Nk:()=>n3,pM:()=>ir,Oz:()=>ix,tF:()=>at,rj:()=>n2,ec:()=>nX,bb:()=>iS,xp:()=>iz,wL:()=>iE,sr:()=>iD,Qn:()=>iI,MK:()=>ie,IO:()=>n0,P9:()=>ip,S5:()=>is,PU:()=>nB,cd:()=>nU,eo:()=>nV,yi:()=>id,ZB:()=>aa,D5:()=>iW,iV:()=>iq,Hd:()=>nZ,Gx:()=>ac,DP:()=>nW,BQ:()=>ae,_y:()=>as,AV:()=>iA,um:()=>nq,xM:()=>iN,gT:()=>iv,Kr:()=>ih,$X:()=>im,TC:()=>it,Zi:()=>ao,CR:()=>al,ld:()=>nY,L$:()=>i8,Rl:()=>nF,Lw:()=>i2,KR:()=>i9,sf:()=>nK,wP:()=>i7});var n,i,a,o,l,c,u,s={};r.r(s),r.d(s,{scaleBand:()=>w,scaleDiverging:()=>function e(){var t=eG(r6()(eA));return t.copy=function(){return r4(t,e())},y.apply(t,arguments)},scaleDivergingLog:()=>function e(){var t=eQ(r6()).domain([.1,1,10]);return t.copy=function(){return r4(t,e()).base(t.base())},y.apply(t,arguments)},scaleDivergingPow:()=>r8,scaleDivergingSqrt:()=>r9,scaleDivergingSymlog:()=>function e(){var t=e2(r6());return t.copy=function(){return r4(t,e()).constant(t.constant())},y.apply(t,arguments)},scaleIdentity:()=>function e(t){var r;function n(e){return null==e||isNaN(e*=1)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,eP),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,eP):[0,1],eG(n)},scaleImplicit:()=>b,scaleLinear:()=>function e(){var t=eD();return t.copy=function(){return eT(t,e())},p.apply(t,arguments),eG(t)},scaleLog:()=>function e(){let t=eQ(eC()).domain([1,10]);return t.copy=()=>eT(t,e()).base(t.base()),p.apply(t,arguments),t},scaleOrdinal:()=>x,scalePoint:()=>O,scalePow:()=>e8,scaleQuantile:()=>function e(){var t,r=[],n=[],i=[];function a(){var e=0,t=Math.max(1,n.length);for(i=Array(t-1);++e<t;)i[e-1]=function(e,t,r=N){if(!(!(n=e.length)||isNaN(t*=1))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e);return o+(r(e[a+1],a+1,e)-o)*(i-a)}}(r,e/t);return o}function o(e){return null==e||isNaN(e*=1)?t:n[z(i,e)]}return o.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?i[t-1]:r[0],t<i.length?i[t]:r[r.length-1]]},o.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t*=1)||r.push(t);return r.sort(_),a()},o.range=function(e){return arguments.length?(n=Array.from(e),a()):n.slice()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.quantiles=function(){return i.slice()},o.copy=function(){return e().domain(r).range(n).unknown(t)},p.apply(o,arguments)},scaleQuantize:()=>function e(){var t,r=0,n=1,i=1,a=[.5],o=[0,1];function l(e){return null!=e&&e<=e?o[z(a,e,0,i)]:t}function c(){var e=-1;for(a=Array(i);++e<i;)a[e]=((e+1)*n-(e-i)*r)/(i+1);return l}return l.domain=function(e){return arguments.length?([r,n]=e,r*=1,n*=1,c()):[r,n]},l.range=function(e){return arguments.length?(i=(o=Array.from(e)).length-1,c()):o.slice()},l.invertExtent=function(e){var t=o.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,a[0]]:t>=i?[a[i-1],n]:[a[t-1],a[t]]},l.unknown=function(e){return arguments.length&&(t=e),l},l.thresholds=function(){return a.slice()},l.copy=function(){return e().domain([r,n]).range(o).unknown(t)},p.apply(eG(l),arguments)},scaleRadial:()=>function e(){var t,r=eD(),n=[0,1],i=!1;function a(e){var n,a=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(a)?t:i?Math.round(a):a}return a.invert=function(e){return r.invert(e7(e))},a.domain=function(e){return arguments.length?(r.domain(e),a):r.domain()},a.range=function(e){return arguments.length?(r.range((n=Array.from(e,eP)).map(e7)),a):n.slice()},a.rangeRound=function(e){return a.range(e).round(!0)},a.round=function(e){return arguments.length?(i=!!e,a):i},a.clamp=function(e){return arguments.length?(r.clamp(e),a):r.clamp()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e(r.domain(),n).round(i).clamp(r.clamp()).unknown(t)},p.apply(a,arguments),eG(a)},scaleSequential:()=>function e(){var t=eG(r2()(eA));return t.copy=function(){return r4(t,e())},y.apply(t,arguments)},scaleSequentialLog:()=>function e(){var t=eQ(r2()).domain([1,10]);return t.copy=function(){return r4(t,e()).base(t.base())},y.apply(t,arguments)},scaleSequentialPow:()=>r3,scaleSequentialQuantile:()=>function e(){var t=[],r=eA;function n(e){if(null!=e&&!isNaN(e*=1))return r((z(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r*=1)||t.push(r);return t.sort(_),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t*=1)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n*=1)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t*=1))){if(t<=0||n<2)return tt(e);if(t>=1)return te(e);var n,i=(n-1)*t,a=Math.floor(i),o=te((function e(t,r,n=0,i=1/0,a){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(t.length-1,i)),!(n<=r&&r<=i))return t;for(a=void 0===a?tr:function(e=_){if(e===_)return tr;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(a);i>n;){if(i-n>600){let o=i-n+1,l=r-n+1,c=Math.log(o),u=.5*Math.exp(2*c/3),s=.5*Math.sqrt(c*u*(o-u)/o)*(l-o/2<0?-1:1),f=Math.max(n,Math.floor(r-l*u/o+s)),d=Math.min(i,Math.floor(r+(o-l)*u/o+s));e(t,r,f,d,a)}let o=t[r],l=n,c=i;for(tn(t,n,r),a(t[i],o)>0&&tn(t,n,i);l<c;){for(tn(t,l,c),++l,--c;0>a(t[l],o);)++l;for(;a(t[c],o)>0;)--c}0===a(t[n],o)?tn(t,n,c):tn(t,++c,i),c<=r&&(n=c+1),r<=c&&(i=c-1)}return t})(e,a).subarray(0,a+1));return o+(tt(e.subarray(a+1))-o)*(i-a)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},y.apply(n,arguments)},scaleSequentialSqrt:()=>r5,scaleSequentialSymlog:()=>function e(){var t=e2(r2());return t.copy=function(){return r4(t,e()).constant(t.constant())},y.apply(t,arguments)},scaleSqrt:()=>e9,scaleSymlog:()=>function e(){var t=e2(eC());return t.copy=function(){return eT(t,e()).constant(t.constant())},p.apply(t,arguments)},scaleThreshold:()=>function e(){var t,r=[.5],n=[0,1],i=1;function a(e){return null!=e&&e<=e?n[z(r,e,0,i)]:t}return a.domain=function(e){return arguments.length?(i=Math.min((r=Array.from(e)).length,n.length-1),a):r.slice()},a.range=function(e){return arguments.length?(n=Array.from(e),i=Math.min(r.length,n.length-1),a):n.slice()},a.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e().domain(r).range(n).unknown(t)},p.apply(a,arguments)},scaleTime:()=>r0,scaleUtc:()=>r1,tickFormat:()=>eH});var f=r(7242),d=r(7812),h=r.n(d);function p(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function y(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class v extends Map{constructor(e,t=m){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(g(this,e))}has(e){return super.has(g(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function g({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function m(e){return null!==e&&"object"==typeof e?e.valueOf():e}let b=Symbol("implicit");function x(){var e=new v,t=[],r=[],n=b;function i(i){let a=e.get(i);if(void 0===a){if(n!==b)return n;e.set(i,a=t.push(i)-1)}return r[a%r.length]}return i.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new v,r))e.has(n)||e.set(n,t.push(n)-1);return i},i.range=function(e){return arguments.length?(r=Array.from(e),i):r.slice()},i.unknown=function(e){return arguments.length?(n=e,i):n},i.copy=function(){return x(t,r).unknown(n)},p.apply(i,arguments),i}function w(){var e,t,r=x().unknown(void 0),n=r.domain,i=r.range,a=0,o=1,l=!1,c=0,u=0,s=.5;function f(){var r=n().length,f=o<a,d=f?o:a,h=f?a:o;e=(h-d)/Math.max(1,r-c+2*u),l&&(e=Math.floor(e)),d+=(h-d-e*(r-c))*s,t=e*(1-c),l&&(d=Math.round(d),t=Math.round(t));var p=(function(e,t,r){e*=1,t*=1,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((t-e)/r)),a=Array(i);++n<i;)a[n]=e+n*r;return a})(r).map(function(t){return d+e*t});return i(f?p.reverse():p)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([a,o]=e,a*=1,o*=1,f()):[a,o]},r.rangeRound=function(e){return[a,o]=e,a*=1,o*=1,l=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(l=!!e,f()):l},r.padding=function(e){return arguments.length?(c=Math.min(1,u=+e),f()):c},r.paddingInner=function(e){return arguments.length?(c=Math.min(1,e),f()):c},r.paddingOuter=function(e){return arguments.length?(u=+e,f()):u},r.align=function(e){return arguments.length?(s=Math.max(0,Math.min(1,e)),f()):s},r.copy=function(){return w(n(),[a,o]).round(l).paddingInner(c).paddingOuter(u).align(s)},p.apply(f(),arguments)}function O(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(w.apply(null,arguments).paddingInner(1))}let j=Math.sqrt(50),S=Math.sqrt(10),P=Math.sqrt(2);function M(e,t,r){let n,i,a,o=(t-e)/Math.max(0,r),l=Math.floor(Math.log10(o)),c=o/Math.pow(10,l),u=c>=j?10:c>=S?5:c>=P?2:1;return(l<0?(n=Math.round(e*(a=Math.pow(10,-l)/u)),i=Math.round(t*a),n/a<e&&++n,i/a>t&&--i,a=-a):(n=Math.round(e/(a=Math.pow(10,l)*u)),i=Math.round(t/a),n*a<e&&++n,i*a>t&&--i),i<n&&.5<=r&&r<2)?M(e,t,2*r):[n,i,a]}function A(e,t,r){if(t*=1,e*=1,!((r*=1)>0))return[];if(e===t)return[e];let n=t<e,[i,a,o]=n?M(t,e,r):M(e,t,r);if(!(a>=i))return[];let l=a-i+1,c=Array(l);if(n)if(o<0)for(let e=0;e<l;++e)c[e]=-((a-e)/o);else for(let e=0;e<l;++e)c[e]=(a-e)*o;else if(o<0)for(let e=0;e<l;++e)c[e]=-((i+e)/o);else for(let e=0;e<l;++e)c[e]=(i+e)*o;return c}function E(e,t,r){return M(e*=1,t*=1,r*=1)[2]}function k(e,t,r){t*=1,e*=1,r*=1;let n=t<e,i=n?E(t,e,r):E(e,t,r);return(n?-1:1)*(i<0?-(1/i):i)}function _(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function T(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function C(e){let t,r,n;function i(e,n,a=0,o=e.length){if(a<o){if(0!==t(n,n))return o;do{let t=a+o>>>1;0>r(e[t],n)?a=t+1:o=t}while(a<o)}return a}return 2!==e.length?(t=_,r=(t,r)=>_(e(t),r),n=(t,r)=>e(t)-r):(t=e===_||e===T?e:D,r=e,n=e),{left:i,center:function(e,t,r=0,a=e.length){let o=i(e,t,r,a-1);return o>r&&n(e[o-1],t)>-n(e[o],t)?o-1:o},right:function(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{let t=i+a>>>1;0>=r(e[t],n)?i=t+1:a=t}while(i<a)}return i}}}function D(){return 0}function N(e){return null===e?NaN:+e}let I=C(_),z=I.right;function L(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function R(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function $(){}I.left,C(N).center;var B="\\s*([+-]?\\d+)\\s*",F="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",U="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",K=/^#([0-9a-f]{3,8})$/,H=RegExp(`^rgb\\(${B},${B},${B}\\)$`),G=RegExp(`^rgb\\(${U},${U},${U}\\)$`),W=RegExp(`^rgba\\(${B},${B},${B},${F}\\)$`),Z=RegExp(`^rgba\\(${U},${U},${U},${F}\\)$`),q=RegExp(`^hsl\\(${F},${U},${U}\\)$`),V=RegExp(`^hsla\\(${F},${U},${U},${F}\\)$`),Y={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function J(){return this.rgb().formatHex()}function X(){return this.rgb().formatRgb()}function Q(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=K.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?ee(t):3===r?new en(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?et(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?et(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=H.exec(e))?new en(t[1],t[2],t[3],1):(t=G.exec(e))?new en(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=W.exec(e))?et(t[1],t[2],t[3],t[4]):(t=Z.exec(e))?et(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=q.exec(e))?eu(t[1],t[2]/100,t[3]/100,1):(t=V.exec(e))?eu(t[1],t[2]/100,t[3]/100,t[4]):Y.hasOwnProperty(e)?ee(Y[e]):"transparent"===e?new en(NaN,NaN,NaN,0):null}function ee(e){return new en(e>>16&255,e>>8&255,255&e,1)}function et(e,t,r,n){return n<=0&&(e=t=r=NaN),new en(e,t,r,n)}function er(e,t,r,n){var i;return 1==arguments.length?((i=e)instanceof $||(i=Q(i)),i)?new en((i=i.rgb()).r,i.g,i.b,i.opacity):new en:new en(e,t,r,null==n?1:n)}function en(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function ei(){return`#${ec(this.r)}${ec(this.g)}${ec(this.b)}`}function ea(){let e=eo(this.opacity);return`${1===e?"rgb(":"rgba("}${el(this.r)}, ${el(this.g)}, ${el(this.b)}${1===e?")":`, ${e})`}`}function eo(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function el(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function ec(e){return((e=el(e))<16?"0":"")+e.toString(16)}function eu(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new ef(e,t,r,n)}function es(e){if(e instanceof ef)return new ef(e.h,e.s,e.l,e.opacity);if(e instanceof $||(e=Q(e)),!e)return new ef;if(e instanceof ef)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,l=a-i,c=(a+i)/2;return l?(o=t===a?(r-n)/l+(r<n)*6:r===a?(n-t)/l+2:(t-r)/l+4,l/=c<.5?a+i:2-a-i,o*=60):l=c>0&&c<1?0:o,new ef(o,l,c,e.opacity)}function ef(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function ed(e){return(e=(e||0)%360)<0?e+360:e}function eh(e){return Math.max(0,Math.min(1,e||0))}function ep(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function ey(e,t,r,n,i){var a=e*e,o=a*e;return((1-3*e+3*a-o)*t+(4-6*a+3*o)*r+(1+3*e+3*a-3*o)*n+o*i)/6}L($,Q,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:J,formatHex:J,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return es(this).formatHsl()},formatRgb:X,toString:X}),L(en,er,R($,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new en(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new en(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new en(el(this.r),el(this.g),el(this.b),eo(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:ei,formatHex:ei,formatHex8:function(){return`#${ec(this.r)}${ec(this.g)}${ec(this.b)}${ec((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:ea,toString:ea})),L(ef,function(e,t,r,n){return 1==arguments.length?es(e):new ef(e,t,r,null==n?1:n)},R($,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new ef(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new ef(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new en(ep(e>=240?e-240:e+120,i,n),ep(e,i,n),ep(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new ef(ed(this.h),eh(this.s),eh(this.l),eo(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=eo(this.opacity);return`${1===e?"hsl(":"hsla("}${ed(this.h)}, ${100*eh(this.s)}%, ${100*eh(this.l)}%${1===e?")":`, ${e})`}`}}));let ev=e=>()=>e;function eg(e,t){var r,n,i=t-e;return i?(r=e,n=i,function(e){return r+e*n}):ev(isNaN(e)?t:e)}let em=function e(t){var r,n=1==(r=+t)?eg:function(e,t){var n,i,a;return t-e?(n=e,i=t,n=Math.pow(n,a=r),i=Math.pow(i,a)-n,a=1/a,function(e){return Math.pow(n+e*i,a)}):ev(isNaN(e)?t:e)};function i(e,t){var r=n((e=er(e)).r,(t=er(t)).r),i=n(e.g,t.g),a=n(e.b,t.b),o=eg(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=i(t),e.b=a(t),e.opacity=o(t),e+""}}return i.gamma=e,i}(1);function eb(e){return function(t){var r,n,i=t.length,a=Array(i),o=Array(i),l=Array(i);for(r=0;r<i;++r)n=er(t[r]),a[r]=n.r||0,o[r]=n.g||0,l[r]=n.b||0;return a=e(a),o=e(o),l=e(l),n.opacity=1,function(e){return n.r=a(e),n.g=o(e),n.b=l(e),n+""}}}eb(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),i=e[n],a=e[n+1],o=n>0?e[n-1]:2*i-a,l=n<t-1?e[n+2]:2*a-i;return ey((r-n/t)*t,o,i,a,l)}}),eb(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),i=e[(n+t-1)%t],a=e[n%t],o=e[(n+1)%t],l=e[(n+2)%t];return ey((r-n/t)*t,i,a,o,l)}});function ex(e,t){return e*=1,t*=1,function(r){return e*(1-r)+t*r}}var ew=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,eO=RegExp(ew.source,"g");function ej(e,t){var r,n,i=typeof t;return null==t||"boolean"===i?ev(t):("number"===i?ex:"string"===i?(n=Q(t))?(t=n,em):function(e,t){var r,n,i,a,o,l=ew.lastIndex=eO.lastIndex=0,c=-1,u=[],s=[];for(e+="",t+="";(i=ew.exec(e))&&(a=eO.exec(t));)(o=a.index)>l&&(o=t.slice(l,o),u[c]?u[c]+=o:u[++c]=o),(i=i[0])===(a=a[0])?u[c]?u[c]+=a:u[++c]=a:(u[++c]=null,s.push({i:c,x:ex(i,a)})),l=eO.lastIndex;return l<t.length&&(o=t.slice(l),u[c]?u[c]+=o:u[++c]=o),u.length<2?s[0]?(r=s[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=s.length,function(e){for(var r,n=0;n<t;++n)u[(r=s[n]).i]=r.x(e);return u.join("")})}:t instanceof Q?em:t instanceof Date?function(e,t){var r=new Date;return e*=1,t*=1,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,i=e?Math.min(n,e.length):0,a=Array(i),o=Array(n);for(r=0;r<i;++r)a[r]=ej(e[r],t[r]);for(;r<n;++r)o[r]=t[r];return function(e){for(r=0;r<i;++r)o[r]=a[r](e);return o}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},i={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=ej(e[r],t[r]):i[r]=t[r];return function(e){for(r in n)i[r]=n[r](e);return i}}:ex:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,i=t.slice();return function(a){for(r=0;r<n;++r)i[r]=e[r]*(1-a)+t[r]*a;return i}})(e,t)}function eS(e,t){return e*=1,t*=1,function(r){return Math.round(e*(1-r)+t*r)}}function eP(e){return+e}var eM=[0,1];function eA(e){return e}function eE(e,t){var r;return(t-=e*=1)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function ek(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=eE(i,n),a=r(o,a)):(n=eE(n,i),a=r(a,o)),function(e){return a(n(e))}}function e_(e,t,r){var n=Math.min(e.length,t.length)-1,i=Array(n),a=Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=eE(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(t){var r=z(e,t,1,n)-1;return a[r](i[r](t))}}function eT(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function eC(){var e,t,r,n,i,a,o=eM,l=eM,c=ej,u=eA;function s(){var e,t,r,c=Math.min(o.length,l.length);return u!==eA&&(e=o[0],t=o[c-1],e>t&&(r=e,e=t,t=r),u=function(r){return Math.max(e,Math.min(t,r))}),n=c>2?e_:ek,i=a=null,f}function f(t){return null==t||isNaN(t*=1)?r:(i||(i=n(o.map(e),l,c)))(e(u(t)))}return f.invert=function(r){return u(t((a||(a=n(l,o.map(e),ex)))(r)))},f.domain=function(e){return arguments.length?(o=Array.from(e,eP),s()):o.slice()},f.range=function(e){return arguments.length?(l=Array.from(e),s()):l.slice()},f.rangeRound=function(e){return l=Array.from(e),c=eS,s()},f.clamp=function(e){return arguments.length?(u=!!e||eA,s()):u!==eA},f.interpolate=function(e){return arguments.length?(c=e,s()):c},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,s()}}function eD(){return eC()(eA,eA)}var eN=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function eI(e){var t;if(!(t=eN.exec(e)))throw Error("invalid format: "+e);return new ez({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function ez(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function eL(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function eR(e){return(e=eL(Math.abs(e)))?e[1]:NaN}function e$(e,t){var r=eL(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}eI.prototype=ez.prototype,ez.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let eB={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>e$(100*e,t),r:e$,s:function(e,t){var r=eL(e,t);if(!r)return e+"";var i=r[0],a=r[1],o=a-(n=3*Math.max(-8,Math.min(8,Math.floor(a/3))))+1,l=i.length;return o===l?i:o>l?i+Array(o-l+1).join("0"):o>0?i.slice(0,o)+"."+i.slice(o):"0."+Array(1-o).join("0")+eL(e,Math.max(0,t+o-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function eF(e){return e}var eU=Array.prototype.map,eK=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function eH(e,t,r,n){var i,l,c,u=k(e,t,r);switch((n=eI(null==n?",f":n)).type){case"s":var s=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(c=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(eR(s)/3)))-eR(Math.abs(u))))||(n.precision=c),o(n,s);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(c=Math.max(0,eR(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(i=Math.abs(i=u)))-eR(i))+1)||(n.precision=c-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(c=Math.max(0,-eR(Math.abs(u))))||(n.precision=c-("%"===n.type)*2)}return a(n)}function eG(e){var t=e.domain;return e.ticks=function(e){var r=t();return A(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return eH(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,i,a=t(),o=0,l=a.length-1,c=a[o],u=a[l],s=10;for(u<c&&(i=c,c=u,u=i,i=o,o=l,l=i);s-- >0;){if((i=E(c,u,r))===n)return a[o]=c,a[l]=u,t(a);if(i>0)c=Math.floor(c/i)*i,u=Math.ceil(u/i)*i;else if(i<0)c=Math.ceil(c*i)/i,u=Math.floor(u*i)/i;else break;n=i}return e},e}function eW(e,t){e=e.slice();var r,n=0,i=e.length-1,a=e[n],o=e[i];return o<a&&(r=n,n=i,i=r,r=a,a=o,o=r),e[n]=t.floor(a),e[i]=t.ceil(o),e}function eZ(e){return Math.log(e)}function eq(e){return Math.exp(e)}function eV(e){return-Math.log(-e)}function eY(e){return-Math.exp(-e)}function eJ(e){return isFinite(e)?+("1e"+e):e<0?0:e}function eX(e){return(t,r)=>-e(-t,r)}function eQ(e){let t,r,n=e(eZ,eq),i=n.domain,o=10;function l(){var a,l;return t=(a=o)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),e=>Math.log(e)/a),r=10===(l=o)?eJ:l===Math.E?Math.exp:e=>Math.pow(l,e),i()[0]<0?(t=eX(t),r=eX(r),e(eV,eY)):e(eZ,eq),n}return n.base=function(e){return arguments.length?(o=+e,l()):o},n.domain=function(e){return arguments.length?(i(e),l()):i()},n.ticks=e=>{let n,a,l=i(),c=l[0],u=l[l.length-1],s=u<c;s&&([c,u]=[u,c]);let f=t(c),d=t(u),h=null==e?10:+e,p=[];if(!(o%1)&&d-f<h){if(f=Math.floor(f),d=Math.ceil(d),c>0){for(;f<=d;++f)for(n=1;n<o;++n)if(!((a=f<0?n/r(-f):n*r(f))<c)){if(a>u)break;p.push(a)}}else for(;f<=d;++f)for(n=o-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<c)){if(a>u)break;p.push(a)}2*p.length<h&&(p=A(c,u,h))}else p=A(f,d,Math.min(d-f,h)).map(r);return s?p.reverse():p},n.tickFormat=(e,i)=>{if(null==e&&(e=10),null==i&&(i=10===o?"s":","),"function"!=typeof i&&(o%1||null!=(i=eI(i)).precision||(i.trim=!0),i=a(i)),e===1/0)return i;let l=Math.max(1,o*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*o<o-.5&&(n*=o),n<=l?i(e):""}},n.nice=()=>i(eW(i(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function e0(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function e1(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function e2(e){var t=1,r=e(e0(1),e1(t));return r.constant=function(r){return arguments.length?e(e0(t=+r),e1(t)):t},eG(r)}function e4(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function e3(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function e5(e){return e<0?-e*e:e*e}function e6(e){var t=e(eA,eA),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(eA,eA):.5===r?e(e3,e5):e(e4(r),e4(1/r)):r},eG(t)}function e8(){var e=e6(eC());return e.copy=function(){return eT(e,e8()).exponent(e.exponent())},p.apply(e,arguments),e}function e9(){return e8.apply(null,arguments).exponent(.5)}function e7(e){return Math.sign(e)*e*e}function te(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function tt(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}a=(i=function(e){var t,r,i,a=void 0===e.grouping||void 0===e.thousands?eF:(t=eU.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var i=e.length,a=[],o=0,l=t[0],c=0;i>0&&l>0&&(c+l+1>n&&(l=Math.max(1,n-c)),a.push(e.substring(i-=l,i+l)),!((c+=l+1)>n));)l=t[o=(o+1)%t.length];return a.reverse().join(r)}),o=void 0===e.currency?"":e.currency[0]+"",l=void 0===e.currency?"":e.currency[1]+"",c=void 0===e.decimal?".":e.decimal+"",u=void 0===e.numerals?eF:(i=eU.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return i[+e]})}),s=void 0===e.percent?"%":e.percent+"",f=void 0===e.minus?"−":e.minus+"",d=void 0===e.nan?"NaN":e.nan+"";function h(e){var t=(e=eI(e)).fill,r=e.align,i=e.sign,h=e.symbol,p=e.zero,y=e.width,v=e.comma,g=e.precision,m=e.trim,b=e.type;"n"===b?(v=!0,b="g"):eB[b]||(void 0===g&&(g=12),m=!0,b="g"),(p||"0"===t&&"="===r)&&(p=!0,t="0",r="=");var x="$"===h?o:"#"===h&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",w="$"===h?l:/[%p]/.test(b)?s:"",O=eB[b],j=/[defgprs%]/.test(b);function S(e){var o,l,s,h=x,S=w;if("c"===b)S=O(e)+S,e="";else{var P=(e*=1)<0||1/e<0;if(e=isNaN(e)?d:O(Math.abs(e),g),m&&(e=function(e){e:for(var t,r=e.length,n=1,i=-1;n<r;++n)switch(e[n]){case".":i=t=n;break;case"0":0===i&&(i=n),t=n;break;default:if(!+e[n])break e;i>0&&(i=0)}return i>0?e.slice(0,i)+e.slice(t+1):e}(e)),P&&0==+e&&"+"!==i&&(P=!1),h=(P?"("===i?i:f:"-"===i||"("===i?"":i)+h,S=("s"===b?eK[8+n/3]:"")+S+(P&&"("===i?")":""),j){for(o=-1,l=e.length;++o<l;)if(48>(s=e.charCodeAt(o))||s>57){S=(46===s?c+e.slice(o+1):e.slice(o))+S,e=e.slice(0,o);break}}}v&&!p&&(e=a(e,1/0));var M=h.length+e.length+S.length,A=M<y?Array(y-M+1).join(t):"";switch(v&&p&&(e=a(A+e,A.length?y-S.length:1/0),A=""),r){case"<":e=h+e+S+A;break;case"=":e=h+A+e+S;break;case"^":e=A.slice(0,M=A.length>>1)+h+e+S+A.slice(M);break;default:e=A+h+e+S}return u(e)}return g=void 0===g?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,g)):Math.max(0,Math.min(20,g)),S.toString=function(){return e+""},S}return{format:h,formatPrefix:function(e,t){var r=h(((e=eI(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(eR(t)/3))),i=Math.pow(10,-n),a=eK[8+n/3];return function(e){return r(i*e)+a}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,o=i.formatPrefix;function tr(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:+(e>t))}function tn(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}let ti=new Date,ta=new Date;function to(e,t,r,n){function i(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return i.floor=t=>(e(t=new Date(+t)),t),i.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),i.round=e=>{let t=i(e),r=i.ceil(e);return e-t<r-e?t:r},i.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),i.range=(r,n,a)=>{let o,l=[];if(r=i.ceil(r),a=null==a?1:Math.floor(a),!(r<n)||!(a>0))return l;do l.push(o=new Date(+r)),t(r,a),e(r);while(o<r&&r<n);return l},i.filter=r=>to(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e)if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}),r&&(i.count=(t,n)=>(ti.setTime(+t),ta.setTime(+n),e(ti),e(ta),Math.floor(r(ti,ta))),i.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?i.filter(n?t=>n(t)%e==0:t=>i.count(0,t)%e==0):i:null),i}let tl=to(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);tl.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?to(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):tl:null,tl.range;let tc=to(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());tc.range;let tu=to(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());tu.range;let ts=to(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());ts.range;let tf=to(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());tf.range;let td=to(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());td.range;let th=to(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);th.range;let tp=to(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);tp.range;let ty=to(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function tv(e){return to(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}ty.range;let tg=tv(0),tm=tv(1),tb=tv(2),tx=tv(3),tw=tv(4),tO=tv(5),tj=tv(6);function tS(e){return to(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}tg.range,tm.range,tb.range,tx.range,tw.range,tO.range,tj.range;let tP=tS(0),tM=tS(1),tA=tS(2),tE=tS(3),tk=tS(4),t_=tS(5),tT=tS(6);tP.range,tM.range,tA.range,tE.range,tk.range,t_.range,tT.range;let tC=to(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());tC.range;let tD=to(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());tD.range;let tN=to(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());tN.every=e=>isFinite(e=Math.floor(e))&&e>0?to(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,tN.range;let tI=to(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function tz(e,t,r,n,i,a){let o=[[tc,1,1e3],[tc,5,5e3],[tc,15,15e3],[tc,30,3e4],[a,1,6e4],[a,5,3e5],[a,15,9e5],[a,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function l(t,r,n){let i=Math.abs(r-t)/n,a=C(([,,e])=>e).right(o,i);if(a===o.length)return e.every(k(t/31536e6,r/31536e6,n));if(0===a)return tl.every(Math.max(k(t,r,n),1));let[l,c]=o[i/o[a-1][2]<o[a][2]/i?a-1:a];return l.every(c)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let i=r&&"function"==typeof r.range?r:l(e,t,r),a=i?i.range(e,+t+1):[];return n?a.reverse():a},l]}tI.every=e=>isFinite(e=Math.floor(e))&&e>0?to(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,tI.range;let[tL,tR]=tz(tI,tD,tP,ty,td,ts),[t$,tB]=tz(tN,tC,tg,th,tf,tu);function tF(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function tU(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function tK(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var tH={"-":"",_:" ",0:"0"},tG=/^\s*\d+/,tW=/^%/,tZ=/[\\^$*+?|[\]().{}]/g;function tq(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?Array(r-a+1).join(t)+i:i)}function tV(e){return e.replace(tZ,"\\$&")}function tY(e){return RegExp("^(?:"+e.map(tV).join("|")+")","i")}function tJ(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function tX(e,t,r){var n=tG.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function tQ(e,t,r){var n=tG.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function t0(e,t,r){var n=tG.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function t1(e,t,r){var n=tG.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function t2(e,t,r){var n=tG.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function t4(e,t,r){var n=tG.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function t3(e,t,r){var n=tG.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function t5(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function t6(e,t,r){var n=tG.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function t8(e,t,r){var n=tG.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function t9(e,t,r){var n=tG.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function t7(e,t,r){var n=tG.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function re(e,t,r){var n=tG.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function rt(e,t,r){var n=tG.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function rr(e,t,r){var n=tG.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function rn(e,t,r){var n=tG.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function ri(e,t,r){var n=tG.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function ra(e,t,r){var n=tW.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function ro(e,t,r){var n=tG.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function rl(e,t,r){var n=tG.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function rc(e,t){return tq(e.getDate(),t,2)}function ru(e,t){return tq(e.getHours(),t,2)}function rs(e,t){return tq(e.getHours()%12||12,t,2)}function rf(e,t){return tq(1+th.count(tN(e),e),t,3)}function rd(e,t){return tq(e.getMilliseconds(),t,3)}function rh(e,t){return rd(e,t)+"000"}function rp(e,t){return tq(e.getMonth()+1,t,2)}function ry(e,t){return tq(e.getMinutes(),t,2)}function rv(e,t){return tq(e.getSeconds(),t,2)}function rg(e){var t=e.getDay();return 0===t?7:t}function rm(e,t){return tq(tg.count(tN(e)-1,e),t,2)}function rb(e){var t=e.getDay();return t>=4||0===t?tw(e):tw.ceil(e)}function rx(e,t){return e=rb(e),tq(tw.count(tN(e),e)+(4===tN(e).getDay()),t,2)}function rw(e){return e.getDay()}function rO(e,t){return tq(tm.count(tN(e)-1,e),t,2)}function rj(e,t){return tq(e.getFullYear()%100,t,2)}function rS(e,t){return tq((e=rb(e)).getFullYear()%100,t,2)}function rP(e,t){return tq(e.getFullYear()%1e4,t,4)}function rM(e,t){var r=e.getDay();return tq((e=r>=4||0===r?tw(e):tw.ceil(e)).getFullYear()%1e4,t,4)}function rA(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+tq(t/60|0,"0",2)+tq(t%60,"0",2)}function rE(e,t){return tq(e.getUTCDate(),t,2)}function rk(e,t){return tq(e.getUTCHours(),t,2)}function r_(e,t){return tq(e.getUTCHours()%12||12,t,2)}function rT(e,t){return tq(1+tp.count(tI(e),e),t,3)}function rC(e,t){return tq(e.getUTCMilliseconds(),t,3)}function rD(e,t){return rC(e,t)+"000"}function rN(e,t){return tq(e.getUTCMonth()+1,t,2)}function rI(e,t){return tq(e.getUTCMinutes(),t,2)}function rz(e,t){return tq(e.getUTCSeconds(),t,2)}function rL(e){var t=e.getUTCDay();return 0===t?7:t}function rR(e,t){return tq(tP.count(tI(e)-1,e),t,2)}function r$(e){var t=e.getUTCDay();return t>=4||0===t?tk(e):tk.ceil(e)}function rB(e,t){return e=r$(e),tq(tk.count(tI(e),e)+(4===tI(e).getUTCDay()),t,2)}function rF(e){return e.getUTCDay()}function rU(e,t){return tq(tM.count(tI(e)-1,e),t,2)}function rK(e,t){return tq(e.getUTCFullYear()%100,t,2)}function rH(e,t){return tq((e=r$(e)).getUTCFullYear()%100,t,2)}function rG(e,t){return tq(e.getUTCFullYear()%1e4,t,4)}function rW(e,t){var r=e.getUTCDay();return tq((e=r>=4||0===r?tk(e):tk.ceil(e)).getUTCFullYear()%1e4,t,4)}function rZ(){return"+0000"}function rq(){return"%"}function rV(e){return+e}function rY(e){return Math.floor(e/1e3)}function rJ(e){return new Date(e)}function rX(e){return e instanceof Date?+e:+new Date(+e)}function rQ(e,t,r,n,i,a,o,l,c,u){var s=eD(),f=s.invert,d=s.domain,h=u(".%L"),p=u(":%S"),y=u("%I:%M"),v=u("%I %p"),g=u("%a %d"),m=u("%b %d"),b=u("%B"),x=u("%Y");function w(e){return(c(e)<e?h:l(e)<e?p:o(e)<e?y:a(e)<e?v:n(e)<e?i(e)<e?g:m:r(e)<e?b:x)(e)}return s.invert=function(e){return new Date(f(e))},s.domain=function(e){return arguments.length?d(Array.from(e,rX)):d().map(rJ)},s.ticks=function(t){var r=d();return e(r[0],r[r.length-1],null==t?10:t)},s.tickFormat=function(e,t){return null==t?w:u(t)},s.nice=function(e){var r=d();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?d(eW(r,e)):s},s.copy=function(){return eT(s,rQ(e,t,r,n,i,a,o,l,c,u))},s}function r0(){return p.apply(rQ(t$,tB,tN,tC,tg,th,tf,tu,tc,c).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function r1(){return p.apply(rQ(tL,tR,tI,tD,tP,tp,td,ts,tc,u).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function r2(){var e,t,r,n,i,a=0,o=1,l=eA,c=!1;function u(t){return null==t||isNaN(t*=1)?i:l(0===r?.5:(t=(n(t)-e)*r,c?Math.max(0,Math.min(1,t)):t))}function s(e){return function(t){var r,n;return arguments.length?([r,n]=t,l=e(r,n),u):[l(0),l(1)]}}return u.domain=function(i){return arguments.length?([a,o]=i,e=n(a*=1),t=n(o*=1),r=e===t?0:1/(t-e),u):[a,o]},u.clamp=function(e){return arguments.length?(c=!!e,u):c},u.interpolator=function(e){return arguments.length?(l=e,u):l},u.range=s(ej),u.rangeRound=s(eS),u.unknown=function(e){return arguments.length?(i=e,u):i},function(i){return n=i,e=i(a),t=i(o),r=e===t?0:1/(t-e),u}}function r4(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function r3(){var e=e6(r2());return e.copy=function(){return r4(e,r3()).exponent(e.exponent())},y.apply(e,arguments)}function r5(){return r3.apply(null,arguments).exponent(.5)}function r6(){var e,t,r,n,i,a,o,l=0,c=.5,u=1,s=1,f=eA,d=!1;function h(e){return isNaN(e*=1)?o:(e=.5+((e=+a(e))-t)*(s*e<s*t?n:i),f(d?Math.max(0,Math.min(1,e)):e))}function p(e){return function(t){var r,n,i;return arguments.length?([r,n,i]=t,f=function(e,t){void 0===t&&(t=e,e=ej);for(var r=0,n=t.length-1,i=t[0],a=Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return a[t](e-t)}}(e,[r,n,i]),h):[f(0),f(.5),f(1)]}}return h.domain=function(o){return arguments.length?([l,c,u]=o,e=a(l*=1),t=a(c*=1),r=a(u*=1),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,h):[l,c,u]},h.clamp=function(e){return arguments.length?(d=!!e,h):d},h.interpolator=function(e){return arguments.length?(f=e,h):f},h.range=p(ej),h.rangeRound=p(eS),h.unknown=function(e){return arguments.length?(o=e,h):o},function(o){return a=o,e=o(l),t=o(c),r=o(u),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,h}}function r8(){var e=e6(r6());return e.copy=function(){return r4(e,r8()).exponent(e.exponent())},y.apply(e,arguments)}function r9(){return r8.apply(null,arguments).exponent(.5)}c=(l=function(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,l=e.months,c=e.shortMonths,u=tY(i),s=tJ(i),f=tY(a),d=tJ(a),h=tY(o),p=tJ(o),y=tY(l),v=tJ(l),g=tY(c),m=tJ(c),b={a:function(e){return o[e.getDay()]},A:function(e){return a[e.getDay()]},b:function(e){return c[e.getMonth()]},B:function(e){return l[e.getMonth()]},c:null,d:rc,e:rc,f:rh,g:rS,G:rM,H:ru,I:rs,j:rf,L:rd,m:rp,M:ry,p:function(e){return i[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:rV,s:rY,S:rv,u:rg,U:rm,V:rx,w:rw,W:rO,x:null,X:null,y:rj,Y:rP,Z:rA,"%":rq},x={a:function(e){return o[e.getUTCDay()]},A:function(e){return a[e.getUTCDay()]},b:function(e){return c[e.getUTCMonth()]},B:function(e){return l[e.getUTCMonth()]},c:null,d:rE,e:rE,f:rD,g:rH,G:rW,H:rk,I:r_,j:rT,L:rC,m:rN,M:rI,p:function(e){return i[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:rV,s:rY,S:rz,u:rL,U:rR,V:rB,w:rF,W:rU,x:null,X:null,y:rK,Y:rG,Z:rZ,"%":rq},w={a:function(e,t,r){var n=h.exec(t.slice(r));return n?(e.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=g.exec(t.slice(r));return n?(e.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return S(e,t,r,n)},d:t9,e:t9,f:ri,g:t3,G:t4,H:re,I:re,j:t7,L:rn,m:t8,M:rt,p:function(e,t,r){var n=u.exec(t.slice(r));return n?(e.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:t6,Q:ro,s:rl,S:rr,u:tQ,U:t0,V:t1,w:tX,W:t2,x:function(e,t,n){return S(e,r,t,n)},X:function(e,t,r){return S(e,n,t,r)},y:t3,Y:t4,Z:t5,"%":ra};function O(e,t){return function(r){var n,i,a,o=[],l=-1,c=0,u=e.length;for(r instanceof Date||(r=new Date(+r));++l<u;)37===e.charCodeAt(l)&&(o.push(e.slice(c,l)),null!=(i=tH[n=e.charAt(++l)])?n=e.charAt(++l):i="e"===n?" ":"0",(a=t[n])&&(n=a(r,i)),o.push(n),c=l+1);return o.push(e.slice(c,l)),o.join("")}}function j(e,t){return function(r){var n,i,a=tK(1900,void 0,1);if(S(a,e,r+="",0)!=r.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(1e3*a.s+("L"in a?a.L:0));if(!t||"Z"in a||(a.Z=0),"p"in a&&(a.H=a.H%12+12*a.p),void 0===a.m&&(a.m="q"in a?a.q:0),"V"in a){if(a.V<1||a.V>53)return null;"w"in a||(a.w=1),"Z"in a?(n=(i=(n=tU(tK(a.y,0,1))).getUTCDay())>4||0===i?tM.ceil(n):tM(n),n=tp.offset(n,(a.V-1)*7),a.y=n.getUTCFullYear(),a.m=n.getUTCMonth(),a.d=n.getUTCDate()+(a.w+6)%7):(n=(i=(n=tF(tK(a.y,0,1))).getDay())>4||0===i?tm.ceil(n):tm(n),n=th.offset(n,(a.V-1)*7),a.y=n.getFullYear(),a.m=n.getMonth(),a.d=n.getDate()+(a.w+6)%7)}else("W"in a||"U"in a)&&("w"in a||(a.w="u"in a?a.u%7:+("W"in a)),i="Z"in a?tU(tK(a.y,0,1)).getUTCDay():tF(tK(a.y,0,1)).getDay(),a.m=0,a.d="W"in a?(a.w+6)%7+7*a.W-(i+5)%7:a.w+7*a.U-(i+6)%7);return"Z"in a?(a.H+=a.Z/100|0,a.M+=a.Z%100,tU(a)):tF(a)}}function S(e,t,r,n){for(var i,a,o=0,l=t.length,c=r.length;o<l;){if(n>=c)return -1;if(37===(i=t.charCodeAt(o++))){if(!(a=w[(i=t.charAt(o++))in tH?t.charAt(o++):i])||(n=a(e,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(t,b),x.x=O(r,x),x.X=O(n,x),x.c=O(t,x),{format:function(e){var t=O(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=j(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=j(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,l.parse,u=l.utcFormat,l.utcParse;var r7=r(4397),ne=r(9058),nt=r(5101),nr=r(4504),nn=r(7463);function ni(e){if(Array.isArray(e)&&2===e.length){var[t,r]=e;if((0,nn.H)(t)&&(0,nn.H)(r))return!0}return!1}function na(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}var no=r(4963),nl=r.n(no),nc=e=>e,nu={},ns=e=>e===nu,nf=e=>function t(){return 0==arguments.length||1==arguments.length&&ns(arguments.length<=0?void 0:arguments[0])?t:e(...arguments)},nd=(e,t)=>1===e?t:nf(function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var a=n.filter(e=>e!==nu).length;return a>=e?t(...n):nd(e-a,nf(function(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];return t(...n.map(e=>ns(e)?r.shift():e),...r)}))}),nh=e=>nd(e.length,e),np=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},ny=nh((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map(e=>t[e]).map(e)),nv=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return nc;var n=t.reverse(),i=n[0],a=n.slice(1);return function(){return a.reduce((e,t)=>t(e),i(...arguments))}},ng=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),nm=e=>{var t=null,r=null;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return t&&i.every((e,r)=>{var n;return e===(null==(n=t)?void 0:n[r])})?r:(t=i,r=e(...i))}};function nb(e){var t;return 0===e?1:Math.floor(new(nl())(e).abs().log(10).toNumber())+1}function nx(e,t,r){for(var n=new(nl())(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}nh((e,t,r)=>{var n=+e;return n+r*(t-n)}),nh((e,t,r)=>{var n=t-e;return(r-e)/(n=n||1/0)}),nh((e,t,r)=>{var n=t-e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});var nw=e=>{var[t,r]=e,[n,i]=[t,r];return t>r&&([n,i]=[r,t]),[n,i]},nO=(e,t,r)=>{if(e.lte(0))return new(nl())(0);var n=nb(e.toNumber()),i=new(nl())(10).pow(n),a=e.div(i),o=1!==n?.05:.1,l=new(nl())(Math.ceil(a.div(o).toNumber())).add(r).mul(o).mul(i);return new(nl())(t?l.toNumber():Math.ceil(l.toNumber()))},nj=(e,t,r)=>{var n=new(nl())(1),i=new(nl())(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new(nl())(10).pow(nb(e)-1),i=new(nl())(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new(nl())(Math.floor(e)))}else 0===e?i=new(nl())(Math.floor((t-1)/2)):r||(i=new(nl())(Math.floor(e)));var o=Math.floor((t-1)/2);return nv(ny(e=>i.add(new(nl())(e-o).mul(n)).toNumber()),np)(0,t)},nS=function(e,t,r,n){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new(nl())(0),tickMin:new(nl())(0),tickMax:new(nl())(0)};var o=nO(new(nl())(t).sub(e).div(r-1),n,a),l=Math.ceil((i=e<=0&&t>=0?new(nl())(0):(i=new(nl())(e).add(t).div(2)).sub(new(nl())(i).mod(o))).sub(e).div(o).toNumber()),c=Math.ceil(new(nl())(t).sub(i).div(o).toNumber()),u=l+c+1;return u>r?nS(e,t,r,n,a+1):(u<r&&(c=t>0?c+(r-u):c,l=t>0?l:l+(r-u)),{step:o,tickMin:i.sub(new(nl())(l).mul(o)),tickMax:i.add(new(nl())(c).mul(o))})},nP=nm(function(e){var[t,r]=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(n,2),[o,l]=nw([t,r]);if(o===-1/0||l===1/0){var c=l===1/0?[o,...np(0,n-1).map(()=>1/0)]:[...np(0,n-1).map(()=>-1/0),l];return t>r?ng(c):c}if(o===l)return nj(o,n,i);var{step:u,tickMin:s,tickMax:f}=nS(o,l,a,i,0),d=nx(s,f.add(new(nl())(.1).mul(u)),u);return t>r?ng(d):d}),nM=nm(function(e,t){var[r,n]=e,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[a,o]=nw([r,n]);if(a===-1/0||o===1/0)return[r,n];if(a===o)return[a];var l=Math.max(t,2),c=nO(new(nl())(o).sub(a).div(l-1),i,0),u=[...nx(new(nl())(a),new(nl())(o),c),o];return!1===i&&(u=u.map(e=>Math.round(e))),r>n?ng(u):u}),nA=r(5336),nE=r(4305),nk=r(3401),n_=r(4335),nT=r(3361),nC=r(382),nD=r(5953),nN=r(5158),nI=r(639),nz=r(3084);function nL(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nR(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nL(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nL(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var n$=[0,"auto"],nB={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},nF=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return null==r?nB:r},nU={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:n$,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:nz.tQ},nK=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return null==r?nU:r},nH={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},nG=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return null==r?nH:r},nW=(e,t,r)=>{switch(t){case"xAxis":return nF(e,r);case"yAxis":return nK(e,r);case"zAxis":return nG(e,r);case"angleAxis":return(0,nC.Be)(e,r);case"radiusAxis":return(0,nC.Gl)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},nZ=(e,t,r)=>{switch(t){case"xAxis":return nF(e,r);case"yAxis":return nK(e,r);case"angleAxis":return(0,nC.Be)(e,r);case"radiusAxis":return(0,nC.Gl)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},nq=e=>e.graphicalItems.countOfBars>0;function nV(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var nY=e=>e.graphicalItems.cartesianItems,nJ=(0,f.Mz)([nD.N,nN.E],nV),nX=(e,t,r)=>e.filter(r).filter(e=>(null==t?void 0:t.includeHidden)===!0||!e.hide),nQ=(0,f.Mz)([nY,nW,nJ],nX),n0=e=>e.filter(e=>void 0===e.stackId),n1=(0,f.Mz)([nQ],n0),n2=e=>e.map(e=>e.data).filter(Boolean).flat(1),n4=(0,f.Mz)([nQ],n2),n3=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=t;return e.length>0?e:r.slice(n,i+1)},n5=(0,f.Mz)([n4,nt.HS],n3),n6=(e,t,r)=>(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,ne.kr)(e,t.dataKey)})):r.length>0?r.map(e=>e.dataKey).flatMap(t=>e.map(e=>({value:(0,ne.kr)(e,t)}))):e.map(e=>({value:e})),n8=(0,f.Mz)([n5,nW,nQ],n6);function n9(e,t){switch(e){case"xAxis":return"x"===t.direction;case"yAxis":return"y"===t.direction;default:return!1}}function n7(e){return e.filter(e=>(0,nr.vh)(e)||e instanceof Date).map(Number).filter(e=>!1===(0,nr.M8)(e))}var ie=(e,t,r)=>Object.fromEntries(Object.entries(t.reduce((e,t)=>(null==t.stackId||(null==e[t.stackId]&&(e[t.stackId]=[]),e[t.stackId].push(t)),e),{})).map(t=>{var[n,i]=t,a=i.map(e=>e.dataKey);return[n,{stackedData:(0,ne.yy)(e,a,r),graphicalItems:i}]})),it=(0,f.Mz)([n5,nQ,nT.eC],ie),ir=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:i}=t;if("zAxis"!==r){var a=(0,ne.Mk)(e,n,i);if(null==a||0!==a[0]||0!==a[1])return a}},ii=(0,f.Mz)([it,nt.LF,nD.N],ir),ia=(e,t,r,n)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var i,a,o=null==(i=r.errorBars)?void 0:i.filter(e=>n9(n,e)),l=(0,ne.kr)(e,null!=(a=t.dataKey)?a:r.dataKey);return{value:l,errorDomain:function(e,t,r){return!r||"number"!=typeof t||(0,nr.M8)(t)||!r.length?[]:n7(r.flatMap(r=>{var n,i,a=(0,ne.kr)(e,r.dataKey);if(Array.isArray(a)?[n,i]=a:n=i=a,(0,nn.H)(n)&&(0,nn.H)(i))return[t-n,t+i]}))}(e,l,o)}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,ne.kr)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]})),io=(0,f.Mz)(n5,nW,n1,nD.N,ia);function il(e){var{value:t}=e;if((0,nr.vh)(t)||t instanceof Date)return t}var ic=e=>{var t=n7(e.flatMap(e=>[e.value,e.errorDomain]).flat(1));if(0!==t.length)return[Math.min(...t),Math.max(...t)]},iu=(e,t,r)=>{var n=e.map(il).filter(e=>null!=e);return r&&(null==t.dataKey||t.allowDuplicatedCategory&&(0,nr.CG)(n))?h()(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))},is=e=>{var t;if(null==e||!("domain"in e))return n$;if(null!=e.domain)return e.domain;if(null!=e.ticks){if("number"===e.type){var r=n7(e.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===e.type)return e.ticks.map(String)}return null!=(t=null==e?void 0:e.domain)?t:n$},id=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.filter(Boolean);if(0!==n.length){var i=n.flat();return[Math.min(...i),Math.max(...i)]}},ih=e=>e.referenceElements.dots,ip=(e,t,r)=>e.filter(e=>"extendDomain"===e.ifOverflow).filter(e=>"xAxis"===t?e.xAxisId===r:e.yAxisId===r),iy=(0,f.Mz)([ih,nD.N,nN.E],ip),iv=e=>e.referenceElements.areas,ig=(0,f.Mz)([iv,nD.N,nN.E],ip),im=e=>e.referenceElements.lines,ib=(0,f.Mz)([im,nD.N,nN.E],ip),ix=(e,t)=>{var r=n7(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},iw=(0,f.Mz)(iy,nD.N,ix),iO=(e,t)=>{var r=n7(e.flatMap(e=>["xAxis"===t?e.x1:e.y1,"xAxis"===t?e.x2:e.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},ij=(0,f.Mz)([ig,nD.N],iO),iS=(e,t)=>{var r=n7(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},iP=(0,f.Mz)(ib,nD.N,iS),iM=(0,f.Mz)(iw,iP,ij,(e,t,r)=>id(e,r,t)),iA=(0,f.Mz)([nW],is),iE=(e,t,r,n,i)=>{var a=function(e,t){if(t&&"function"!=typeof e&&Array.isArray(e)&&2===e.length){var r,n,[i,a]=e;if((0,nn.H)(i))r=i;else if("function"==typeof i)return;if((0,nn.H)(a))n=a;else if("function"==typeof a)return;var o=[r,n];if(ni(o))return o}}(t,e.allowDataOverflow);return null!=a?a:function(e,t,r){if(r||null!=t){if("function"==typeof e&&null!=t)try{var n=e(t,r);if(ni(n))return na(n,t,r)}catch(e){}if(Array.isArray(e)&&2===e.length){var i,a,[o,l]=e;if("auto"===o)null!=t&&(i=Math.min(...t));else if((0,nr.Et)(o))i=o;else if("function"==typeof o)try{null!=t&&(i=o(null==t?void 0:t[0]))}catch(e){}else if("string"==typeof o&&ne.IH.test(o)){var c=ne.IH.exec(o);if(null==c||null==t)i=void 0;else{var u=+c[1];i=t[0]-u}}else i=null==t?void 0:t[0];if("auto"===l)null!=t&&(a=Math.max(...t));else if((0,nr.Et)(l))a=l;else if("function"==typeof l)try{null!=t&&(a=l(null==t?void 0:t[1]))}catch(e){}else if("string"==typeof l&&ne.qx.test(l)){var s=ne.qx.exec(l);if(null==s||null==t)a=void 0;else{var f=+s[1];a=t[1]+f}}else a=null==t?void 0:t[1];var d=[i,a];if(ni(d))return null==t?d:na(d,t,r)}}}(t,id(r,i,ic(n)),e.allowDataOverflow)},ik=(0,f.Mz)([nW,iA,ii,io,iM],iE),i_=[0,1],iT=(e,t,r,n,i,a,o)=>{if(null!=e&&null!=r&&0!==r.length){var{dataKey:l,type:c}=e,u=(0,ne._L)(t,a);return u&&null==l?h()(0,r.length):"category"===c?iu(n,e,u):"expand"===i?i_:o}},iC=(0,f.Mz)([nW,r7.fz,n5,n8,nT.eC,nD.N,ik],iT),iD=(e,t,r,n,i)=>{if(null!=e){var{scale:a,type:o}=e;if("auto"===a)return"radial"===t&&"radiusAxis"===i?"band":"radial"===t&&"angleAxis"===i?"linear":"category"===o&&n&&(n.indexOf("LineChart")>=0||n.indexOf("AreaChart")>=0||n.indexOf("ComposedChart")>=0&&!r)?"point":"category"===o?"band":"linear";if("string"==typeof a){var l="scale".concat((0,nr.Zb)(a));return l in s?l:"point"}}},iN=(0,f.Mz)([nW,r7.fz,nq,nT.iO,nD.N],iD);function iI(e,t,r,n){if(null!=r&&null!=n){if("function"==typeof e.scale)return e.scale.copy().domain(r).range(n);var i=function(e){if(null!=e){if(e in s)return s[e]();var t="scale".concat((0,nr.Zb)(e));if(t in s)return s[t]()}}(t);if(null!=i){var a=i.domain(r).range(n);return(0,ne.YB)(a),a}}}var iz=(e,t,r)=>{var n=is(t);if("auto"===r||"linear"===r){if(null!=t&&t.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&ni(e))return nP(e,t.tickCount,t.allowDecimals);if(null!=t&&t.tickCount&&"number"===t.type&&ni(e))return nM(e,t.tickCount,t.allowDecimals)}},iL=(0,f.Mz)([iC,nZ,iN],iz),iR=(e,t,r,n)=>"angleAxis"!==n&&(null==e?void 0:e.type)==="number"&&ni(t)&&Array.isArray(r)&&r.length>0?[Math.min(t[0],r[0]),Math.max(t[1],r[r.length-1])]:t,i$=(0,f.Mz)([nW,iC,iL,nD.N],iR),iB=(0,f.Mz)(n8,nW,(e,t)=>{if(t&&"number"===t.type){var r=1/0,n=Array.from(n7(e.map(e=>e.value))).sort((e,t)=>e-t);if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(0===i)return 1/0;for(var a=0;a<n.length-1;a++)r=Math.min(r,n[a+1]-n[a]);return r/i}}),iF=(0,f.Mz)(iB,r7.fz,nT.gY,nk.HZ,(e,t,r,n)=>n,(e,t,r,n,i)=>{if(!(0,nn.H)(e))return 0;var a="vertical"===t?n.height:n.width;if("gap"===i)return e*a/2;if("no-gap"===i){var o=(0,nr.F4)(r,e*a),l=e*a/2;return l-o-(l-o)/a*o}return 0}),iU=(0,f.Mz)(nF,(e,t)=>{var r=nF(e,t);return null==r||"string"!=typeof r.padding?0:iF(e,"xAxis",t,r.padding)},(e,t)=>{if(null==e)return{left:0,right:0};var r,n,{padding:i}=e;return"string"==typeof i?{left:t,right:t}:{left:(null!=(r=i.left)?r:0)+t,right:(null!=(n=i.right)?n:0)+t}}),iK=(0,f.Mz)(nK,(e,t)=>{var r=nK(e,t);return null==r||"string"!=typeof r.padding?0:iF(e,"yAxis",t,r.padding)},(e,t)=>{if(null==e)return{top:0,bottom:0};var r,n,{padding:i}=e;return"string"==typeof i?{top:t,bottom:t}:{top:(null!=(r=i.top)?r:0)+t,bottom:(null!=(n=i.bottom)?n:0)+t}}),iH=(0,f.Mz)([nk.HZ,iU,n_.U,n_.C,(e,t,r)=>r],(e,t,r,n,i)=>{var{padding:a}=n;return i?[a.left,r.width-a.right]:[e.left+t.left,e.left+e.width-t.right]}),iG=(0,f.Mz)([nk.HZ,r7.fz,iK,n_.U,n_.C,(e,t,r)=>r],(e,t,r,n,i,a)=>{var{padding:o}=i;return a?[n.height-o.bottom,o.top]:"horizontal"===t?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]}),iW=(e,t,r,n)=>{var i;switch(t){case"xAxis":return iH(e,r,n);case"yAxis":return iG(e,r,n);case"zAxis":return null==(i=nG(e,r))?void 0:i.range;case"angleAxis":return(0,nC.Cv)(e);case"radiusAxis":return(0,nC.Dc)(e,r);default:return}},iZ=(0,f.Mz)([nW,iW],nI.I),iq=(0,f.Mz)([nW,iN,i$,iZ],iI);function iV(e,t){return e.id<t.id?-1:+(e.id>t.id)}(0,f.Mz)(nQ,nD.N,(e,t)=>e.flatMap(e=>{var t;return null!=(t=e.errorBars)?t:[]}).filter(e=>n9(t,e)));var iY=(e,t)=>t,iJ=(e,t,r)=>r,iX=(0,f.Mz)(nE.h,iY,iJ,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(iV)),iQ=(0,f.Mz)(nE.W,iY,iJ,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(iV)),i0=(e,t)=>({width:e.width,height:t.height}),i1=(e,t)=>({width:"number"==typeof t.width?t.width:nz.tQ,height:e.height}),i2=(0,f.Mz)(nk.HZ,nF,i0),i4=(e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}},i3=(e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}},i5=(0,f.Mz)(nA.A$,nk.HZ,iX,iY,iJ,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=i0(t,r);null==a&&(a=i4(t,n,e));var c="top"===n&&!i||"bottom"===n&&i;o[r.id]=a-Number(c)*l.height,a+=(c?-1:1)*l.height}),o}),i6=(0,f.Mz)(nA.Lp,nk.HZ,iQ,iY,iJ,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=i1(t,r);null==a&&(a=i3(t,n,e));var c="left"===n&&!i||"right"===n&&i;o[r.id]=a-Number(c)*l.width,a+=(c?-1:1)*l.width}),o}),i8=(e,t)=>{var r=(0,nk.HZ)(e),n=nF(e,t);if(null!=n){var i=i5(e,n.orientation,n.mirror)[t];return null==i?{x:r.left,y:0}:{x:r.left,y:i}}},i9=(e,t)=>{var r=(0,nk.HZ)(e),n=nK(e,t);if(null!=n){var i=i6(e,n.orientation,n.mirror)[t];return null==i?{x:0,y:r.top}:{x:i,y:r.top}}},i7=(0,f.Mz)(nk.HZ,nK,(e,t)=>({width:"number"==typeof t.width?t.width:nz.tQ,height:e.height})),ae=(e,t,r)=>{switch(t){case"xAxis":return i2(e,r).width;case"yAxis":return i7(e,r).height;default:return}},at=(e,t,r,n)=>{if(null!=r){var{allowDuplicatedCategory:i,type:a,dataKey:o}=r,l=(0,ne._L)(e,n),c=t.map(e=>e.value);if(o&&l&&"category"===a&&i&&(0,nr.CG)(c))return c}},ar=(0,f.Mz)([r7.fz,n8,nW,nD.N],at),an=(e,t,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:a}=r;if((0,ne._L)(e,n)&&("number"===i||"auto"!==a))return t.map(e=>e.value)}},ai=(0,f.Mz)([r7.fz,n8,nZ,nD.N],an),aa=(0,f.Mz)([r7.fz,(e,t,r)=>{switch(t){case"xAxis":return nF(e,r);case"yAxis":return nK(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},iN,iq,ar,ai,iW,iL,nD.N],(e,t,r,n,i,a,o,l,c)=>{if(null==t)return null;var u=(0,ne._L)(e,c);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:c,categoricalDomain:a,duplicateDomain:i,isCategorical:u,niceTicks:l,range:o,realScaleType:r,scale:n}}),ao=(0,f.Mz)([r7.fz,nZ,iN,iq,iL,iW,ar,ai,nD.N],(e,t,r,n,i,a,o,l,c)=>{if(null!=t&&null!=n){var u=(0,ne._L)(e,c),{type:s,ticks:f,tickCount:d}=t,h="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,p="category"===s&&n.bandwidth?n.bandwidth()/h:0;p="angleAxis"===c&&null!=a&&a.length>=2?2*(0,nr.sA)(a[0]-a[1])*p:p;var y=f||i;return y?y.map((e,t)=>({index:t,coordinate:n(o?o.indexOf(e):e)+p,value:e,offset:p})).filter(e=>!(0,nr.M8)(e.coordinate)):u&&l?l.map((e,t)=>({coordinate:n(e)+p,value:e,index:t,offset:p})):n.ticks?n.ticks(d).map(e=>({coordinate:n(e)+p,value:e,offset:p})):n.domain().map((e,t)=>({coordinate:n(e)+p,value:o?o[e]:e,index:t,offset:p}))}}),al=(0,f.Mz)([r7.fz,nZ,iq,iW,ar,ai,nD.N],(e,t,r,n,i,a,o)=>{if(null!=t&&null!=r&&null!=n&&n[0]!==n[1]){var l=(0,ne._L)(e,o),{tickCount:c}=t,u=0;return(u="angleAxis"===o&&(null==n?void 0:n.length)>=2?2*(0,nr.sA)(n[0]-n[1])*u:u,l&&a)?a.map((e,t)=>({coordinate:r(e)+u,value:e,index:t,offset:u})):r.ticks?r.ticks(c).map(e=>({coordinate:r(e)+u,value:e,offset:u})):r.domain().map((e,t)=>({coordinate:r(e)+u,value:i?i[e]:e,index:t,offset:u}))}}),ac=(0,f.Mz)(nW,iq,(e,t)=>{if(null!=e&&null!=t)return nR(nR({},e),{},{scale:t})}),au=(0,f.Mz)([nW,iN,iC,iZ],iI);(0,f.Mz)((e,t,r)=>nG(e,r),au,(e,t)=>{if(null!=e&&null!=t)return nR(nR({},e),{},{scale:t})});var as=(0,f.Mz)([r7.fz,nE.h,nE.W],(e,t,r)=>{switch(e){case"horizontal":return t.some(e=>e.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(e=>e.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}})},3414:(e,t,r)=>{"use strict";r.d(t,{J:()=>ed});var n=r(2015);r(7645);var i=Symbol.for("react.forward_ref"),a=Symbol.for("react.memo");function o(e){return e.dependsOnOwnProps?!!e.dependsOnOwnProps:1!==e.length}var l={notify(){},get:()=>[]},c="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,u="undefined"!=typeof navigator&&"ReactNative"===navigator.product,s=c||u?n.useLayoutEffect:n.useEffect;function f(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}var d={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},h={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},p={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},y={[i]:{$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},[a]:p};function v(e){return function(e){if("object"==typeof e&&null!==e){let{$$typeof:t}=e;switch(t){case null:switch(e=e.type){case null:case null:case null:case null:case null:return e;default:switch(e=e&&e.$$typeof){case null:case i:case null:case a:case null:return e;default:return t}}case null:return t}}}(e)===a?p:y[e.$$typeof]||d}var g=Object.defineProperty,m=Object.getOwnPropertyNames,b=Object.getOwnPropertySymbols,x=Object.getOwnPropertyDescriptor,w=Object.getPrototypeOf,O=Object.prototype,j=Symbol.for("react-redux-context"),S="undefined"!=typeof globalThis?globalThis:{},P=function(){if(!n.createContext)return{};let e=S[j]??=new Map,t=e.get(n.createContext);return t||(t=n.createContext(null),e.set(n.createContext,t)),t}(),M=function(e){let{children:t,context:r,serverState:i,store:a}=e,o=n.useMemo(()=>{let e=function(e,t){let r,n=l,i=0,a=!1;function o(){s.onStateChange&&s.onStateChange()}function c(){if(i++,!r){let t,i;r=e.subscribe(o),t=null,i=null,n={clear(){t=null,i=null},notify(){let e=t;for(;e;)e.callback(),e=e.next},get(){let e=[],r=t;for(;r;)e.push(r),r=r.next;return e},subscribe(e){let r=!0,n=i={callback:e,next:null,prev:i};return n.prev?n.prev.next=n:t=n,function(){r&&null!==t&&(r=!1,n.next?n.next.prev=n.prev:i=n.prev,n.prev?n.prev.next=n.next:t=n.next)}}}}}function u(){i--,r&&0===i&&(r(),r=void 0,n.clear(),n=l)}let s={addNestedSub:function(e){c();let t=n.subscribe(e),r=!1;return()=>{r||(r=!0,t(),u())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:o,isSubscribed:function(){return a},trySubscribe:function(){a||(a=!0,c())},tryUnsubscribe:function(){a&&(a=!1,u())},getListeners:()=>n};return s}(a);return{store:a,subscription:e,getServerState:i?()=>i:void 0}},[a,i]),c=n.useMemo(()=>a.getState(),[a]);return s(()=>{let{subscription:e}=o;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),c!==a.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[o,c]),n.createElement((r||P).Provider,{value:o},t)},A=r(6912),E=r(2481),k=r(8208),_=r(3497),T=r(7202),C=r(8224),D=r(8934);function N(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}var I=r(1413),z=r(5437),L=r(7555),R=(0,E.Z0)({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=(0,L.ss)(e).dots.findIndex(e=>e===t.payload);-1!==r&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=(0,L.ss)(e).areas.findIndex(e=>e===t.payload);-1!==r&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=(0,L.ss)(e).lines.findIndex(e=>e===t.payload);-1!==r&&e.lines.splice(r,1)}}}),{addDot:$,removeDot:B,addArea:F,removeArea:U,addLine:K,removeLine:H}=R.actions,G=R.reducer,W={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},Z=(0,E.Z0)({name:"brush",initialState:W,reducers:{setBrushSettings:(e,t)=>null==t.payload?W:t.payload}}),{setBrushSettings:q}=Z.actions,V=Z.reducer,Y=r(123),J=r(192),X=(0,E.Z0)({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=(0,L.h4)(t.payload)},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=(0,L.h4)(t.payload)},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:Q,removeRadiusAxis:ee,addAngleAxis:et,removeAngleAxis:er}=X.actions,en=X.reducer,ei=r(344),ea=r(2428),eo=r(6942),el=r(6626),ec=(0,A.HY)({brush:V,cartesianAxis:I.CA,chartData:T.LV,graphicalItems:z.iZ,layout:C.Vp,legend:Y.CU,options:k.lJ,polarAxis:en,polarOptions:ei.J,referenceElements:G,rootProps:J.vE,tooltip:_.En}),eu=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return(0,E.U1)({reducer:ec,preloadedState:e,middleware:e=>e({serializableCheck:!1}).concat([D.YF.middleware,D.fP.middleware,ea.$7.middleware,eo.x.middleware,el.k.middleware]),devTools:{serialize:{replacer:N},name:"recharts-".concat(t)}})},es=r(7580),ef=r(3251);function ed(e){var{preloadedState:t,children:r,reduxStoreName:i}=e,a=(0,es.r)(),o=(0,n.useRef)(null);if(a)return r;null==o.current&&(o.current=eu(t,i));var l=ef.E;return n.createElement(M,{context:l,store:o.current},r)}},3497:(e,t,r)=>{"use strict";r.d(t,{E1:()=>v,En:()=>m,Ix:()=>l,ML:()=>h,Nt:()=>p,RD:()=>s,UF:()=>u,XB:()=>c,jF:()=>y,k_:()=>a,o4:()=>g,oP:()=>f,xS:()=>d});var n=r(2481),i=r(7555),a={active:!1,index:null,dataKey:void 0,coordinate:void 0},o=(0,n.Z0)({name:"tooltip",initialState:{itemInteraction:{click:a,hover:a},axisInteraction:{click:a,hover:a},keyboardInteraction:a,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push((0,i.h4)(t.payload))},removeTooltipEntrySettings(e,t){var r=(0,i.ss)(e).tooltipItemPayloads.indexOf((0,i.h4)(t.payload));r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:l,removeTooltipEntrySettings:c,setTooltipSettingsState:u,setActiveMouseOverItemIndex:s,mouseLeaveItem:f,mouseLeaveChart:d,setActiveClickItemIndex:h,setMouseOverAxisIndex:p,setMouseClickAxisIndex:y,setSyncInteraction:v,setKeyboardInteraction:g}=o.actions,m=o.reducer},3506:(e,t,r)=>{"use strict";r.d(t,{p:()=>a,v:()=>o});var n=r(2015),i=r(9684);function a(e){return(0,i.j)(),(0,n.useRef)(null),null}function o(e){return(0,i.j)(),null}r(5437),r(9058)},3532:(e,t,r)=>{"use strict";r.d(t,{F:()=>ej,L:()=>eg});var n=r(2015),i=r(7063),a=r.n(i),o=r(9486),l=r(7242),c=r(5101),u=r(3401),s=r(9058),f=r(3411),d=r(4397),h=r(5953),p=r(5158),y=r(3361),v=e=>e.graphicalItems.polarItems,g=(0,l.Mz)([h.N,p.E],f.eo),m=(0,l.Mz)([v,f.DP,g],f.ec),b=(0,l.Mz)([m],f.rj),x=(0,l.Mz)([b,c.z3],f.Nk),w=(0,l.Mz)([x,f.DP,m],f.fb),O=(0,l.Mz)([x,f.DP,m],(e,t,r)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var n;return{value:(0,s.kr)(e,null!=(n=t.dataKey)?n:r.dataKey),errorDomain:[]}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,s.kr)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]}))),j=()=>void 0,S=(0,l.Mz)([f.DP,f.AV,j,O,j],f.wL),P=(0,l.Mz)([f.DP,d.fz,x,w,y.eC,h.N,S],f.tP),M=(0,l.Mz)([P,f.DP,f.xM],f.xp);function A(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function E(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?A(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):A(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}(0,l.Mz)([f.DP,P,M,h.N],f.g1);var k=(e,t)=>t,_=[],T=(e,t,r)=>(null==r?void 0:r.length)===0?_:r,C=(0,l.Mz)([c.z3,k,T],(e,t,r)=>{var n,{chartData:i}=e;if((n=(null==t?void 0:t.data)!=null&&t.data.length>0?t.data:i)&&n.length||null==r||(n=r.map(e=>E(E({},t.presentationProps),e.props))),null!=n)return n}),D=(0,l.Mz)([C,k,T],(e,t,r)=>{if(null!=e)return e.map((e,n)=>{var i,a,o=(0,s.kr)(e,t.nameKey,t.name);return a=null!=r&&null!=(i=r[n])&&null!=(i=i.props)&&i.fill?r[n].props.fill:"object"==typeof e&&null!=e&&"fill"in e?e.fill:t.fill,{value:(0,s.uM)(o,t.dataKey),color:a,payload:e,type:t.legendType}})}),N=(0,l.Mz)([v,k],(e,t)=>{if(e.some(e=>"pie"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),I=(0,l.Mz)([C,N,T,u.HZ],(e,t,r,n)=>{if(null!=t&&null!=e)return eg({offset:n,pieSettings:t,displayedData:e,cells:r})}),z=r(9684),L=r(3506),R=r(7269),$=r(2764),B=r(1094),F=r(3993),U=r(2661),K=r(4056),H=r(8702),G=r(4504),W=r(9044),Z=r(2623),q=r(4964),V=r(5108),Y=r(6113),J=r(7171),X=r(3084),Q=r(3207),ee=r(4644),et=r(6326),er=["onMouseEnter","onClick","onMouseLeave"];function en(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ei(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?en(Object(r),!0).forEach(function(t){ea(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):en(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ea(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eo(){return(eo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function el(e){var t=(0,n.useMemo)(()=>(0,U.J9)(e,!1),[e]),r=(0,n.useMemo)(()=>(0,U.aS)(e.children,F.f),[e.children]),i=(0,n.useMemo)(()=>({name:e.name,nameKey:e.nameKey,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey,cx:e.cx,cy:e.cy,startAngle:e.startAngle,endAngle:e.endAngle,minAngle:e.minAngle,paddingAngle:e.paddingAngle,innerRadius:e.innerRadius,outerRadius:e.outerRadius,cornerRadius:e.cornerRadius,legendType:e.legendType,fill:e.fill,presentationProps:t}),[e.cornerRadius,e.cx,e.cy,e.data,e.dataKey,e.endAngle,e.innerRadius,e.minAngle,e.name,e.nameKey,e.outerRadius,e.paddingAngle,e.startAngle,e.tooltipType,e.legendType,e.fill,t]),a=(0,z.G)(e=>D(e,i,r));return n.createElement(J._,{legendPayload:a})}function ec(e){var{dataKey:t,nameKey:r,sectors:n,stroke:i,strokeWidth:a,fill:o,name:l,hide:c,tooltipType:u}=e;return{dataDefinedOnItem:null==n?void 0:n.map(e=>e.tooltipPayload),positions:null==n?void 0:n.map(e=>e.tooltipPosition),settings:{stroke:i,strokeWidth:a,fill:o,dataKey:t,nameKey:r,name:(0,s.uM)(l,t),hide:c,type:u,color:o,unit:""}}}var eu=(e,t)=>e>t?"start":e<t?"end":"middle",es=(e,t,r)=>"function"==typeof t?t(e):(0,G.F4)(t,r,.8*r),ef=(e,t,r)=>{var{top:n,left:i,width:a,height:o}=t,l=(0,H.lY)(a,o),c=i+(0,G.F4)(e.cx,a,a/2),u=n+(0,G.F4)(e.cy,o,o/2),s=(0,G.F4)(e.innerRadius,l,0);return{cx:c,cy:u,innerRadius:s,outerRadius:es(r,e.outerRadius,l),maxRadius:e.maxRadius||Math.sqrt(a*a+o*o)/2}},ed=(e,t)=>(0,G.sA)(t-e)*Math.min(Math.abs(t-e),360),eh=(e,t)=>{if(n.isValidElement(e))return n.cloneElement(e,t);if("function"==typeof e)return e(t);var r=(0,o.$)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return n.createElement($.I,eo({},t,{type:"linear",className:r}))},ep=(e,t,r)=>{if(n.isValidElement(e))return n.cloneElement(e,t);var i=r;if("function"==typeof e&&(i=e(t),n.isValidElement(i)))return i;var a=(0,o.$)("recharts-pie-label-text","boolean"!=typeof e&&"function"!=typeof e?e.className:"");return n.createElement(B.E,eo({},t,{alignmentBaseline:"middle",className:a}),i)};function ey(e){var{sectors:t,props:r,showLabels:i}=e,{label:a,labelLine:o,dataKey:l}=r;if(!i||!a||!t)return null;var c=(0,U.J9)(r,!1),u=(0,U.J9)(a,!1),f=(0,U.J9)(o,!1),d="object"==typeof a&&"offsetRadius"in a&&a.offsetRadius||20,h=t.map((e,t)=>{var r=(e.startAngle+e.endAngle)/2,i=(0,H.IZ)(e.cx,e.cy,e.outerRadius+d,r),h=ei(ei(ei(ei({},c),e),{},{stroke:"none"},u),{},{index:t,textAnchor:eu(i.x,e.cx)},i),p=ei(ei(ei(ei({},c),e),{},{fill:"none",stroke:e.fill},f),{},{index:t,points:[(0,H.IZ)(e.cx,e.cy,e.outerRadius,r),i],key:"line"});return n.createElement(R.W,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},o&&eh(o,p),ep(a,h,(0,s.kr)(e,l)))});return n.createElement(R.W,{className:"recharts-pie-labels"},h)}function ev(e){var{sectors:t,activeShape:r,inactiveShape:i,allOtherPieProps:a,showLabels:o}=e,l=(0,z.G)(Y.A2),{onMouseEnter:c,onClick:u,onMouseLeave:s}=a,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(a,er),d=(0,q.Cj)(c,a.dataKey),h=(0,q.Pg)(s),p=(0,q.Ub)(u,a.dataKey);return null==t?null:n.createElement(n.Fragment,null,t.map((e,o)=>{if((null==e?void 0:e.startAngle)===0&&(null==e?void 0:e.endAngle)===0&&1!==t.length)return null;var c=r&&String(o)===l,u=c?r:l?i:null,s=ei(ei({},e),{},{stroke:e.stroke,tabIndex:-1,[X.F0]:o,[X.um]:a.dataKey});return n.createElement(R.W,eo({tabIndex:-1,className:"recharts-pie-sector"},(0,W.XC)(f,e,o),{onMouseEnter:d(e,o),onMouseLeave:h(e,o),onClick:p(e,o),key:"sector-".concat(null==e?void 0:e.startAngle,"-").concat(null==e?void 0:e.endAngle,"-").concat(e.midAngle,"-").concat(o)}),n.createElement(Z.y,eo({option:u,isActive:c,shapeType:"sector"},s)))}),n.createElement(ey,{sectors:t,props:a,showLabels:o}))}function eg(e){var t,r,n,{pieSettings:i,displayedData:a,cells:o,offset:l}=e,{cornerRadius:c,startAngle:u,endAngle:f,dataKey:d,nameKey:h,tooltipType:p}=i,y=Math.abs(i.minAngle),v=ed(u,f),g=Math.abs(v),m=a.length<=1?0:null!=(t=i.paddingAngle)?t:0,b=a.filter(e=>0!==(0,s.kr)(e,d,0)).length,x=g-b*y-(g>=360?b:b-1)*m,w=a.reduce((e,t)=>{var r=(0,s.kr)(t,d,0);return e+((0,G.Et)(r)?r:0)},0);return w>0&&(r=a.map((e,t)=>{var r,a=(0,s.kr)(e,d,0),f=(0,s.kr)(e,h,t),g=ef(i,l,e),b=((0,G.Et)(a)?a:0)/w,O=ei(ei({},e),o&&o[t]&&o[t].props),j=(r=t?n.endAngle+(0,G.sA)(v)*m*(0!==a):u)+(0,G.sA)(v)*((0!==a?y:0)+b*x),S=(r+j)/2,P=(g.innerRadius+g.outerRadius)/2,M=[{name:f,value:a,payload:O,dataKey:d,type:p}],A=(0,H.IZ)(g.cx,g.cy,P,S);return n=ei(ei(ei(ei({},i.presentationProps),{},{percent:b,cornerRadius:c,name:f,tooltipPayload:M,midAngle:S,middleRadius:P,tooltipPosition:A},O),g),{},{value:(0,s.kr)(e,d),startAngle:r,endAngle:j,payload:O,paddingAngle:(0,G.sA)(v)*m})})),r}function em(e){var{props:t,previousSectorsRef:r}=e,{sectors:i,isAnimationActive:o,animationBegin:l,animationDuration:c,animationEasing:u,activeShape:s,inactiveShape:f,onAnimationStart:d,onAnimationEnd:h}=t,p=(0,Q.n)(t,"recharts-pie-"),y=r.current,[v,g]=(0,n.useState)(!0),m=(0,n.useCallback)(()=>{"function"==typeof h&&h(),g(!1)},[h]),b=(0,n.useCallback)(()=>{"function"==typeof d&&d(),g(!0)},[d]);return n.createElement(et.i,{begin:l,duration:c,isActive:o,easing:u,from:{t:0},to:{t:1},onAnimationStart:b,onAnimationEnd:m,key:p},e=>{var{t:o}=e,l=[],c=(i&&i[0]).startAngle;return i.forEach((e,t)=>{var r=y&&y[t],n=t>0?a()(e,"paddingAngle",0):0;if(r){var i=(0,G.Dj)(r.endAngle-r.startAngle,e.endAngle-e.startAngle),u=ei(ei({},e),{},{startAngle:c+n,endAngle:c+i(o)+n});l.push(u),c=u.endAngle}else{var{endAngle:s,startAngle:f}=e,d=(0,G.Dj)(0,s-f)(o),h=ei(ei({},e),{},{startAngle:c+n,endAngle:c+d+n});l.push(h),c=h.endAngle}}),r.current=l,n.createElement(R.W,null,n.createElement(ev,{sectors:l,activeShape:s,inactiveShape:f,allOtherPieProps:t,showLabels:!v}))})}function eb(e){var{sectors:t,isAnimationActive:r,activeShape:i,inactiveShape:a}=e,o=(0,n.useRef)(null),l=o.current;return r&&t&&t.length&&(!l||l!==t)?n.createElement(em,{props:e,previousSectorsRef:o}):n.createElement(ev,{sectors:t,activeShape:i,inactiveShape:a,allOtherPieProps:e,showLabels:!0})}function ex(e){var{hide:t,className:r,rootTabIndex:i}=e,a=(0,o.$)("recharts-pie",r);return t?null:n.createElement(R.W,{tabIndex:i,className:a},n.createElement(eb,e))}var ew={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!K.m.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function eO(e){var t=(0,ee.e)(e,ew),r=(0,n.useMemo)(()=>(0,U.aS)(e.children,F.f),[e.children]),i=(0,U.J9)(t,!1),a=(0,n.useMemo)(()=>({name:t.name,nameKey:t.nameKey,tooltipType:t.tooltipType,data:t.data,dataKey:t.dataKey,cx:t.cx,cy:t.cy,startAngle:t.startAngle,endAngle:t.endAngle,minAngle:t.minAngle,paddingAngle:t.paddingAngle,innerRadius:t.innerRadius,outerRadius:t.outerRadius,cornerRadius:t.cornerRadius,legendType:t.legendType,fill:t.fill,presentationProps:i}),[t.cornerRadius,t.cx,t.cy,t.data,t.dataKey,t.endAngle,t.innerRadius,t.minAngle,t.name,t.nameKey,t.outerRadius,t.paddingAngle,t.startAngle,t.tooltipType,t.legendType,t.fill,i]),o=(0,z.G)(e=>I(e,a,r));return n.createElement(n.Fragment,null,n.createElement(V.r,{fn:ec,args:ei(ei({},t),{},{sectors:o})}),n.createElement(ex,eo({},t,{sectors:o})))}class ej extends n.PureComponent{constructor(){super(...arguments),ea(this,"id",(0,G.NF)("recharts-pie-"))}render(){return n.createElement(n.Fragment,null,n.createElement(L.v,{data:this.props.data,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:0,radiusAxisId:0,stackId:void 0,barSize:void 0,type:"pie"}),n.createElement(el,this.props),n.createElement(eO,this.props),this.props.children)}}ea(ej,"displayName","Pie"),ea(ej,"defaultProps",ew)},3547:(e,t,r)=>{"use strict";r.d(t,{r:()=>x});var n=r(2015),i=r(8208),a=r(3414),o=r(9491),l=r(5239),c=r(672),u=r(9684);function s(e){return(0,u.j)(),null}r(344);var f=r(8352),d=r(4644),h=r(7463),p=["width","height","layout"];function y(){return(y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var v={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},g=(0,n.forwardRef)(function(e,t){var r,i=(0,d.e)(e.categoricalChartProps,v),{width:u,height:g,layout:m}=i,b=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,p);if(!(0,h.F)(u)||!(0,h.F)(g))return null;var{chartName:x,defaultTooltipEventType:w,validateTooltipEventTypes:O,tooltipPayloadSearcher:j}=e;return n.createElement(a.J,{preloadedState:{options:{chartName:x,defaultTooltipEventType:w,validateTooltipEventTypes:O,tooltipPayloadSearcher:j,eventEmitter:void 0}},reduxStoreName:null!=(r=i.id)?r:x},n.createElement(o.TK,{chartData:i.data}),n.createElement(l.s,{width:u,height:g,layout:m,margin:i.margin}),n.createElement(c.p,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),n.createElement(s,{cx:i.cx,cy:i.cy,startAngle:i.startAngle,endAngle:i.endAngle,innerRadius:i.innerRadius,outerRadius:i.outerRadius}),n.createElement(f.L,y({width:u,height:g},b,{ref:t})))}),m=["item"],b={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},x=(0,n.forwardRef)((e,t)=>{var r=(0,d.e)(e,b);return n.createElement(g,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:m,tooltipPayloadSearcher:i.uN,categoricalChartProps:r,ref:t})})},3667:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getSymbols=function(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}},3713:(e,t,r)=>{"use strict";r.d(t,{l3:()=>u,m7:()=>s}),r(2015);var n=r(9684),i=r(3361);new(r(9778)),r(8208),r(3497);var a=r(6973),o=r(6113);function l(e){return e.tooltip.syncInteraction}var c=r(4397);function u(){(0,n.j)(),(0,n.G)(i.lZ),(0,n.G)(i.pH),(0,n.j)(),(0,n.G)(i.hX),(0,n.G)(o.R4),(0,c.WX)(),(0,c.sk)(),(0,n.G)(e=>e.rootProps.className),(0,n.G)(i.lZ),(0,n.G)(i.pH),(0,n.j)()}function s(e,t,r,o,c,u){(0,n.G)(r=>(0,a.dp)(r,e,t)),(0,n.G)(i.pH),(0,n.G)(i.lZ),(0,n.G)(i.hX);var s=(0,n.G)(l);null==s||s.active}r(7202)},3762:(e,t,r)=>{"use strict";r.d(t,{N:()=>h});var n=r(5542),i=r.n(n);let a=require("next-auth/providers/discord");var o=r.n(a),l=r(9021),c=r(2115),u=r.n(c),s=r(3873);let f={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>s.resolve(process.cwd(),e)).find(e=>l.existsSync(e));if(!e){let t=s.resolve(__dirname,"../../../config.yml");l.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=l.readFileSync(e,"utf8");f=u().parse(t)}catch(e){process.exit(1)}let d={bot:{token:f.bot.token,clientId:f.bot.clientId,clientSecret:f.bot.clientSecret,guildId:f.bot.guildId,ticketCategoryId:f.bot.ticketCategoryId||null,ticketLogChannelId:f.bot.ticketLogChannelId||null,prefix:f.bot.prefix},dashboard:{admins:f.dashboard?.admins||[],adminRoleIds:f.dashboard?.adminRoleIds||[],session:{secret:f.dashboard?.session?.secret||f.bot.clientSecret}},database:{url:f.database.url,name:f.database.name,options:{maxPoolSize:f.database.options?.maxPoolSize||10,minPoolSize:f.database.options?.minPoolSize||1,maxIdleTimeMS:f.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:f.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:f.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:f.database.options?.connectTimeoutMS||1e4,retryWrites:f.database.options?.retryWrites!==!1,retryReads:f.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let h={providers:[o()({clientId:d.bot.clientId,clientSecret:d.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:r})=>(t&&r&&(e.accessToken=t.access_token||null,e.id=r.id||null),e),async session({session:e,token:t}){if(e?.user){let r=t.id||null,n=t.accessToken||null;e.user.id=r,e.user.accessToken=n;let i=!1;if(r)if((d.dashboard.admins||[]).includes(r))i=!0;else{let e=d.dashboard.adminRoleIds||[];if(e.length&&d.bot.token&&d.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${d.bot.guildId}/members/${r}`,{headers:{Authorization:`Bot ${d.bot.token}`}});if(t.ok){let r=await t.json();i=e.some(e=>r.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=i,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let r=new URL(t),n=`${r.protocol}//localhost${r.port?`:${r.port}`:""}`;return e.startsWith(t)||e.startsWith(n)?e:t}},secret:d.dashboard.session.secret||d.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}};i()(h)},3764:(e,t,r)=>{"use strict";r.d(t,{g:()=>u});var n=r(7242),i=r(4397),a=r(6113),o=r(3401),l=r(6973),c=r(382),u=(0,n.Mz)([(e,t)=>t,i.fz,c.D0,a.Re,a.gL,a.R4,l.r1,o.HZ],l.aX)},3873:e=>{"use strict";e.exports=require("path")},3993:(e,t,r)=>{"use strict";r.d(t,{f:()=>n});var n=e=>null;n.displayName="Cell"},4056:(e,t,r)=>{"use strict";r.d(t,{m:()=>n});var n={isSsr:!0}},4075:e=>{"use strict";e.exports=require("zlib")},4078:e=>{"use strict";e.exports=import("swr")},4115:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(1590);t.throttle=function(e,t=0,r={}){let{leading:i=!0,trailing:a=!0}=r;return n.debounce(e,t,{leading:i,maxWait:t,trailing:a})}},4305:(e,t,r)=>{"use strict";r.d(t,{W:()=>a,h:()=>i});var n=r(7242),i=(0,n.Mz)(e=>e.cartesianAxis.xAxis,e=>Object.values(e)),a=(0,n.Mz)(e=>e.cartesianAxis.yAxis,e=>Object.values(e))},4335:(e,t,r)=>{"use strict";r.d(t,{C:()=>l,U:()=>c});var n=r(7242),i=r(3401),a=r(5336),o=r(4504),l=e=>e.brush,c=(0,n.Mz)([l,i.HZ,a.HK],(e,t,r)=>({height:e.height,x:(0,o.Et)(e.x)?e.x:t.left,y:(0,o.Et)(e.y)?e.y:t.top+t.height+t.brushBottom-((null==r?void 0:r.bottom)||0),width:(0,o.Et)(e.width)?e.width:t.width}))},4351:(e,t,r)=>{"use strict";r.d(t,{J:()=>j,Z:()=>g});var n=r(2015),i=r(9486),a=r(1094),o=r(2661),l=r(4504),c=r(8702),u=r(4397),s=["offset"],f=["labelRef"];function d(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function y(){return(y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var v=e=>{var{value:t,formatter:r}=e,n=(0,l.uy)(e.children)?t:e.children;return"function"==typeof r?r(n):n},g=e=>null!=e&&"function"==typeof e,m=(e,t)=>(0,l.sA)(t-e)*Math.min(Math.abs(t-e),360),b=(e,t,r)=>{var a,o,{position:u,viewBox:s,offset:f,className:d}=e,{cx:h,cy:p,innerRadius:v,outerRadius:g,startAngle:b,endAngle:x,clockWise:w}=s,O=(v+g)/2,j=m(b,x),S=j>=0?1:-1;"insideStart"===u?(a=b+S*f,o=w):"insideEnd"===u?(a=x-S*f,o=!w):"end"===u&&(a=x+S*f,o=w),o=j<=0?o:!o;var P=(0,c.IZ)(h,p,O,a),M=(0,c.IZ)(h,p,O,a+(o?1:-1)*359),A="M".concat(P.x,",").concat(P.y,"\n    A").concat(O,",").concat(O,",0,1,").concat(+!o,",\n    ").concat(M.x,",").concat(M.y),E=(0,l.uy)(e.id)?(0,l.NF)("recharts-radial-line-"):e.id;return n.createElement("text",y({},r,{dominantBaseline:"central",className:(0,i.$)("recharts-radial-bar-label",d)}),n.createElement("defs",null,n.createElement("path",{id:E,d:A})),n.createElement("textPath",{xlinkHref:"#".concat(E)},t))},x=e=>{var{viewBox:t,offset:r,position:n}=e,{cx:i,cy:a,innerRadius:o,outerRadius:l,startAngle:u,endAngle:s}=t,f=(u+s)/2;if("outside"===n){var{x:d,y:h}=(0,c.IZ)(i,a,l+r,f);return{x:d,y:h,textAnchor:d>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var{x:p,y}=(0,c.IZ)(i,a,(o+l)/2,f);return{x:p,y,textAnchor:"middle",verticalAnchor:"middle"}},w=(e,t)=>{var{parentViewBox:r,offset:n,position:i}=e,{x:a,y:o,width:c,height:u}=t,s=u>=0?1:-1,f=s*n,d=s>0?"end":"start",h=s>0?"start":"end",y=c>=0?1:-1,v=y*n,g=y>0?"end":"start",m=y>0?"start":"end";if("top"===i)return p(p({},{x:a+c/2,y:o-s*n,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(o-r.y,0),width:c}:{});if("bottom"===i)return p(p({},{x:a+c/2,y:o+u+f,textAnchor:"middle",verticalAnchor:h}),r?{height:Math.max(r.y+r.height-(o+u),0),width:c}:{});if("left"===i){var b={x:a-v,y:o+u/2,textAnchor:g,verticalAnchor:"middle"};return p(p({},b),r?{width:Math.max(b.x-r.x,0),height:u}:{})}if("right"===i){var x={x:a+c+v,y:o+u/2,textAnchor:m,verticalAnchor:"middle"};return p(p({},x),r?{width:Math.max(r.x+r.width-x.x,0),height:u}:{})}var w=r?{width:c,height:u}:{};return"insideLeft"===i?p({x:a+v,y:o+u/2,textAnchor:m,verticalAnchor:"middle"},w):"insideRight"===i?p({x:a+c-v,y:o+u/2,textAnchor:g,verticalAnchor:"middle"},w):"insideTop"===i?p({x:a+c/2,y:o+f,textAnchor:"middle",verticalAnchor:h},w):"insideBottom"===i?p({x:a+c/2,y:o+u-f,textAnchor:"middle",verticalAnchor:d},w):"insideTopLeft"===i?p({x:a+v,y:o+f,textAnchor:m,verticalAnchor:h},w):"insideTopRight"===i?p({x:a+c-v,y:o+f,textAnchor:g,verticalAnchor:h},w):"insideBottomLeft"===i?p({x:a+v,y:o+u-f,textAnchor:m,verticalAnchor:d},w):"insideBottomRight"===i?p({x:a+c-v,y:o+u-f,textAnchor:g,verticalAnchor:d},w):i&&"object"==typeof i&&((0,l.Et)(i.x)||(0,l._3)(i.x))&&((0,l.Et)(i.y)||(0,l._3)(i.y))?p({x:a+(0,l.F4)(i.x,c),y:o+(0,l.F4)(i.y,u),textAnchor:"end",verticalAnchor:"end"},w):p({x:a+c/2,y:o+u/2,textAnchor:"middle",verticalAnchor:"middle"},w)},O=e=>"cx"in e&&(0,l.Et)(e.cx);function j(e){var t,{offset:r=5}=e,c=p({offset:r},d(e,s)),{viewBox:h,position:g,value:m,children:j,content:S,className:P="",textBreakAll:M,labelRef:A}=c,E=(0,u.sk)(),k=h||E;if(!k||(0,l.uy)(m)&&(0,l.uy)(j)&&!(0,n.isValidElement)(S)&&"function"!=typeof S)return null;if((0,n.isValidElement)(S)){var{labelRef:_}=c,T=d(c,f);return(0,n.cloneElement)(S,T)}if("function"==typeof S){if(t=(0,n.createElement)(S,c),(0,n.isValidElement)(t))return t}else t=v(c);var C=O(k),D=(0,o.J9)(c,!0);if(C&&("insideStart"===g||"insideEnd"===g||"end"===g))return b(c,t,D);var N=C?x(c):w(c,k);return n.createElement(a.E,y({ref:A,className:(0,i.$)("recharts-label",P)},D,N,{breakAll:M}),t)}j.displayName="Label";var S=e=>{var{cx:t,cy:r,angle:n,startAngle:i,endAngle:a,r:o,radius:c,innerRadius:u,outerRadius:s,x:f,y:d,top:h,left:p,width:y,height:v,clockWise:g,labelViewBox:m}=e;if(m)return m;if((0,l.Et)(y)&&(0,l.Et)(v)){if((0,l.Et)(f)&&(0,l.Et)(d))return{x:f,y:d,width:y,height:v};if((0,l.Et)(h)&&(0,l.Et)(p))return{x:h,y:p,width:y,height:v}}return(0,l.Et)(f)&&(0,l.Et)(d)?{x:f,y:d,width:0,height:0}:(0,l.Et)(t)&&(0,l.Et)(r)?{cx:t,cy:r,startAngle:i||n||0,endAngle:a||n||0,innerRadius:u||0,outerRadius:s||c||o||0,clockWise:g}:e.viewBox?e.viewBox:void 0},P=(e,t,r)=>{if(!e)return null;var i={viewBox:t,labelRef:r};return!0===e?n.createElement(j,y({key:"label-implicit"},i)):(0,l.vh)(e)?n.createElement(j,y({key:"label-implicit",value:e},i)):(0,n.isValidElement)(e)?e.type===j?(0,n.cloneElement)(e,p({key:"label-implicit"},i)):n.createElement(j,y({key:"label-implicit",content:e},i)):g(e)?n.createElement(j,y({key:"label-implicit",content:e},i)):e&&"object"==typeof e?n.createElement(j,y({},e,{key:"label-implicit"},i)):null};j.parseViewBox=S,j.renderCallByParent=function(e,t){var r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&r&&!e.label)return null;var{children:i,labelRef:a}=e,l=S(e),c=(0,o.aS)(i,j).map((e,r)=>(0,n.cloneElement)(e,{viewBox:t||l,key:"label-".concat(r)}));return r?[P(e.label,t||l,a),...c]:c}},4363:(e,t,r)=>{"use strict";r.d(t,{o:()=>n});var n=(e,t,r,n,i,a,o,l)=>{if(null!=a&&null!=l){var c=o[0],u=null==c?void 0:l(c.positions,a);if(null!=u)return u;var s=null==i?void 0:i[Number(a)];if(s)if("horizontal"===r)return{x:s.coordinate,y:(n.top+t)/2};else return{x:(n.left+e)/2,y:s.coordinate}}}},4390:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8221),i=r(4980),a=r(4521),o=r(1516);t.uniqBy=function(e,t=i.identity){return a.isArrayLikeObject(e)?n.uniqBy(Array.from(e),o.iteratee(t)):[]}},4397:(e,t,r)=>{"use strict";r.d(t,{W7:()=>s,WX:()=>p,fz:()=>h,rY:()=>d,sk:()=>c,yi:()=>f}),r(2015);var n=r(9684),i=r(3401),a=r(5336),o=r(7580),l=r(4335),c=()=>{var e,t=(0,o.r)(),r=(0,n.G)(i.Ds),a=(0,n.G)(l.U),c=null==(e=(0,n.G)(l.C))?void 0:e.padding;return t&&a&&c?{width:a.width-c.left-c.right,height:a.height-c.top-c.bottom,x:c.left,y:c.top}:r},u={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},s=()=>{var e;return null!=(e=(0,n.G)(i.HZ))?e:u},f=()=>(0,n.G)(a.Lp),d=()=>(0,n.G)(a.A$),h=e=>e.layout.layoutType,p=()=>(0,n.G)(h)},4504:(e,t,r)=>{"use strict";r.d(t,{CG:()=>h,Dj:()=>p,Et:()=>c,F4:()=>d,M8:()=>o,NF:()=>f,Zb:()=>g,_3:()=>l,eP:()=>y,sA:()=>a,uy:()=>v,vh:()=>u});var n=r(7063),i=r.n(n),a=e=>0===e?0:e>0?1:-1,o=e=>"number"==typeof e&&e!=+e,l=e=>"string"==typeof e&&e.indexOf("%")===e.length-1,c=e=>("number"==typeof e||e instanceof Number)&&!o(e),u=e=>c(e)||"string"==typeof e,s=0,f=e=>{var t=++s;return"".concat(e||"").concat(t)},d=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!c(e)&&"string"!=typeof e)return n;if(l(e)){if(null==t)return n;var a=e.indexOf("%");r=t*parseFloat(e.slice(0,a))/100}else r=+e;return o(r)&&(r=n),i&&null!=t&&r>t&&(r=t),r},h=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++)if(r[e[n]])return!0;else r[e[n]]=!0;return!1},p=(e,t)=>c(e)&&c(t)?r=>e+r*(t-e):()=>t;function y(e,t,r){if(e&&e.length)return e.find(e=>e&&("function"==typeof t?t(e):i()(e,t))===r)}var v=e=>null==e,g=e=>v(e)?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1))},4521:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(360),i=r(8098);t.isArrayLikeObject=function(e){return i.isObjectLike(e)&&n.isArrayLike(e)}},4638:(e,t,r)=>{"use strict";r.d(t,{y:()=>eq,L:()=>eZ});var n=r(2015),i=r(9486),a=r(7269),o=r(2661),l=r(3506),c=r(7580),u=["children"],s=()=>{},f=(0,n.createContext)({addErrorBar:s,removeErrorBar:s}),d=(0,n.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function h(e){var{children:t}=e,r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,u);return n.createElement(d.Provider,{value:r},t)}var p=()=>(0,n.useContext)(d),y=e=>{var{children:t,xAxisId:r,yAxisId:i,zAxisId:a,dataKey:o,data:u,stackId:s,hide:d,type:h,barSize:p}=e,[y,v]=n.useState([]),g=(0,n.useCallback)(e=>{v(t=>[...t,e])},[v]),m=(0,n.useCallback)(e=>{v(t=>t.filter(t=>t!==e))},[v]),b=(0,c.r)();return n.createElement(f.Provider,{value:{addErrorBar:g,removeErrorBar:m}},n.createElement(l.p,{type:h,data:u,xAxisId:r,yAxisId:i,zAxisId:a,dataKey:o,errorBars:y,stackId:s,hide:d,barSize:p,isPanorama:b}),t)};function v(e){var{addErrorBar:t,removeErrorBar:r}=(0,n.useContext)(f);return null}var g=r(3362),m=r(4644),b=r(6326),x=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function w(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function O(){return(O=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function j(e){var{direction:t,width:r,dataKey:i,isAnimationActive:l,animationBegin:c,animationDuration:u,animationEasing:s}=e,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,x),d=(0,o.J9)(f,!1),{data:h,dataPointFormatter:y,xAxisId:v,yAxisId:m,errorBarOffset:w}=p(),j=(0,g.ZI)(v),S=(0,g.gi)(m);if((null==j?void 0:j.scale)==null||(null==S?void 0:S.scale)==null||null==h||"x"===t&&"number"!==j.type)return null;var P=h.map(e=>{var o,f,{x:h,y:p,value:v,errorVal:g}=y(e,i,t);if(!g)return null;var m=[];if(Array.isArray(g)?[o,f]=g:o=f=g,"x"===t){var{scale:x}=j,P=p+w,M=P+r,A=P-r,E=x(v-o),k=x(v+f);m.push({x1:k,y1:M,x2:k,y2:A}),m.push({x1:E,y1:P,x2:k,y2:P}),m.push({x1:E,y1:M,x2:E,y2:A})}else if("y"===t){var{scale:_}=S,T=h+w,C=T-r,D=T+r,N=_(v-o),I=_(v+f);m.push({x1:C,y1:I,x2:D,y2:I}),m.push({x1:T,y1:N,x2:T,y2:I}),m.push({x1:C,y1:N,x2:D,y2:N})}var z="".concat(h+w,"px ").concat(p+w,"px");return n.createElement(a.W,O({className:"recharts-errorBar",key:"bar-".concat(m.map(e=>"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)))},d),m.map(e=>{var t=l?{transformOrigin:"".concat(e.x1-5,"px")}:void 0;return n.createElement(b.i,{from:{transform:"scaleY(0)",transformOrigin:z},to:{transform:"scaleY(1)",transformOrigin:z},begin:c,easing:s,isActive:l,duration:u,key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2),style:{transformOrigin:z}},n.createElement("line",O({},e,{style:t})))}))});return n.createElement(a.W,{className:"recharts-errorBars"},P)}var S=(0,n.createContext)(void 0);function P(e){var{direction:t,children:r}=e;return n.createElement(S.Provider,{value:t},r)}var M={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function A(e){var t,r,i=(t=e.direction,r=(0,n.useContext)(S),null!=t?t:null!=r?r:"x"),{width:a,isAnimationActive:o,animationBegin:l,animationDuration:c,animationEasing:u}=(0,m.e)(e,M);return n.createElement(n.Fragment,null,n.createElement(v,{dataKey:e.dataKey,direction:i}),n.createElement(j,O({},e,{direction:i,width:a,isAnimationActive:o,animationBegin:l,animationDuration:c,animationEasing:u})))}class E extends n.Component{render(){return n.createElement(A,this.props)}}w(E,"defaultProps",M),w(E,"displayName","ErrorBar");var k=r(3993),_=r(9213),T=r.n(_),C=r(4351),D=r(9058),N=r(4504),I=["valueAccessor"],z=["data","dataKey","clockWise","id","textBreakAll"];function L(){return(L=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function R(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function $(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?R(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):R(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function B(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var F=e=>Array.isArray(e.value)?T()(e.value):e.value;function U(e){var{valueAccessor:t=F}=e,r=B(e,I),{data:i,dataKey:l,clockWise:c,id:u,textBreakAll:s}=r,f=B(r,z);return i&&i.length?n.createElement(a.W,{className:"recharts-label-list"},i.map((e,r)=>{var i=(0,N.uy)(l)?t(e,r):(0,D.kr)(e&&e.payload,l),a=(0,N.uy)(u)?{}:{id:"".concat(u,"-").concat(r)};return n.createElement(C.J,L({},(0,o.J9)(e,!0),f,a,{parentViewBox:e.parentViewBox,value:i,textBreakAll:s,viewBox:C.J.parseViewBox((0,N.uy)(c)?e:$($({},e),{},{clockWise:c})),key:"label-".concat(r),index:r}))})):null}U.displayName="LabelList",U.renderCallByParent=function(e,t){var r,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&i&&!e.label)return null;var{children:a}=e,l=(0,o.aS)(a,U).map((e,r)=>(0,n.cloneElement)(e,{data:t,key:"labelList-".concat(r)}));return i?[(r=e.label,r?!0===r?n.createElement(U,{key:"labelList-implicit",data:t}):n.isValidElement(r)||(0,C.Z)(r)?n.createElement(U,{key:"labelList-implicit",data:t,content:r}):"object"==typeof r?n.createElement(U,L({data:t},r,{key:"labelList-implicit"})):null:null),...l]:l};var K=r(4056),H=r(9044),G=r(2623),W=["x","y"];function Z(){return(Z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function q(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function V(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?q(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):q(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Y(e,t){var{x:r,y:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,W),a=parseInt("".concat(r),10),o=parseInt("".concat(n),10),l=parseInt("".concat(t.height||i.height),10),c=parseInt("".concat(t.width||i.width),10);return V(V(V(V(V({},t),i),a?{x:a}:{}),o?{y:o}:{}),{},{height:l,width:c,name:t.name,radius:t.radius})}function J(e){return n.createElement(G.y,Z({shapeType:"rectangle",propTransformer:Y,activeClassName:"recharts-active-bar"},e))}var X=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(r,n)=>{if((0,N.Et)(e))return e;var i=(0,N.Et)(r)||(0,N.uy)(r);return i?e(r,n):(i||function(e,t){if(!e)throw Error("Invariant failed")}(!1),t)}},Q=r(4964),ee=r(5108),et=r(9684),er=r(5437),en=()=>{var e=(0,et.j)();return(0,n.useEffect)(()=>(e((0,er.lm)()),()=>{e((0,er.Ch)())})),null},ei=r(3411);function ea(e,t){var r,n,i=(0,et.G)(t=>(0,ei.Rl)(t,e)),a=(0,et.G)(e=>(0,ei.sf)(e,t)),o=null!=(r=null==i?void 0:i.allowDataOverflow)?r:ei.PU.allowDataOverflow,l=null!=(n=null==a?void 0:a.allowDataOverflow)?n:ei.cd.allowDataOverflow;return{needClip:o||l,needClipX:o,needClipY:l}}function eo(e){var{xAxisId:t,yAxisId:r,clipPathId:i}=e,a=(0,g.oM)(),{needClipX:o,needClipY:l,needClip:c}=ea(t,r);if(!c)return null;var{x:u,y:s,width:f,height:d}=a;return n.createElement("clipPath",{id:"clipPath-".concat(i)},n.createElement("rect",{x:o?u:u-f/2,y:l?s:s-d/2,width:o?f:2*f,height:l?d:2*d}))}var el=r(4397),ec=r(7242),eu=r(5101),es=r(3401),ef=r(3361),ed=r(7463);function eh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ep(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eh(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eh(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var ey=(e,t,r,n,i)=>i,ev=(e,t,r)=>{var n=null!=r?r:e;if(!(0,N.uy)(n))return(0,N.F4)(n,t,0)},eg=(0,ec.Mz)([el.fz,ei.ld,(e,t)=>t,(e,t,r)=>r,(e,t,r,n)=>n],(e,t,r,n,i)=>t.filter(t=>"horizontal"===e?t.xAxisId===r:t.yAxisId===n).filter(e=>e.isPanorama===i).filter(e=>!1===e.hide).filter(e=>"bar"===e.type));function em(e){return null!=e.stackId&&null!=e.dataKey}var eb=(0,ec.Mz)([eg,ef.x3,(e,t,r)=>"horizontal"===(0,el.fz)(e)?(0,ei.BQ)(e,"xAxis",t):(0,ei.BQ)(e,"yAxis",r)],(e,t,r)=>{var n=e.filter(em),i=e.filter(e=>null==e.stackId);return[...Object.entries(n.reduce((e,t)=>(e[t.stackId]||(e[t.stackId]=[]),e[t.stackId].push(t),e),{})).map(e=>{var[n,i]=e;return{stackId:n,dataKeys:i.map(e=>e.dataKey),barSize:ev(t,r,i[0].barSize)}}),...i.map(e=>({stackId:void 0,dataKeys:[e.dataKey].filter(e=>null!=e),barSize:ev(t,r,e.barSize)}))]}),ex=(e,t,r,n)=>{var i,a;return"horizontal"===(0,el.fz)(e)?(i=(0,ei.Gx)(e,"xAxis",t,n),a=(0,ei.CR)(e,"xAxis",t,n)):(i=(0,ei.Gx)(e,"yAxis",r,n),a=(0,ei.CR)(e,"yAxis",r,n)),(0,D.Hj)(i,a)},ew=(0,ec.Mz)([eb,ef.JN,ef._5,ef.gY,(e,t,r,n,i)=>{var a,o,l,c,u=(0,el.fz)(e),s=(0,ef.JN)(e),{maxBarSize:f}=i,d=(0,N.uy)(f)?s:f;return"horizontal"===u?(l=(0,ei.Gx)(e,"xAxis",t,n),c=(0,ei.CR)(e,"xAxis",t,n)):(l=(0,ei.Gx)(e,"yAxis",r,n),c=(0,ei.CR)(e,"yAxis",r,n)),null!=(a=null!=(o=(0,D.Hj)(l,c,!0))?o:d)?a:0},ex,(e,t,r,n,i)=>i.maxBarSize],(e,t,r,n,i,a,o)=>{var l=function(e,t,r,n,i){var a,o=n.length;if(!(o<1)){var l=(0,N.F4)(e,r,0,!0),c=[];if((0,ed.H)(n[0].barSize)){var u=!1,s=r/o,f=n.reduce((e,t)=>e+(t.barSize||0),0);(f+=(o-1)*l)>=r&&(f-=(o-1)*l,l=0),f>=r&&s>0&&(u=!0,s*=.9,f=o*s);var d={offset:((r-f)/2|0)-l,size:0};a=n.reduce((e,t)=>{var r,n=[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:d.offset+d.size+l,size:u?s:null!=(r=t.barSize)?r:0}}];return d=n[n.length-1].position,n},c)}else{var h=(0,N.F4)(t,r,0,!0);r-2*h-(o-1)*l<=0&&(l=0);var p=(r-2*h-(o-1)*l)/o;p>1&&(p>>=0);var y=(0,ed.H)(i)?Math.min(p,i):p;a=n.reduce((e,t,r)=>[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:h+(p+l)*r+(p-y)/2,size:y}}],c)}return a}}(r,n,i!==a?i:a,e,(0,N.uy)(o)?t:o);return i!==a&&null!=l&&(l=l.map(e=>ep(ep({},e),{},{position:ep(ep({},e.position),{},{offset:e.position.offset-i/2})}))),l}),eO=(0,ec.Mz)([ew,ey],(e,t)=>{if(null!=e){var r=e.find(e=>e.stackId===t.stackId&&e.dataKeys.includes(t.dataKey));if(null!=r)return r.position}}),ej=(0,ec.Mz)([ei.ld,ey],(e,t)=>{if(e.some(e=>"bar"===e.type&&t.dataKey===e.dataKey&&t.stackId===e.stackId&&t.stackId===e.stackId))return t}),eS=(0,ec.Mz)([(e,t,r,n)=>"horizontal"===(0,el.fz)(e)?(0,ei.TC)(e,"yAxis",r,n):(0,ei.TC)(e,"xAxis",t,n),ey],(e,t)=>{if(!e||(null==t?void 0:t.dataKey)==null)return;var{stackId:r}=t;if(null!=r){var n=e[r];if(n){var{stackedData:i}=n;if(i)return i.find(e=>e.key===t.dataKey)}}}),eP=(0,ec.Mz)([es.HZ,(e,t,r,n)=>(0,ei.Gx)(e,"xAxis",t,n),(e,t,r,n)=>(0,ei.Gx)(e,"yAxis",r,n),(e,t,r,n)=>(0,ei.CR)(e,"xAxis",t,n),(e,t,r,n)=>(0,ei.CR)(e,"yAxis",r,n),eO,el.fz,eu.HS,ex,eS,ej,(e,t,r,n,i,a)=>a],(e,t,r,n,i,a,o,l,c,u,s,f)=>{var d,{chartData:h,dataStartIndex:p,dataEndIndex:y}=l;if(null!=s&&null!=a&&("horizontal"===o||"vertical"===o)&&null!=t&&null!=r&&null!=n&&null!=i&&null!=c){var{data:v}=s;if(null!=(d=null!=v&&v.length>0?v:null==h?void 0:h.slice(p,y+1)))return eZ({layout:o,barSettings:s,pos:a,bandSize:c,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,stackedData:u,displayedData:d,offset:e,cells:f})}}),eM=r(6113),eA=r(7171),eE=r(3207),ek=["onMouseEnter","onMouseLeave","onClick"],e_=["value","background","tooltipPosition"],eT=["onMouseEnter","onClick","onMouseLeave"];function eC(){return(eC=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function eD(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eN(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eD(Object(r),!0).forEach(function(t){eI(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eD(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function eI(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ez(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var eL=e=>{var{dataKey:t,name:r,fill:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:(0,D.uM)(r,t),payload:e}]};function eR(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:i,name:a,hide:o,unit:l}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:i,dataKey:t,nameKey:void 0,name:(0,D.uM)(a,t),hide:o,type:e.tooltipType,color:e.fill,unit:l}}}function e$(e){var t=(0,et.G)(eM.A2),{data:r,dataKey:i,background:a,allOtherBarProps:l}=e,{onMouseEnter:c,onMouseLeave:u,onClick:s}=l,f=ez(l,ek),d=(0,Q.Cj)(c,i),h=(0,Q.Pg)(u),p=(0,Q.Ub)(s,i);if(!a||null==r)return null;var y=(0,o.J9)(a,!1);return n.createElement(n.Fragment,null,r.map((e,r)=>{var{value:o,background:l,tooltipPosition:c}=e,u=ez(e,e_);if(!l)return null;var s=d(e,r),v=h(e,r),g=p(e,r),m=eN(eN(eN(eN(eN({option:a,isActive:String(r)===t},u),{},{fill:"#eee"},l),y),(0,H.XC)(f,e,r)),{},{onMouseEnter:s,onMouseLeave:v,onClick:g,dataKey:i,index:r,className:"recharts-bar-background-rectangle"});return n.createElement(J,eC({key:"background-bar-".concat(r)},m))}))}function eB(e){var{data:t,props:r,showLabels:i}=e,l=(0,o.J9)(r,!1),{shape:c,dataKey:u,activeBar:s}=r,f=(0,et.G)(eM.A2),d=(0,et.G)(eM.Xb),{onMouseEnter:h,onClick:p,onMouseLeave:y}=r,v=ez(r,eT),g=(0,Q.Cj)(h,u),m=(0,Q.Pg)(y),b=(0,Q.Ub)(p,u);return t?n.createElement(n.Fragment,null,t.map((e,t)=>{var r=s&&String(t)===f&&(null==d||u===d),i=eN(eN(eN({},l),e),{},{isActive:r,option:r?s:c,index:t,dataKey:u});return n.createElement(a.W,eC({className:"recharts-bar-rectangle"},(0,H.XC)(v,e,t),{onMouseEnter:g(e,t),onMouseLeave:m(e,t),onClick:b(e,t),key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(t)}),n.createElement(J,i))}),i&&U.renderCallByParent(r,t)):null}function eF(e){var{props:t,previousRectanglesRef:r}=e,{data:i,layout:o,isAnimationActive:l,animationBegin:c,animationDuration:u,animationEasing:s,onAnimationEnd:f,onAnimationStart:d}=t,h=r.current,p=(0,eE.n)(t,"recharts-bar-"),[y,v]=(0,n.useState)(!1),g=(0,n.useCallback)(()=>{"function"==typeof f&&f(),v(!1)},[f]),m=(0,n.useCallback)(()=>{"function"==typeof d&&d(),v(!0)},[d]);return n.createElement(b.i,{begin:c,duration:u,isActive:l,easing:s,from:{t:0},to:{t:1},onAnimationEnd:g,onAnimationStart:m,key:p},e=>{var{t:l}=e,c=1===l?i:i.map((e,t)=>{var r=h&&h[t];if(r){var n=(0,N.Dj)(r.x,e.x),i=(0,N.Dj)(r.y,e.y),a=(0,N.Dj)(r.width,e.width),c=(0,N.Dj)(r.height,e.height);return eN(eN({},e),{},{x:n(l),y:i(l),width:a(l),height:c(l)})}if("horizontal"===o){var u=(0,N.Dj)(0,e.height)(l);return eN(eN({},e),{},{y:e.y+e.height-u,height:u})}var s=(0,N.Dj)(0,e.width)(l);return eN(eN({},e),{},{width:s})});return l>0&&(r.current=c),n.createElement(a.W,null,n.createElement(eB,{props:t,data:c,showLabels:!y}))})}function eU(e){var{data:t,isAnimationActive:r}=e,i=(0,n.useRef)(null);return r&&t&&t.length&&(null==i.current||i.current!==t)?n.createElement(eF,{previousRectanglesRef:i,props:e}):n.createElement(eB,{props:e,data:t,showLabels:!0})}var eK=(e,t)=>{var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:(0,D.kr)(e,t)}};class eH extends n.PureComponent{constructor(){super(...arguments),eI(this,"id",(0,N.NF)("recharts-bar-"))}render(){var{hide:e,data:t,dataKey:r,className:o,xAxisId:l,yAxisId:c,needClip:u,background:s,id:f,layout:d}=this.props;if(e)return null;var h=(0,i.$)("recharts-bar",o),p=(0,N.uy)(f)?this.id:f;return n.createElement(a.W,{className:h},u&&n.createElement("defs",null,n.createElement(eo,{clipPathId:p,xAxisId:l,yAxisId:c})),n.createElement(a.W,{className:"recharts-bar-rectangles",clipPath:u?"url(#clipPath-".concat(p,")"):null},n.createElement(e$,{data:t,dataKey:r,background:s,allOtherBarProps:this.props}),n.createElement(eU,this.props)),n.createElement(P,{direction:"horizontal"===d?"y":"x"},this.props.children))}}var eG={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!K.m.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function eW(e){var t,{xAxisId:r,yAxisId:i,hide:a,legendType:l,minPointSize:u,activeBar:s,animationBegin:f,animationDuration:d,animationEasing:p,isAnimationActive:y}=(0,m.e)(e,eG),{needClip:v}=ea(r,i),g=(0,el.WX)(),b=(0,c.r)(),x=(0,n.useMemo)(()=>({barSize:e.barSize,data:void 0,dataKey:e.dataKey,maxBarSize:e.maxBarSize,minPointSize:u,stackId:(0,D.$8)(e.stackId)}),[e.barSize,e.dataKey,e.maxBarSize,u,e.stackId]),w=(0,o.aS)(e.children,k.f),O=(0,et.G)(e=>eP(e,r,i,b,x,w));if("vertical"!==g&&"horizontal"!==g)return null;var j=null==O?void 0:O[0];return t=null==j||null==j.height||null==j.width?0:"vertical"===g?j.height/2:j.width/2,n.createElement(h,{xAxisId:r,yAxisId:i,data:O,dataPointFormatter:eK,errorBarOffset:t},n.createElement(eH,eC({},e,{layout:g,needClip:v,data:O,xAxisId:r,yAxisId:i,hide:a,legendType:l,minPointSize:u,activeBar:s,animationBegin:f,animationDuration:d,animationEasing:p,isAnimationActive:y})))}function eZ(e){var{layout:t,barSettings:{dataKey:r,minPointSize:n},pos:i,bandSize:a,xAxis:o,yAxis:l,xAxisTicks:c,yAxisTicks:u,stackedData:s,displayedData:f,offset:d,cells:h}=e,p="horizontal"===t?l:o,y=s?p.scale.domain():null,v=(0,D.DW)({numericAxis:p});return f.map((e,f)=>{s?g=(0,D._f)(s[f],y):Array.isArray(g=(0,D.kr)(e,r))||(g=[v,g]);var p=X(n,0)(g[1],f);if("horizontal"===t){var g,m,b,x,w,O,j,[S,P]=[l.scale(g[0]),l.scale(g[1])];m=(0,D.y2)({axis:o,ticks:c,bandSize:a,offset:i.offset,entry:e,index:f}),b=null!=(j=null!=P?P:S)?j:void 0,x=i.size;var M=S-P;if(w=(0,N.M8)(M)?0:M,O={x:m,y:d.top,width:x,height:d.height},Math.abs(p)>0&&Math.abs(w)<Math.abs(p)){var A=(0,N.sA)(w||p)*(Math.abs(p)-Math.abs(w));b-=A,w+=A}}else{var[E,k]=[o.scale(g[0]),o.scale(g[1])];if(m=E,b=(0,D.y2)({axis:l,ticks:u,bandSize:a,offset:i.offset,entry:e,index:f}),x=k-E,w=i.size,O={x:d.left,y:b,width:d.width,height:w},Math.abs(p)>0&&Math.abs(x)<Math.abs(p)){var _=(0,N.sA)(x||p)*(Math.abs(p)-Math.abs(x));x+=_}}return eN(eN({},e),{},{x:m,y:b,width:x,height:w,value:s?g:g[1],payload:e,background:O,tooltipPosition:{x:m+x/2,y:b+w/2}},h&&h[f]&&h[f].props)})}class eq extends n.PureComponent{render(){return n.createElement(y,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},n.createElement(en,null),n.createElement(eA.A,{legendPayload:eL(this.props)}),n.createElement(ee.r,{fn:eR,args:this.props}),n.createElement(eW,this.props))}}eI(eq,"displayName","Bar"),eI(eq,"defaultProps",eG)},4644:(e,t,r)=>{"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return Object.keys(t).reduce((e,r)=>(void 0===e[r]&&void 0!==t[r]&&(e[r]=t[r]),e),r)}r.d(t,{e:()=>i})},4682:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{config:()=>y,default:()=>f,getServerSideProps:()=>p,getStaticPaths:()=>h,getStaticProps:()=>d,reportWebVitals:()=>v,routeModule:()=>O,unstable_getServerProps:()=>x,unstable_getServerSideProps:()=>w,unstable_getStaticParams:()=>b,unstable_getStaticPaths:()=>m,unstable_getStaticProps:()=>g});var i=r(1292),a=r(8834),o=r(786),l=r(3567),c=r(8077),u=r(2878),s=e([c,u]);[c,u]=s.then?(await s)():s;let f=(0,o.M)(u,"default"),d=(0,o.M)(u,"getStaticProps"),h=(0,o.M)(u,"getStaticPaths"),p=(0,o.M)(u,"getServerSideProps"),y=(0,o.M)(u,"config"),v=(0,o.M)(u,"reportWebVitals"),g=(0,o.M)(u,"unstable_getStaticProps"),m=(0,o.M)(u,"unstable_getStaticPaths"),b=(0,o.M)(u,"unstable_getStaticParams"),x=(0,o.M)(u,"unstable_getServerProps"),w=(0,o.M)(u,"unstable_getServerSideProps"),O=new i.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/overview",pathname:"/overview",bundlePath:"",filename:""},components:{App:c.default,Document:l.default},userland:u});n()}catch(e){n(e)}})},4721:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var n=r(4504),i=(e,t)=>{var r,i=Number(t);if(!(0,n.M8)(i)&&null!=t)return i>=0?null==e||null==(r=e[i])?void 0:r.value:void 0}},4722:e=>{"use strict";e.exports=require("next-auth/react")},4817:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toPath=function(e){let t=[],r=e.length;if(0===r)return t;let n=0,i="",a="",o=!1;for(46===e.charCodeAt(0)&&(t.push(""),n++);n<r;){let l=e[n];a?"\\"===l&&n+1<r?i+=e[++n]:l===a?a="":i+=l:o?'"'===l||"'"===l?a=l:"]"===l?(o=!1,t.push(i),i=""):i+=l:"["===l?(o=!0,i&&(t.push(i),i="")):"."===l?i&&(t.push(i),i=""):i+=l,n++}return i&&t.push(i),t}},4919:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(9952);t.property=function(e){return function(t){return n.get(t,e)}}},4963:function(e,t,r){var n;!function(i){"use strict";var a,o={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},l=!0,c="[DecimalError] ",u=c+"Invalid argument: ",s=c+"Exponent out of range: ",f=Math.floor,d=Math.pow,h=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,p=f(1286742750677284.5),y={};function v(e,t){var r,n,i,a,o,c,u,s,f=e.constructor,d=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),l?M(t,d):t;if(u=e.d,s=t.d,o=e.e,i=t.e,u=u.slice(),a=o-i){for(a<0?(n=u,a=-a,c=s.length):(n=s,i=o,c=u.length),a>(c=(o=Math.ceil(d/7))>c?o+1:c+1)&&(a=c,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for((c=u.length)-(a=s.length)<0&&(a=c,n=s,s=u,u=n),r=0;a;)r=(u[--a]=u[a]+s[a]+r)/1e7|0,u[a]%=1e7;for(r&&(u.unshift(r),++i),c=u.length;0==u[--c];)u.pop();return t.d=u,t.e=i,l?M(t,d):t}function g(e,t,r){if(e!==~~e||e<t||e>r)throw Error(u+e)}function m(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)(r=7-(n=e[t]+"").length)&&(a+=j(r)),a+=n;(r=7-(n=(o=e[t])+"").length)&&(a+=j(r))}else if(0===o)return"0";for(;o%10==0;)o/=10;return a+o}y.absoluteValue=y.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},y.comparedTo=y.cmp=function(e){var t,r,n,i;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(i=e.d.length)?n:i;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},y.decimalPlaces=y.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},y.dividedBy=y.div=function(e){return b(this,new this.constructor(e))},y.dividedToIntegerBy=y.idiv=function(e){var t=this.constructor;return M(b(this,new t(e),0,1),t.precision)},y.equals=y.eq=function(e){return!this.cmp(e)},y.exponent=function(){return w(this)},y.greaterThan=y.gt=function(e){return this.cmp(e)>0},y.greaterThanOrEqualTo=y.gte=function(e){return this.cmp(e)>=0},y.isInteger=y.isint=function(){return this.e>this.d.length-2},y.isNegative=y.isneg=function(){return this.s<0},y.isPositive=y.ispos=function(){return this.s>0},y.isZero=function(){return 0===this.s},y.lessThan=y.lt=function(e){return 0>this.cmp(e)},y.lessThanOrEqualTo=y.lte=function(e){return 1>this.cmp(e)},y.logarithm=y.log=function(e){var t,r=this.constructor,n=r.precision,i=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(a))throw Error(c+"NaN");if(this.s<1)throw Error(c+(this.s?"NaN":"-Infinity"));return this.eq(a)?new r(0):(l=!1,t=b(S(this,i),S(e,i),i),l=!0,M(t,n))},y.minus=y.sub=function(e){return e=new this.constructor(e),this.s==e.s?A(this,e):v(this,(e.s=-e.s,e))},y.modulo=y.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(c+"NaN");return this.s?(l=!1,t=b(this,e,0,1).times(e),l=!0,this.minus(t)):M(new r(this),n)},y.naturalExponential=y.exp=function(){return x(this)},y.naturalLogarithm=y.ln=function(){return S(this)},y.negated=y.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},y.plus=y.add=function(e){return e=new this.constructor(e),this.s==e.s?v(this,e):A(this,(e.s=-e.s,e))},y.precision=y.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(u+e);if(t=w(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},y.squareRoot=y.sqrt=function(){var e,t,r,n,i,a,o,u=this.constructor;if(this.s<1){if(!this.s)return new u(0);throw Error(c+"NaN")}for(e=w(this),l=!1,0==(i=Math.sqrt(+this))||i==1/0?(((t=m(this.d)).length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=f((e+1)/2)-(e<0||e%2),n=new u(t=i==1/0?"5e"+e:(t=i.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new u(i.toString()),i=o=(r=u.precision)+3;;)if(n=(a=n).plus(b(this,a,o+2)).times(.5),m(a.d).slice(0,o)===(t=m(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&"4999"==t){if(M(a,r+1,0),a.times(a).eq(this)){n=a;break}}else if("9999"!=t)break;o+=4}return l=!0,M(n,r)},y.times=y.mul=function(e){var t,r,n,i,a,o,c,u,s,f=this.constructor,d=this.d,h=(e=new f(e)).d;if(!this.s||!e.s)return new f(0);for(e.s*=this.s,r=this.e+e.e,(u=d.length)<(s=h.length)&&(a=d,d=h,h=a,o=u,u=s,s=o),a=[],n=o=u+s;n--;)a.push(0);for(n=s;--n>=0;){for(t=0,i=u+n;i>n;)c=a[i]+h[n]*d[i-n-1]+t,a[i--]=c%1e7|0,t=c/1e7|0;a[i]=(a[i]+t)%1e7|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,l?M(e,f.precision):e},y.toDecimalPlaces=y.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(g(e,0,1e9),void 0===t?t=n.rounding:g(t,0,8),M(r,e+w(r)+1,t))},y.toExponential=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=E(n,!0):(g(e,0,1e9),void 0===t?t=i.rounding:g(t,0,8),r=E(n=M(new i(n),e+1,t),!0,e+1)),r},y.toFixed=function(e,t){var r,n,i=this.constructor;return void 0===e?E(this):(g(e,0,1e9),void 0===t?t=i.rounding:g(t,0,8),r=E((n=M(new i(this),e+w(this)+1,t)).abs(),!1,e+w(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},y.toInteger=y.toint=function(){var e=this.constructor;return M(new e(this),w(this)+1,e.rounding)},y.toNumber=function(){return+this},y.toPower=y.pow=function(e){var t,r,n,i,o,u,s=this,d=s.constructor,h=+(e=new d(e));if(!e.s)return new d(a);if(!(s=new d(s)).s){if(e.s<1)throw Error(c+"Infinity");return s}if(s.eq(a))return s;if(n=d.precision,e.eq(a))return M(s,n);if(u=(t=e.e)>=(r=e.d.length-1),o=s.s,u){if((r=h<0?-h:h)<=0x1fffffffffffff){for(i=new d(a),t=Math.ceil(n/7+4),l=!1;r%2&&k((i=i.times(s)).d,t),0!==(r=f(r/2));)k((s=s.times(s)).d,t);return l=!0,e.s<0?new d(a).div(i):M(i,n)}}else if(o<0)throw Error(c+"NaN");return o=o<0&&1&e.d[Math.max(t,r)]?-1:1,s.s=1,l=!1,i=e.times(S(s,n+12)),l=!0,(i=x(i)).s=o,i},y.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return void 0===e?(r=w(i),n=E(i,r<=a.toExpNeg||r>=a.toExpPos)):(g(e,1,1e9),void 0===t?t=a.rounding:g(t,0,8),r=w(i=M(new a(i),e,t)),n=E(i,e<=r||r<=a.toExpNeg,e)),n},y.toSignificantDigits=y.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(g(e,1,1e9),void 0===t?t=r.rounding:g(t,0,8)),M(new r(this),e,t)},y.toString=y.valueOf=y.val=y.toJSON=function(){var e=w(this),t=this.constructor;return E(this,e<=t.toExpNeg||e>=t.toExpPos)};var b=function(){function e(e,t){var r,n=0,i=e.length;for(e=e.slice();i--;)r=e[i]*t+n,e[i]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var i,a;if(r!=n)a=r>n?1:-1;else for(i=a=0;i<r;i++)if(e[i]!=t[i]){a=e[i]>t[i]?1:-1;break}return a}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=+(e[r]<t[r]),e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,i,a,o){var l,u,s,f,d,h,p,y,v,g,m,b,x,O,j,S,P,A,E=n.constructor,k=n.s==i.s?1:-1,_=n.d,T=i.d;if(!n.s)return new E(n);if(!i.s)throw Error(c+"Division by zero");for(s=0,u=n.e-i.e,P=T.length,j=_.length,y=(p=new E(k)).d=[];T[s]==(_[s]||0);)++s;if(T[s]>(_[s]||0)&&--u,(b=null==a?a=E.precision:o?a+(w(n)-w(i))+1:a)<0)return new E(0);if(b=b/7+2|0,s=0,1==P)for(f=0,T=T[0],b++;(s<j||f)&&b--;s++)x=1e7*f+(_[s]||0),y[s]=x/T|0,f=x%T|0;else{for((f=1e7/(T[0]+1)|0)>1&&(T=e(T,f),_=e(_,f),P=T.length,j=_.length),O=P,g=(v=_.slice(0,P)).length;g<P;)v[g++]=0;(A=T.slice()).unshift(0),S=T[0],T[1]>=1e7/2&&++S;do f=0,(l=t(T,v,P,g))<0?(m=v[0],P!=g&&(m=1e7*m+(v[1]||0)),(f=m/S|0)>1?(f>=1e7&&(f=1e7-1),h=(d=e(T,f)).length,g=v.length,1==(l=t(d,v,h,g))&&(f--,r(d,P<h?A:T,h))):(0==f&&(l=f=1),d=T.slice()),(h=d.length)<g&&d.unshift(0),r(v,d,g),-1==l&&(g=v.length,(l=t(T,v,P,g))<1&&(f++,r(v,P<g?A:T,g))),g=v.length):0===l&&(f++,v=[0]),y[s++]=f,l&&v[0]?v[g++]=_[O]||0:(v=[_[O]],g=1);while((O++<j||void 0!==v[0])&&b--)}return y[0]||y.shift(),p.e=u,M(p,o?a+w(p)+1:a)}}();function x(e,t){var r,n,i,o,c,u=0,f=0,h=e.constructor,p=h.precision;if(w(e)>16)throw Error(s+w(e));if(!e.s)return new h(a);for(null==t?(l=!1,c=p):c=t,o=new h(.03125);e.abs().gte(.1);)e=e.times(o),f+=5;for(c+=Math.log(d(2,f))/Math.LN10*2+5|0,r=n=i=new h(a),h.precision=c;;){if(n=M(n.times(e),c),r=r.times(++u),m((o=i.plus(b(n,r,c))).d).slice(0,c)===m(i.d).slice(0,c)){for(;f--;)i=M(i.times(i),c);return h.precision=p,null==t?(l=!0,M(i,p)):i}i=o}}function w(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function O(e,t,r){if(t>e.LN10.sd())throw l=!0,r&&(e.precision=r),Error(c+"LN10 precision limit exceeded");return M(new e(e.LN10),t)}function j(e){for(var t="";e--;)t+="0";return t}function S(e,t){var r,n,i,o,u,s,f,d,h,p=1,y=e,v=y.d,g=y.constructor,x=g.precision;if(y.s<1)throw Error(c+(y.s?"NaN":"-Infinity"));if(y.eq(a))return new g(0);if(null==t?(l=!1,d=x):d=t,y.eq(10))return null==t&&(l=!0),O(g,d);if(g.precision=d+=10,n=(r=m(v)).charAt(0),!(15e14>Math.abs(o=w(y))))return f=O(g,d+2,x).times(o+""),y=S(new g(n+"."+r.slice(1)),d-10).plus(f),g.precision=x,null==t?(l=!0,M(y,x)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=m((y=y.times(e)).d)).charAt(0),p++;for(o=w(y),n>1?(y=new g("0."+r),o++):y=new g(n+"."+r.slice(1)),s=u=y=b(y.minus(a),y.plus(a),d),h=M(y.times(y),d),i=3;;){if(u=M(u.times(h),d),m((f=s.plus(b(u,new g(i),d))).d).slice(0,d)===m(s.d).slice(0,d))return s=s.times(2),0!==o&&(s=s.plus(O(g,d+2,x).times(o+""))),s=b(s,new g(p),d),g.precision=x,null==t?(l=!0,M(s,x)):s;s=f,i+=2}}function P(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(i=t.length;48===t.charCodeAt(i-1);)--i;if(t=t.slice(n,i)){if(i-=n,e.e=f((r=r-n-1)/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=7;n<i;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),l&&(e.e>p||e.e<-p))throw Error(s+r)}else e.s=0,e.e=0,e.d=[0];return e}function M(e,t,r){var n,i,a,o,c,u,h,y,v=e.d;for(o=1,a=v[0];a>=10;a/=10)o++;if((n=t-o)<0)n+=7,i=t,h=v[y=0];else{if((y=Math.ceil((n+1)/7))>=(a=v.length))return e;for(o=1,h=a=v[y];a>=10;a/=10)o++;n%=7,i=n-7+o}if(void 0!==r&&(c=h/(a=d(10,o-i-1))%10|0,u=t<0||void 0!==v[y+1]||h%a,u=r<4?(c||u)&&(0==r||r==(e.s<0?3:2)):c>5||5==c&&(4==r||u||6==r&&(n>0?i>0?h/d(10,o-i):0:v[y-1])%10&1||r==(e.s<0?8:7))),t<1||!v[0])return u?(a=w(e),v.length=1,t=t-a-1,v[0]=d(10,(7-t%7)%7),e.e=f(-t/7)||0):(v.length=1,v[0]=e.e=e.s=0),e;if(0==n?(v.length=y,a=1,y--):(v.length=y+1,a=d(10,7-n),v[y]=i>0?(h/d(10,o-i)%d(10,i)|0)*a:0),u)for(;;)if(0==y){1e7==(v[0]+=a)&&(v[0]=1,++e.e);break}else{if(v[y]+=a,1e7!=v[y])break;v[y--]=0,a=1}for(n=v.length;0===v[--n];)v.pop();if(l&&(e.e>p||e.e<-p))throw Error(s+w(e));return e}function A(e,t){var r,n,i,a,o,c,u,s,f,d,h=e.constructor,p=h.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new h(e),l?M(t,p):t;if(u=e.d,d=t.d,n=t.e,s=e.e,u=u.slice(),o=s-n){for((f=o<0)?(r=u,o=-o,c=d.length):(r=d,n=s,c=u.length),o>(i=Math.max(Math.ceil(p/7),c)+2)&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for((f=(i=u.length)<(c=d.length))&&(c=i),i=0;i<c;i++)if(u[i]!=d[i]){f=u[i]<d[i];break}o=0}for(f&&(r=u,u=d,d=r,t.s=-t.s),c=u.length,i=d.length-c;i>0;--i)u[c++]=0;for(i=d.length;i>o;){if(u[--i]<d[i]){for(a=i;a&&0===u[--a];)u[a]=1e7-1;--u[a],u[i]+=1e7}u[i]-=d[i]}for(;0===u[--c];)u.pop();for(;0===u[0];u.shift())--n;return u[0]?(t.d=u,t.e=n,l?M(t,p):t):new h(0)}function E(e,t,r){var n,i=w(e),a=m(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+j(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+j(-i-1)+a,r&&(n=r-o)>0&&(a+=j(n))):i>=o?(a+=j(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+j(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=j(n))),e.s<0?"-"+a:a}function k(e,t){if(e.length>t)return e.length=t,!0}function _(e){if(!e||"object"!=typeof e)throw Error(c+"Object expected");var t,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if(void 0!==(n=e[r=i[t]]))if(f(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(u+r+": "+n);if(void 0!==(n=e[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(u+r+": "+n);return this}(o=function e(t){var r,n,i;function a(e){if(!(this instanceof a))return new a(e);if(this.constructor=a,e instanceof a){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(u+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return P(this,e.toString())}if("string"!=typeof e)throw Error(u+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,h.test(e))P(this,e);else throw Error(u+e)}if(a.prototype=y,a.ROUND_UP=0,a.ROUND_DOWN=1,a.ROUND_CEIL=2,a.ROUND_FLOOR=3,a.ROUND_HALF_UP=4,a.ROUND_HALF_DOWN=5,a.ROUND_HALF_EVEN=6,a.ROUND_HALF_CEIL=7,a.ROUND_HALF_FLOOR=8,a.clone=e,a.config=a.set=_,void 0===t&&(t={}),t)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)t.hasOwnProperty(n=i[r++])||(t[n]=this[n]);return a.config(t),a}(o)).default=o.Decimal=o,a=new o(1),void 0===(n=(function(){return o}).call(t,r,t,e))||(e.exports=n)}(0)},4964:(e,t,r)=>{"use strict";r.d(t,{Cj:()=>a,Pg:()=>o,Ub:()=>l});var n=r(9684),i=r(3497),a=(e,t)=>{var r=(0,n.j)();return(n,a)=>o=>{null==e||e(n,a,o),r((0,i.RD)({activeIndex:String(a),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},o=e=>{var t=(0,n.j)();return(r,n)=>a=>{null==e||e(r,n,a),t((0,i.oP)())}},l=(e,t)=>{var r=(0,n.j)();return(n,a)=>o=>{null==e||e(n,a,o),r((0,i.ML)({activeIndex:String(a),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}}},4980:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.identity=function(e){return e}},5004:(e,t,r)=>{"use strict";var n=r(2015),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,o=n.useEffect,l=n.useLayoutEffect,c=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,s=n[1];return l(function(){i.value=r,i.getSnapshot=t,u(i)&&s({inst:i})},[e,r,t]),o(function(){return u(i)&&s({inst:i}),e(function(){u(i)&&s({inst:i})})},[e]),c(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:s},5046:(e,t,r)=>{"use strict";r.d(t,{$:()=>i,X:()=>a});var n=r(2015),i=(0,n.createContext)(null),a=()=>(0,n.useContext)(i)},5101:(e,t,r)=>{"use strict";r.d(t,{HS:()=>o,LF:()=>i,z3:()=>a});var n=r(7242),i=e=>e.chartData,a=(0,n.Mz)([i],e=>{var t=null!=e.chartData?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}}),o=(e,t,r,n)=>n?a(e):i(e)},5108:(e,t,r)=>{"use strict";r.d(t,{r:()=>a}),r(2015);var n=r(9684);r(3497);var i=r(7580);function a(e){var{fn:t,args:r}=e;return(0,n.j)(),(0,i.r)(),null}},5158:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var n=(e,t,r)=>r},5239:(e,t,r)=>{"use strict";r.d(t,{s:()=>a}),r(2015);var n=r(7580);r(8224);var i=r(9684);function a(e){var{layout:t,width:r,height:a,margin:o}=e;return(0,i.j)(),(0,n.r)(),null}},5292:(e,t,r)=>{"use strict";r.d(t,{m:()=>ea});var n=r(2015),i=r(2326),a=r(7206),o=r.n(a),l=r(9486),c=r(4504);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return Array.isArray(e)&&(0,c.vh)(e[0])&&(0,c.vh)(e[1])?e.join(" ~ "):e}var h=e=>{var{separator:t=" : ",contentStyle:r={},itemStyle:i={},labelStyle:a={},payload:s,formatter:h,itemSorter:p,wrapperClassName:y,labelClassName:v,label:g,labelFormatter:m,accessibilityLayer:b=!1}=e,x=f({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),w=f({margin:0},a),O=!(0,c.uy)(g),j=O?g:"",S=(0,l.$)("recharts-default-tooltip",y),P=(0,l.$)("recharts-tooltip-label",v);return O&&m&&null!=s&&(j=m(g,s)),n.createElement("div",u({className:S,style:x},b?{role:"status","aria-live":"assertive"}:{}),n.createElement("p",{className:P,style:w},n.isValidElement(j)?j:"".concat(j)),(()=>{if(s&&s.length){var e=(p?o()(s,p):s).map((e,r)=>{if("none"===e.type)return null;var a=e.formatter||h||d,{value:o,name:l}=e,u=o,p=l;if(a){var y=a(o,l,e,r,s);if(Array.isArray(y))[u,p]=y;else{if(null==y)return null;u=y}}var v=f({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},i);return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(r),style:v},(0,c.vh)(p)?n.createElement("span",{className:"recharts-tooltip-item-name"},p):null,(0,c.vh)(p)?n.createElement("span",{className:"recharts-tooltip-item-separator"},t):null,n.createElement("span",{className:"recharts-tooltip-item-value"},u),n.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null})())},p="recharts-tooltip-wrapper",y={visibility:"hidden"};function v(e){var{allowEscapeViewBox:t,coordinate:r,key:n,offsetTopLeft:i,position:a,reverseDirection:o,tooltipDimension:l,viewBox:u,viewBoxDimension:s}=e;if(a&&(0,c.Et)(a[n]))return a[n];var f=r[n]-l-(i>0?i:0),d=r[n]+i;if(t[n])return o[n]?f:d;var h=u[n];return null==h?0:o[n]?f<h?Math.max(d,h):Math.max(f,h):null==s?0:d+l>h+s?Math.max(f,h):Math.max(d,h)}function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){b(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function b(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class x extends n.PureComponent{constructor(){super(...arguments),b(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),b(this,"handleKeyDown",e=>{if("Escape"===e.key){var t,r,n,i;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(t=null==(r=this.props.coordinate)?void 0:r.x)?t:0,y:null!=(n=null==(i=this.props.coordinate)?void 0:i.y)?n:0}})}})}componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var e,t;this.state.dismissed&&((null==(e=this.props.coordinate)?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null==(t=this.props.coordinate)?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:e,allowEscapeViewBox:t,animationDuration:r,animationEasing:i,children:a,coordinate:o,hasPayload:u,isAnimationActive:s,offset:f,position:d,reverseDirection:h,useTranslate3d:g,viewBox:b,wrapperStyle:x,lastBoundingBox:w,innerRef:O,hasPortalFromProps:j}=this.props,{cssClasses:S,cssProperties:P}=function(e){var t,r,n,{allowEscapeViewBox:i,coordinate:a,offsetTopLeft:o,position:u,reverseDirection:s,tooltipBox:f,useTranslate3d:d,viewBox:h}=e;return{cssProperties:t=f.height>0&&f.width>0&&a?function(e){var{translateX:t,translateY:r,useTranslate3d:n}=e;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}({translateX:r=v({allowEscapeViewBox:i,coordinate:a,key:"x",offsetTopLeft:o,position:u,reverseDirection:s,tooltipDimension:f.width,viewBox:h,viewBoxDimension:h.width}),translateY:n=v({allowEscapeViewBox:i,coordinate:a,key:"y",offsetTopLeft:o,position:u,reverseDirection:s,tooltipDimension:f.height,viewBox:h,viewBoxDimension:h.height}),useTranslate3d:d}):y,cssClasses:function(e){var{coordinate:t,translateX:r,translateY:n}=e;return(0,l.$)(p,{["".concat(p,"-right")]:(0,c.Et)(r)&&t&&(0,c.Et)(t.x)&&r>=t.x,["".concat(p,"-left")]:(0,c.Et)(r)&&t&&(0,c.Et)(t.x)&&r<t.x,["".concat(p,"-bottom")]:(0,c.Et)(n)&&t&&(0,c.Et)(t.y)&&n>=t.y,["".concat(p,"-top")]:(0,c.Et)(n)&&t&&(0,c.Et)(t.y)&&n<t.y})}({translateX:r,translateY:n,coordinate:a})}}({allowEscapeViewBox:t,coordinate:o,offsetTopLeft:f,position:d,reverseDirection:h,tooltipBox:{height:w.height,width:w.width},useTranslate3d:g,viewBox:b}),M=j?{}:m(m({transition:s&&e?"transform ".concat(r,"ms ").concat(i):void 0},P),{},{pointerEvents:"none",visibility:!this.state.dismissed&&e&&u?"visible":"hidden",position:"absolute",top:0,left:0}),A=m(m({},M),{},{visibility:!this.state.dismissed&&e&&u?"visible":"hidden"},x);return n.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:S,style:A,ref:O},a)}}var w=r(4056),O=r(6441),j=r.n(O),S=r(4397),P=r(2579),M=r(2764),A=r(2661),E=["x","y","top","left","width","height","className"];function k(){return(k=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function _(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var T=(e,t,r,n,i,a)=>"M".concat(e,",").concat(i,"v").concat(n,"M").concat(a,",").concat(t,"h").concat(r),C=e=>{var{x:t=0,y:r=0,top:i=0,left:a=0,width:o=0,height:u=0,className:s}=e,f=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:t,y:r,top:i,left:a,width:o,height:u},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,E));return(0,c.Et)(t)&&(0,c.Et)(r)&&(0,c.Et)(o)&&(0,c.Et)(u)&&(0,c.Et)(i)&&(0,c.Et)(a)?n.createElement("path",k({},(0,A.J9)(f,!0),{className:(0,l.$)("recharts-cross",s),d:T(t,r,o,u,i,a)})):null},D=r(2219),N=r(8702);function I(e){var{cx:t,cy:r,radius:n,startAngle:i,endAngle:a}=e;return{points:[(0,N.IZ)(t,r,n,i),(0,N.IZ)(t,r,n,a)],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}var z=r(1506),L=r(9684),R=r(9058),$=r(6113);function B(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function F(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?B(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):B(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var U=()=>(0,L.G)($.Dn),K=()=>{var e=U(),t=(0,L.G)($.R4),r=(0,L.G)($.fl);return(0,R.Hj)(F(F({},e),{},{scale:r}),t)},H=r(6973);function G(){return(G=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function W(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Z(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?W(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):W(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function q(e){var t,r,i,{coordinate:a,payload:o,index:c,offset:u,tooltipAxisBandSize:s,layout:f,cursor:d,tooltipEventType:h,chartName:p}=e;if(!d||!a||"ScatterChart"!==p&&"axis"!==h)return null;if("ScatterChart"===p)r=a,i=C;else if("BarChart"===p)t=s/2,r={stroke:"none",fill:"#ccc",x:"horizontal"===f?a.x-t:u.left+.5,y:"horizontal"===f?u.top+.5:a.y-t,width:"horizontal"===f?s:u.width-1,height:"horizontal"===f?u.height-1:s},i=D.M;else if("radial"===f){var{cx:y,cy:v,radius:g,startAngle:m,endAngle:b}=I(a);r={cx:y,cy:v,startAngle:m,endAngle:b,innerRadius:g,outerRadius:g},i=z.h}else r={points:function(e,t,r){var n,i,a,o;if("horizontal"===e)a=n=t.x,i=r.top,o=r.top+r.height;else if("vertical"===e)o=i=t.y,n=r.left,a=r.left+r.width;else if(null!=t.cx&&null!=t.cy)if("centric"!==e)return I(t);else{var{cx:l,cy:c,innerRadius:u,outerRadius:s,angle:f}=t,d=(0,N.IZ)(l,c,u,f),h=(0,N.IZ)(l,c,s,f);n=d.x,i=d.y,a=h.x,o=h.y}return[{x:n,y:i},{x:a,y:o}]}(f,a,u)},i=M.I;var x="object"==typeof d&&"className"in d?d.className:void 0,w=Z(Z(Z(Z({stroke:"#ccc",pointerEvents:"none"},u),r),(0,A.J9)(d,!1)),{},{payload:o,payloadIndex:c,className:(0,l.$)("recharts-tooltip-cursor",x)});return(0,n.isValidElement)(d)?(0,n.cloneElement)(d,w):(0,n.createElement)(i,w)}function V(e){var t=K(),r=(0,S.W7)(),i=(0,S.WX)(),a=(0,H.fW)();return n.createElement(q,G({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:r,layout:i,tooltipAxisBandSize:t,chartName:a}))}var Y=r(5046);r(3497);var J=r(3713),X=r(5306),Q=r(4644);function ee(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function et(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ee(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ee(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function er(e){return e.dataKey}var en=[],ei={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!w.m.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function ea(e){var t,r,a=(0,Q.e)(e,ei),{active:o,allowEscapeViewBox:l,animationDuration:c,animationEasing:u,content:s,filterNull:f,isAnimationActive:d,offset:p,payloadUniqBy:y,position:v,reverseDirection:g,useTranslate3d:m,wrapperStyle:b,cursor:w,shared:O,trigger:M,defaultIndex:A,portal:E,axisId:k}=a;(0,L.j)();var _="number"==typeof A?String(A):A,T=(0,S.sk)(),C=(0,P.$)(),D=(0,X.Td)(O),{activeIndex:N,isActive:I}=(0,L.G)(e=>(0,H.yn)(e,D,M,_)),z=(0,L.G)(e=>(0,H.u9)(e,D,M,_)),R=(0,L.G)(e=>(0,H.BZ)(e,D,M,_)),$=(0,L.G)(e=>(0,H.dS)(e,D,M,_)),B=(0,Y.X)(),F=null!=o?o:I,[U,K]=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[t,r]=(0,n.useState)({height:0,left:0,top:0,width:0}),i=(0,n.useCallback)(e=>{if(null!=e){var n=e.getBoundingClientRect(),i={height:n.height,left:n.left,top:n.top,width:n.width};(Math.abs(i.height-t.height)>1||Math.abs(i.left-t.left)>1||Math.abs(i.top-t.top)>1||Math.abs(i.width-t.width)>1)&&r({height:i.height,left:i.left,top:i.top,width:i.width})}},[t.width,t.height,t.top,t.left,...e]);return[t,i]}([z,F]),G="axis"===D?R:void 0;(0,J.m7)(D,M,$,G,N,F);var W=null!=E?E:B;if(null==W)return null;var Z=null!=z?z:en;F||(Z=en),f&&Z.length&&(t=z.filter(e=>null!=e.value&&(!0!==e.hide||a.includeHidden)),Z=!0===y?j()(t,er):"function"==typeof y?j()(t,y):t);var q=Z.length>0,ee=n.createElement(x,{allowEscapeViewBox:l,animationDuration:c,animationEasing:u,isAnimationActive:d,active:F,coordinate:$,hasPayload:q,offset:p,position:v,reverseDirection:g,useTranslate3d:m,viewBox:T,wrapperStyle:b,lastBoundingBox:U,innerRef:K,hasPortalFromProps:!!E},(r=et(et({},a),{},{payload:Z,label:G,active:F,coordinate:$,accessibilityLayer:C}),n.isValidElement(s)?n.cloneElement(s,r):"function"==typeof s?n.createElement(s,r):n.createElement(h,r)));return n.createElement(n.Fragment,null,(0,i.createPortal)(ee,W),F&&n.createElement(V,{cursor:w,tooltipEventType:D,coordinate:$,payload:z,index:N}))}},5306:(e,t,r)=>{"use strict";r.d(t,{$g:()=>o,Hw:()=>a,Td:()=>c,au:()=>l,xH:()=>i});var n=r(9684),i=e=>e.options.defaultTooltipEventType,a=e=>e.options.validateTooltipEventTypes;function o(e,t,r){if(null==e)return t;var n=e?"axis":"item";return null==r?t:r.includes(n)?n:t}function l(e,t){return o(t,i(e),a(e))}function c(e){return(0,n.G)(t=>l(t,e))}},5336:(e,t,r)=>{"use strict";r.d(t,{A$:()=>i,HK:()=>o,Lp:()=>n,et:()=>a});var n=e=>e.layout.width,i=e=>e.layout.height,a=e=>e.layout.scale,o=e=>e.layout.margin},5350:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isDeepKey=function(e){switch(typeof e){case"number":case"symbol":return!1;case"string":return e.includes(".")||e.includes("[")||e.includes("]")}}},5411:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(7254),i=r(3667),a=r(8923),o=r(776),l=r(7157);t.isEqualWith=function(e,t,r){return function e(t,r,c,u,s,f,d){let h=d(t,r,c,u,s,f);if(void 0!==h)return h;if(typeof t==typeof r)switch(typeof t){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return t===r;case"number":return t===r||Object.is(t,r)}return function t(r,c,u,s){if(Object.is(r,c))return!0;let f=a.getTag(r),d=a.getTag(c);if(f===o.argumentsTag&&(f=o.objectTag),d===o.argumentsTag&&(d=o.objectTag),f!==d)return!1;switch(f){case o.stringTag:return r.toString()===c.toString();case o.numberTag:{let e=r.valueOf(),t=c.valueOf();return l.eq(e,t)}case o.booleanTag:case o.dateTag:case o.symbolTag:return Object.is(r.valueOf(),c.valueOf());case o.regexpTag:return r.source===c.source&&r.flags===c.flags;case o.functionTag:return r===c}let h=(u=u??new Map).get(r),p=u.get(c);if(null!=h&&null!=p)return h===c;u.set(r,c),u.set(c,r);try{switch(f){case o.mapTag:if(r.size!==c.size)return!1;for(let[t,n]of r.entries())if(!c.has(t)||!e(n,c.get(t),t,r,c,u,s))return!1;return!0;case o.setTag:{if(r.size!==c.size)return!1;let t=Array.from(r.values()),n=Array.from(c.values());for(let i=0;i<t.length;i++){let a=t[i],o=n.findIndex(t=>e(a,t,void 0,r,c,u,s));if(-1===o)return!1;n.splice(o,1)}return!0}case o.arrayTag:case o.uint8ArrayTag:case o.uint8ClampedArrayTag:case o.uint16ArrayTag:case o.uint32ArrayTag:case o.bigUint64ArrayTag:case o.int8ArrayTag:case o.int16ArrayTag:case o.int32ArrayTag:case o.bigInt64ArrayTag:case o.float32ArrayTag:case o.float64ArrayTag:if("undefined"!=typeof Buffer&&Buffer.isBuffer(r)!==Buffer.isBuffer(c)||r.length!==c.length)return!1;for(let t=0;t<r.length;t++)if(!e(r[t],c[t],t,r,c,u,s))return!1;return!0;case o.arrayBufferTag:if(r.byteLength!==c.byteLength)return!1;return t(new Uint8Array(r),new Uint8Array(c),u,s);case o.dataViewTag:if(r.byteLength!==c.byteLength||r.byteOffset!==c.byteOffset)return!1;return t(new Uint8Array(r),new Uint8Array(c),u,s);case o.errorTag:return r.name===c.name&&r.message===c.message;case o.objectTag:{if(!(t(r.constructor,c.constructor,u,s)||n.isPlainObject(r)&&n.isPlainObject(c)))return!1;let a=[...Object.keys(r),...i.getSymbols(r)],o=[...Object.keys(c),...i.getSymbols(c)];if(a.length!==o.length)return!1;for(let t=0;t<a.length;t++){let n=a[t],i=r[n];if(!Object.hasOwn(c,n))return!1;let o=c[n];if(!e(i,o,n,r,c,u,s))return!1}return!0}default:return!1}}finally{u.delete(r),u.delete(c)}}(t,r,f,d)}(e,t,void 0,void 0,void 0,void 0,r)}},5437:(e,t,r)=>{"use strict";r.d(t,{As:()=>f,Ch:()=>l,TK:()=>d,Vi:()=>s,ZF:()=>u,g5:()=>c,iZ:()=>h,lm:()=>o});var n=r(2481),i=r(7555),a=(0,n.Z0)({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(e){e.countOfBars+=1},removeBar(e){e.countOfBars-=1},addCartesianGraphicalItem(e,t){e.cartesianItems.push((0,i.h4)(t.payload))},replaceCartesianGraphicalItem(e,t){var{prev:r,next:n}=t.payload,a=(0,i.ss)(e).cartesianItems.indexOf((0,i.h4)(r));a>-1&&(e.cartesianItems[a]=(0,i.h4)(n))},removeCartesianGraphicalItem(e,t){var r=(0,i.ss)(e).cartesianItems.indexOf((0,i.h4)(t.payload));r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push((0,i.h4)(t.payload))},removePolarGraphicalItem(e,t){var r=(0,i.ss)(e).polarItems.indexOf((0,i.h4)(t.payload));r>-1&&e.polarItems.splice(r,1)}}}),{addBar:o,removeBar:l,addCartesianGraphicalItem:c,replaceCartesianGraphicalItem:u,removeCartesianGraphicalItem:s,addPolarGraphicalItem:f,removePolarGraphicalItem:d}=a.actions,h=a.reducer},5492:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8708),i=r(2505);t.range=function(e,t,r){r&&"number"!=typeof r&&n.isIterateeCall(e,t,r)&&(t=r=void 0),e=i.toFinite(e),void 0===t?(t=e,e=0):t=i.toFinite(t),r=void 0===r?e<t?1:-1:i.toFinite(r);let a=Math.max(Math.ceil((t-e)/(r||1)),0),o=Array(a);for(let t=0;t<a;t++)o[t]=e,e+=r;return o}},5524:(e,t,r)=>{"use strict";r.d(t,{h:()=>x});var n=r(2015),i=r(9486),a=r(9713),o=r(1413),l=r(9684),c=r(3411),u=r(3401),s=r(7580),f=e=>{var{ticks:t,label:r,labelGapWithTick:n=5,tickSize:i=0,tickMargin:a=0}=e,o=0;if(t){t.forEach(e=>{if(e){var t=e.getBoundingClientRect();t.width>o&&(o=t.width)}});var l=r?r.getBoundingClientRect().width:0;return Math.round(o+(i+a)+l+(r?n:0))}return 0},d=r(4351),h=["dangerouslySetInnerHTML","ticks"];function p(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function y(){return(y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function v(e){return(0,l.j)(),null}var g=e=>{var t,{yAxisId:r,className:p,width:v,label:g}=e,m=(0,n.useRef)(null),b=(0,n.useRef)(null),x=(0,l.G)(u.c2),w=(0,s.r)(),O=(0,l.j)(),j="yAxis",S=(0,l.G)(e=>(0,c.iV)(e,j,r,w)),P=(0,l.G)(e=>(0,c.wP)(e,r)),M=(0,l.G)(e=>(0,c.KR)(e,r)),A=(0,l.G)(e=>(0,c.Zi)(e,j,r,w));if((0,n.useLayoutEffect)(()=>{if(!("auto"!==v||!P||(0,d.Z)(g)||(0,n.isValidElement)(g))){var e,t=m.current,i=null==t||null==(e=t.tickRefs)?void 0:e.current,{tickSize:a,tickMargin:l}=t.props,c=f({ticks:i,label:b.current,labelGapWithTick:5,tickSize:a,tickMargin:l});Math.round(P.width)!==Math.round(c)&&O((0,o.QG)({id:r,width:c}))}},[m,null==m||null==(t=m.current)||null==(t=t.tickRefs)?void 0:t.current,null==P?void 0:P.width,P,O,g,r,v]),null==P||null==M)return null;var{dangerouslySetInnerHTML:E,ticks:k}=e,_=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,h);return n.createElement(a.u,y({},_,{ref:m,labelRef:b,scale:S,x:M.x,y:M.y,width:P.width,height:P.height,className:(0,i.$)("recharts-".concat(j," ").concat(j),p),viewBox:x,ticks:A}))},m=e=>{var t,r,i,a,o;return n.createElement(n.Fragment,null,n.createElement(v,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(i=e.angle)?i:0,minTickGap:null!=(a=e.minTickGap)?a:5,tick:null==(o=e.tick)||o,tickFormatter:e.tickFormatter}),n.createElement(g,e))},b={allowDataOverflow:c.cd.allowDataOverflow,allowDecimals:c.cd.allowDecimals,allowDuplicatedCategory:c.cd.allowDuplicatedCategory,hide:!1,mirror:c.cd.mirror,orientation:c.cd.orientation,padding:c.cd.padding,reversed:c.cd.reversed,scale:c.cd.scale,tickCount:c.cd.tickCount,type:c.cd.type,width:c.cd.width,yAxisId:0};class x extends n.Component{render(){return n.createElement(m,this.props)}}p(x,"displayName","YAxis"),p(x,"defaultProps",b)},5542:e=>{"use strict";e.exports=require("next-auth")},5806:e=>{"use strict";e.exports=require("next-auth/next")},5928:(e,t,r)=>{"use strict";r.d(t,{H:()=>i});var n=r(8079);let i=[{id:"overview",title:"Overview",description:"View server statistics and general information.",icon:n.z1n,href:"/overview",color:"blue",gradient:{from:"rgba(49, 130, 206, 0.4)",to:"rgba(49, 130, 206, 0.1)"},accentColor:"#63B3ED"},{id:"gameservers",title:"Game Servers",description:"Manage and monitor your game servers. View status, add or edit server configurations.",icon:n.ufi,href:"/gameservers",color:"green",gradient:{from:"rgba(72, 187, 120, 0.4)",to:"rgba(72, 187, 120, 0.1)"},accentColor:"#68D391"},{id:"applications",title:"Applications",description:"Review and manage guild applications. Process new members and handle requests.",icon:n.est,href:"/applications",color:"purple",gradient:{from:"rgba(159, 122, 234, 0.4)",to:"rgba(159, 122, 234, 0.1)"},accentColor:"#B794F4"},{id:"tickets",title:"Support Tickets",description:"Track and manage support tickets. Respond to user inquiries and resolve issues.",icon:n.lrG,href:"/tickets",color:"orange",gradient:{from:"rgba(237, 137, 54, 0.4)",to:"rgba(237, 137, 54, 0.1)"},accentColor:"#F6AD55"},{id:"moderation",title:"Moderation",description:"Tools and features for server moderators.",icon:n.F5$,href:"/moderation",color:"teal",gradient:{from:"rgba(49, 151, 149, 0.4)",to:"rgba(49, 151, 149, 0.1)"},accentColor:"#4FD1C5",requiredRole:"moderator"},{id:"experimental",title:"Experimental Features",description:"Try out new features that are still in development. These may not work as expected.",icon:n.VSk,href:"#",color:"yellow",gradient:{from:"rgba(236, 201, 75, 0.4)",to:"rgba(236, 201, 75, 0.1)"},accentColor:"#F6E05E",experimental:!0,disabled:!0}]},5947:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if("object"!=typeof e||null==e)return!1;if(null===Object.getPrototypeOf(e))return!0;if("[object Object]"!==Object.prototype.toString.call(e)){let t=e[Symbol.toStringTag];return null!=t&&!!Object.getOwnPropertyDescriptor(e,Symbol.toStringTag)?.writable&&e.toString()===`[object ${t}]`}let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},5953:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var n=(e,t)=>t},6113:(e,t,r)=>{"use strict";r.d(t,{BZ:()=>ea,eE:()=>eu,Xb:()=>eo,A2:()=>ei,yn:()=>es,Dn:()=>S,gL:()=>V,fl:()=>Y,R4:()=>Q,Re:()=>O,n4:()=>_});var n=r(7242),i=r(3411),a=r(4397),o=r(9058),l=r(5101),c=r(3361),u=r(4504),s=r(639),f=r(5306),d=r(4721),h=r(2383),p=r(594),y=r(4363),v=r(5336),g=r(3401),m=r(2739),b=r(9361),x=r(2157),w=r(1433),O=e=>{var t=(0,a.fz)(e);return"horizontal"===t?"xAxis":"vertical"===t?"yAxis":"centric"===t?"angleAxis":"radiusAxis"},j=e=>e.tooltip.settings.axisId,S=e=>{var t=O(e),r=j(e);return(0,i.Hd)(e,t,r)},P=(0,n.Mz)([S,a.fz,i.um,c.iO,O],i.sr),M=(0,n.Mz)([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],(e,t)=>[...e,...t]),A=(0,n.Mz)([O,j],i.eo),E=(0,n.Mz)([M,S,A],i.ec),k=(0,n.Mz)([E],i.rj),_=(0,n.Mz)([k,l.LF],i.Nk),T=(0,n.Mz)([_,S,E],i.fb),C=(0,n.Mz)([S],i.S5),D=(0,n.Mz)([_,E,c.eC],i.MK),N=(0,n.Mz)([D,l.LF,O],i.pM),I=(0,n.Mz)([E],i.IO),z=(0,n.Mz)([_,S,I,O],i.kz),L=(0,n.Mz)([i.Kr,O,j],i.P9),R=(0,n.Mz)([L,O],i.Oz),$=(0,n.Mz)([i.gT,O,j],i.P9),B=(0,n.Mz)([$,O],i.q),F=(0,n.Mz)([i.$X,O,j],i.P9),U=(0,n.Mz)([F,O],i.bb),K=(0,n.Mz)([R,U,B],i.yi),H=(0,n.Mz)([S,C,N,z,K],i.wL),G=(0,n.Mz)([S,a.fz,_,T,c.eC,O,H],i.tP),W=(0,n.Mz)([G,S,P],i.xp),Z=(0,n.Mz)([S,G,W,O],i.g1),q=e=>{var t=O(e),r=j(e);return(0,i.D5)(e,t,r,!1)},V=(0,n.Mz)([S,q],s.I),Y=(0,n.Mz)([S,P,Z,V],i.Qn),J=(0,n.Mz)([a.fz,T,S,O],i.tF),X=(0,n.Mz)([a.fz,T,S,O],i.iv),Q=(0,n.Mz)([a.fz,S,P,Y,q,J,X,O],(e,t,r,n,i,a,l,c)=>{if(t){var{type:s}=t,f=(0,o._L)(e,c);if(n){var d="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,h="category"===s&&n.bandwidth?n.bandwidth()/d:0;return(h="angleAxis"===c&&null!=i&&(null==i?void 0:i.length)>=2?2*(0,u.sA)(i[0]-i[1])*h:h,f&&l)?l.map((e,t)=>({coordinate:n(e)+h,value:e,index:t,offset:h})):n.domain().map((e,t)=>({coordinate:n(e)+h,value:a?a[e]:e,index:t,offset:h}))}}}),ee=(0,n.Mz)([f.xH,f.Hw,e=>e.tooltip.settings],(e,t,r)=>(0,f.$g)(r.shared,e,t)),et=e=>e.tooltip.settings.trigger,er=e=>e.tooltip.settings.defaultIndex,en=(0,n.Mz)([x.J,ee,et,er],h.i),ei=(0,n.Mz)([en,_],p.P),ea=(0,n.Mz)([Q,ei],d.E),eo=(0,n.Mz)([en],e=>{if(e)return e.dataKey}),el=(0,n.Mz)([x.J,ee,et,er],m.q),ec=(0,n.Mz)([v.Lp,v.A$,a.fz,g.HZ,Q,er,el,b.x],y.o),eu=(0,n.Mz)([en,ec],(e,t)=>null!=e&&e.coordinate?e.coordinate:t),es=(0,n.Mz)([en],e=>e.active),ef=(0,n.Mz)([el,ei,l.LF,S,ea,b.x,ee],w.N);(0,n.Mz)([ef],e=>{if(null!=e)return Array.from(new Set(e.map(e=>e.payload).filter(e=>null!=e)))})},6271:(e,t,r)=>{e.exports=r(2779).isEqual},6284:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(5350),i=r(753),a=r(8258),o=r(4817);t.has=function(e,t){let r;if(0===(r=Array.isArray(t)?t:"string"==typeof t&&n.isDeepKey(t)&&e?.[t]==null?o.toPath(t):[t]).length)return!1;let l=e;for(let e=0;e<r.length;e++){let t=r[e];if((null==l||!Object.hasOwn(l,t))&&!((Array.isArray(l)||a.isArguments(l))&&i.isIndex(t)&&t<l.length))return!1;l=l[t]}return!0}},6315:(e,t,r)=>{"use strict";e.exports=r(5004)},6326:(e,t,r)=>{"use strict";r.d(t,{i:()=>D});var n=r(2015),i=r(6271),a=r.n(i),o=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],l=(e,t)=>e.map((e,r)=>e*t**r).reduce((e,t)=>e+t),c=(e,t)=>r=>l(o(e,t),r),u=(e,t)=>r=>l([...o(e,t).map((e,t)=>e*t).slice(1),0],r),s=function(){for(var e,t,r,n,i=arguments.length,a=Array(i),o=0;o<i;o++)a[o]=arguments[o];if(1===a.length)switch(a[0]){case"linear":[e,r,t,n]=[0,0,1,1];break;case"ease":[e,r,t,n]=[.25,.1,.25,1];break;case"ease-in":[e,r,t,n]=[.42,0,1,1];break;case"ease-out":[e,r,t,n]=[.42,0,.58,1];break;case"ease-in-out":[e,r,t,n]=[0,0,.58,1];break;default:var l=a[0].split("(");"cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length&&([e,r,t,n]=l[1].split(")")[0].split(",").map(e=>parseFloat(e)))}else 4===a.length&&([e,r,t,n]=a);var s=c(e,t),f=c(r,n),d=u(e,t),h=e=>e>1?1:e<0?0:e,p=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var i=s(r)-t,a=d(r);if(1e-4>Math.abs(i-t)||a<1e-4)break;r=h(r-i/a)}return f(r)};return p.isStepper=!1,p},f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,i=(e,i,a)=>{var o=a+(-(e-i)*t-a*r)*n/1e3,l=a*n/1e3+e;return 1e-4>Math.abs(l-i)&&1e-4>Math.abs(o)?[i,0]:[l,o]};return i.isStepper=!0,i.dt=n,i},d=e=>{if("string"==typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return s(e);case"spring":return f();default:if("cubic-bezier"===e.split("(")[0])return s(e)}return"function"==typeof e?e:null};function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y=e=>e.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase())),v=(e,t,r)=>e.map(e=>"".concat(y(e)," ").concat(t,"ms ").concat(r)).join(","),g=(e,t)=>[Object.keys(e),Object.keys(t)].reduce((e,t)=>e.filter(e=>t.includes(e))),m=(e,t)=>Object.keys(t).reduce((r,n)=>p(p({},r),{},{[n]:e(n,t[n])}),{});function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function x(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var w=(e,t,r)=>e+(t-e)*r,O=e=>{var{from:t,to:r}=e;return t!==r},j=(e,t,r)=>{var n=m((t,r)=>{if(O(r)){var[n,i]=e(r.from,r.to,r.velocity);return x(x({},r),{},{from:n,velocity:i})}return r},t);return r<1?m((e,t)=>O(t)?x(x({},t),{},{velocity:w(t.velocity,n[e].velocity,r),from:w(t.from,n[e].from,r)}):t,t):j(e,n,r-1)};let S=(e,t,r,n,i,a)=>{var o=g(e,t);return!0===r.isStepper?function(e,t,r,n,i,a){var o,l=n.reduce((r,n)=>x(x({},r),{},{[n]:{from:e[n],velocity:0,to:t[n]}}),{}),c=()=>m((e,t)=>t.from,l),u=()=>!Object.values(l).filter(O).length,s=null,f=n=>{o||(o=n);var d=(n-o)/r.dt;l=j(r,l,d),i(x(x(x({},e),t),c())),o=n,u()||(s=a.setTimeout(f))};return()=>(s=a.setTimeout(f),()=>{s()})}(e,t,r,o,i,a):function(e,t,r,n,i,a,o){var l,c=null,u=i.reduce((r,n)=>x(x({},r),{},{[n]:[e[n],t[n]]}),{}),s=i=>{l||(l=i);var f=(i-l)/n,d=m((e,t)=>w(...t,r(f)),u);if(a(x(x(x({},e),t),d)),f<1)c=o.setTimeout(s);else{var h=m((e,t)=>w(...t,r(1)),u);a(x(x(x({},e),t),h))}};return()=>(c=o.setTimeout(s),()=>{c()})}(e,t,r,n,o,i,a)};class P{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=a=>{a-r>=t?e(a):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}var M=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function A(){return(A=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function E(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function k(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?E(Object(r),!0).forEach(function(t){_(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):E(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function _(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class T extends n.PureComponent{constructor(e,t){super(e,t),_(this,"mounted",!1),_(this,"manager",null),_(this,"stopJSAnimation",null),_(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:a,children:o,duration:l,animationManager:c}=this.props;if(this.manager=c,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0){this.state={style:{}},"function"==typeof o&&(this.state={style:a});return}if(i){if("function"==typeof o){this.state={style:i};return}this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:i,to:o,from:l}=this.props,{style:c}=this.state;if(r){if(!t){this.state&&c&&(n&&c[n]!==o||!n&&c!==o)&&this.setState({style:n?{[n]:o}:o});return}if(!a()(e.to,o)||!e.canBegin||!e.isActive){var u=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=u||i?l:e.to;this.state&&c&&(n&&c[n]!==s||!n&&c!==s)&&this.setState({style:n?{[n]:s}:s}),this.runAnimation(k(k({},this.props),{},{from:s,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var{from:t,to:r,duration:n,easing:i,begin:a,onAnimationEnd:o,onAnimationStart:l}=e,c=S(t,r,d(i),n,this.changeStyle,this.manager.getTimeoutController());this.manager.start([l,a,()=>{this.stopJSAnimation=c()},n,o])}runAnimation(e){var{begin:t,duration:r,attributeName:n,to:i,easing:a,onAnimationStart:o,onAnimationEnd:l,children:c}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof a||"function"==typeof c||"spring"===a)return void this.runJSAnimation(e);var u=n?{[n]:i}:i,s=v(Object.keys(u),r,a);this.manager.start([o,t,k(k({},u),{},{transition:s}),r,l])}render(){var e=this.props,{children:t,begin:r,duration:i,attributeName:a,easing:o,isActive:l,from:c,to:u,canBegin:s,onAnimationEnd:f,shouldReAnimate:d,onAnimationReStart:h,animationManager:p}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,M),v=n.Children.count(t),g=this.state.style;if("function"==typeof t)return t(g);if(!l||0===v||i<=0)return t;var m=e=>{var{style:t={},className:r}=e.props;return(0,n.cloneElement)(e,k(k({},y),{},{style:k(k({},t),g),className:r}))};return 1===v?m(n.Children.only(t)):n.createElement("div",null,n.Children.map(t,e=>m(e)))}}_(T,"displayName","Animate"),_(T,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var C=(0,n.createContext)(null);function D(e){var t,r,i,a,o,l,c,u,s=(0,n.useContext)(C);return n.createElement(T,A({},e,{animationManager:null!=(c=null!=(u=e.animationManager)?u:s)?c:(t=new P,i=()=>null,a=!1,o=null,l=e=>{if(!a){if(Array.isArray(e)){if(!e.length)return;var[r,...n]=e;if("number"==typeof r){o=t.setTimeout(l.bind(null,n),r);return}l(r),o=t.setTimeout(l.bind(null,n));return}"object"==typeof e&&i(e),"function"==typeof e&&e()}},{stop:()=>{a=!0},start:e=>{a=!1,o&&(o(),o=null),l(e)},subscribe:e=>(i=e,()=>{i=()=>null}),getTimeoutController:()=>t})}))}},6441:(e,t,r)=>{e.exports=r(4390).uniqBy},6509:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(3121);t.cloneDeep=function(e){return n.cloneDeepWith(e)}},6614:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(9405),i=r(9897),a=r(6509),o=r(9952),l=r(6284);t.matchesProperty=function(e,t){switch(typeof e){case"object":Object.is(e?.valueOf(),-0)&&(e="-0");break;case"number":e=i.toKey(e)}return t=a.cloneDeep(t),function(r){let i=o.get(r,e);return void 0===i?l.has(r,e):void 0===t?void 0===i:n.isMatch(i,t)}}},6626:(e,t,r)=>{"use strict";r.d(t,{e:()=>p,k:()=>y});var n=r(2481),i=r(3497),a=r(3764),o=r(8776),l=r(5306),c=r(3084),u=r(7242),s=r(9361),f=r(2157),d=(0,u.Mz)([f.J],e=>e.tooltipItemPayloads),h=(0,u.Mz)([d,s.x,(e,t,r)=>t,(e,t,r)=>r],(e,t,r,n)=>{var i=e.find(e=>e.settings.dataKey===n);if(null!=i){var{positions:a}=i;if(null!=a)return t(a,r)}}),p=(0,n.VP)("touchMove"),y=(0,n.Nc)();y.startListening({actionCreator:p,effect:(e,t)=>{var r=e.payload,n=t.getState(),u=(0,l.au)(n,n.tooltip.settings.shared);if("axis"===u){var s=(0,a.g)(n,(0,o.w)({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(null==s?void 0:s.activeIndex)!=null&&t.dispatch((0,i.Nt)({activeIndex:s.activeIndex,activeDataKey:void 0,activeCoordinate:s.activeCoordinate}))}else if("item"===u){var f,d=r.touches[0],p=document.elementFromPoint(d.clientX,d.clientY);if(!p||!p.getAttribute)return;var y=p.getAttribute(c.F0),v=null!=(f=p.getAttribute(c.um))?f:void 0,g=h(t.getState(),y,v);t.dispatch((0,i.RD)({activeDataKey:v,activeIndex:y,activeCoordinate:g}))}}})},6762:(e,t,r)=>{"use strict";r.d(t,{W:()=>m});var n=r(2015),i=r(9486),a=r(9713),o=r(9684);r(1413);var l=r(3411),c=r(3401),u=r(7580),s=["children"],f=["dangerouslySetInnerHTML","ticks"];function d(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function h(){return(h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function p(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function y(e){(0,o.j)();var t=(0,n.useMemo)(()=>{var{children:t}=e;return p(e,s)},[e]),r=(0,o.G)(e=>(0,l.Rl)(e,t.id));return t===r?e.children:null}var v=e=>{var{xAxisId:t,className:r}=e,s=(0,o.G)(c.c2),d=(0,u.r)(),y="xAxis",v=(0,o.G)(e=>(0,l.iV)(e,y,t,d)),g=(0,o.G)(e=>(0,l.Zi)(e,y,t,d)),m=(0,o.G)(e=>(0,l.Lw)(e,t)),b=(0,o.G)(e=>(0,l.L$)(e,t));if(null==m||null==b)return null;var{dangerouslySetInnerHTML:x,ticks:w}=e,O=p(e,f);return n.createElement(a.u,h({},O,{scale:v,x:b.x,y:b.y,width:m.width,height:m.height,className:(0,i.$)("recharts-".concat(y," ").concat(y),r),viewBox:s,ticks:g}))},g=e=>{var t,r,i,a,o;return n.createElement(y,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(i=e.angle)?i:0,minTickGap:null!=(a=e.minTickGap)?a:5,tick:null==(o=e.tick)||o,tickFormatter:e.tickFormatter},n.createElement(v,e))};class m extends n.Component{render(){return n.createElement(g,this.props)}}d(m,"displayName","XAxis"),d(m,"defaultProps",{allowDataOverflow:l.PU.allowDataOverflow,allowDecimals:l.PU.allowDecimals,allowDuplicatedCategory:l.PU.allowDuplicatedCategory,height:l.PU.height,hide:!1,mirror:l.PU.mirror,orientation:l.PU.orientation,padding:l.PU.padding,reversed:l.PU.reversed,scale:l.PU.scale,tickCount:l.PU.tickCount,type:l.PU.type,xAxisId:0})},6851:(e,t,r)=>{"use strict";e.exports=r(2111)},6852:(e,t,r)=>{"use strict";r.d(t,{R:()=>n});var n=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]}},6912:(e,t,r)=>{"use strict";function n(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}r.d(t,{HY:()=>u,Qd:()=>l,Tw:()=>f,Zz:()=>s,ve:()=>d,y$:()=>c});var i="function"==typeof Symbol&&Symbol.observable||"@@observable",a=()=>Math.random().toString(36).substring(7).split("").join("."),o={INIT:`@@redux/INIT${a()}`,REPLACE:`@@redux/REPLACE${a()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${a()}`};function l(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function c(e,t,r){if("function"!=typeof e)throw Error(n(2));if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw Error(n(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw Error(n(1));return r(c)(e,t)}let a=e,u=t,s=new Map,f=s,d=0,h=!1;function p(){f===s&&(f=new Map,s.forEach((e,t)=>{f.set(t,e)}))}function y(){if(h)throw Error(n(3));return u}function v(e){if("function"!=typeof e)throw Error(n(4));if(h)throw Error(n(5));let t=!0;p();let r=d++;return f.set(r,e),function(){if(t){if(h)throw Error(n(6));t=!1,p(),f.delete(r),s=null}}}function g(e){if(!l(e))throw Error(n(7));if(void 0===e.type)throw Error(n(8));if("string"!=typeof e.type)throw Error(n(17));if(h)throw Error(n(9));try{h=!0,u=a(u,e)}finally{h=!1}return(s=f).forEach(e=>{e()}),e}return g({type:o.INIT}),{dispatch:g,subscribe:v,getState:y,replaceReducer:function(e){if("function"!=typeof e)throw Error(n(10));a=e,g({type:o.REPLACE})},[i]:function(){return{subscribe(e){if("object"!=typeof e||null===e)throw Error(n(11));function t(){e.next&&e.next(y())}return t(),{unsubscribe:v(t)}},[i](){return this}}}}}function u(e){let t,r=Object.keys(e),i={};for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof e[n]&&(i[n]=e[n])}let a=Object.keys(i);try{Object.keys(i).forEach(e=>{let t=i[e];if(void 0===t(void 0,{type:o.INIT}))throw Error(n(12));if(void 0===t(void 0,{type:o.PROBE_UNKNOWN_ACTION()}))throw Error(n(13))})}catch(e){t=e}return function(e={},r){if(t)throw t;let o=!1,l={};for(let t=0;t<a.length;t++){let c=a[t],u=i[c],s=e[c],f=u(s,r);if(void 0===f)throw r&&r.type,Error(n(14));l[c]=f,o=o||f!==s}return(o=o||a.length!==Object.keys(e).length)?l:e}}function s(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce((e,t)=>(...r)=>e(t(...r)))}function f(...e){return t=>(r,i)=>{let a=t(r,i),o=()=>{throw Error(n(15))},l={getState:a.getState,dispatch:(e,...t)=>o(e,...t)};return o=s(...e.map(e=>e(l)))(a.dispatch),{...a,dispatch:o}}}function d(e){return l(e)&&"type"in e&&"string"==typeof e.type}},6942:(e,t,r)=>{"use strict";r.d(t,{x:()=>o,y:()=>a});var n=r(2481),i=r(6113),a=(0,n.VP)("externalEvent"),o=(0,n.Nc)();o.startListening({actionCreator:a,effect:(e,t)=>{if(null!=e.payload.handler){var r=t.getState(),n={activeCoordinate:(0,i.eE)(r),activeDataKey:(0,i.Xb)(r),activeIndex:(0,i.A2)(r),activeLabel:(0,i.BZ)(r),activeTooltipIndex:(0,i.A2)(r),isTooltipActive:(0,i.yn)(r)};e.payload.handler(n,e.payload.reactEvent)}}})},6973:(e,t,r)=>{"use strict";r.d(t,{BZ:()=>D,aX:()=>z,dS:()=>C,dp:()=>k,fW:()=>O,pg:()=>T,r1:()=>M,u9:()=>N,yn:()=>I});var n=r(7242),i=r(7206),a=r.n(i),o=r(9684),l=r(9058),c=r(5101),u=r(6113),s=r(3361),f=r(4397),d=r(3401),h=r(5336),p=r(4721),y=r(2383),v=r(594),g=r(4363),m=r(2739),b=r(9361),x=r(2157),w=r(1433),O=()=>(0,o.G)(s.iO),j=(e,t)=>t,S=(e,t,r)=>r,P=(e,t,r,n)=>n,M=(0,n.Mz)(u.R4,e=>a()(e,e=>e.coordinate)),A=(0,n.Mz)([x.J,j,S,P],y.i),E=(0,n.Mz)([A,u.n4],v.P),k=(e,t,r)=>{if(null!=t){var n=(0,x.J)(e);return"axis"===t?"hover"===r?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:"hover"===r?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}},_=(0,n.Mz)([x.J,j,S,P],m.q),T=(0,n.Mz)([h.Lp,h.A$,f.fz,d.HZ,u.R4,P,_,b.x],g.o),C=(0,n.Mz)([A,T],(e,t)=>{var r;return null!=(r=e.coordinate)?r:t}),D=(0,n.Mz)(u.R4,E,p.E),N=(0,n.Mz)([_,E,c.LF,u.Dn,D,b.x,j],w.N),I=(0,n.Mz)([A],e=>({isActive:e.active,activeIndex:e.index})),z=(e,t,r,n,i,a,o,c)=>{if(e&&t&&n&&i&&a){var u=(0,l.r4)(e.chartX,e.chartY,t,r,c);if(u){var s=(0,l.SW)(u,t),f=(0,l.gH)(s,o,a,n,i),d=(0,l.bk)(t,a,f,u);return{activeIndex:String(f),activeCoordinate:d}}}}},7063:(e,t,r)=>{e.exports=r(9952).get},7157:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.eq=function(e,t){return e===t||Number.isNaN(e)&&Number.isNaN(t)}},7171:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,_:()=>l}),r(2015);var n=r(7580),i=r(4397),a=r(9684);function o(e){var{legendPayload:t}=e;return(0,a.j)(),(0,n.r)(),null}function l(e){var{legendPayload:t}=e;return(0,a.j)(),(0,a.G)(i.fz),null}r(123)},7202:(e,t,r)=>{"use strict";r.d(t,{LV:()=>l,M:()=>a,hq:()=>i});var n=(0,r(2481).Z0)({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(e,t){if(e.chartData=t.payload,null==t.payload){e.dataStartIndex=0,e.dataEndIndex=0;return}t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;null!=r&&(e.dataStartIndex=r),null!=n&&(e.dataEndIndex=n)}}}),{setChartData:i,setDataStartEndIndexes:a,setComputedData:o}=n.actions,l=n.reducer},7206:(e,t,r)=>{e.exports=r(2641).sortBy},7242:(e,t,r)=>{"use strict";r.d(t,{Mz:()=>w});var n=e=>Array.isArray(e)?e:[e],i=0,a=null,o=class{revision=i;_value;_lastValue;_isEqual=l;constructor(e,t=l){this._value=this._lastValue=e,this._isEqual=t}get value(){return a?.add(this),this._value}set value(e){this.value!==e&&(this._value=e,this.revision=++i)}};function l(e,t){return e===t}function c(e){return e instanceof o||console.warn("Not a valid cell! ",e),e.value}var u=(e,t)=>!1;function s(){return function(e,t=l){return new o(null,t)}(0,u)}var f=e=>{let t=e.collectionTag;null===t&&(t=e.collectionTag=s()),c(t)};Symbol();var d=0,h=Object.getPrototypeOf({}),p=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy(this,y);tag=s();tags={};children={};collectionTag=null;id=d++},y={get:(e,t)=>(function(){let{value:r}=e,n=Reflect.get(r,t);if("symbol"==typeof t||t in h)return n;if("object"==typeof n&&null!==n){let r=e.children[t];return void 0===r&&(r=e.children[t]=function(e){return Array.isArray(e)?new v(e):new p(e)}(n)),r.tag&&c(r.tag),r.proxy}{let r=e.tags[t];return void 0===r&&((r=e.tags[t]=s()).value=n),c(r),n}})(),ownKeys:e=>(f(e),Reflect.ownKeys(e.value)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e.value,t),has:(e,t)=>Reflect.has(e.value,t)},v=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy([this],g);tag=s();tags={};children={};collectionTag=null;id=d++},g={get:([e],t)=>("length"===t&&f(e),y.get(e,t)),ownKeys:([e])=>y.ownKeys(e),getOwnPropertyDescriptor:([e],t)=>y.getOwnPropertyDescriptor(e,t),has:([e],t)=>y.has(e,t)},m="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function b(){return{s:0,v:void 0,o:null,p:null}}function x(e,t={}){let r,n=b(),{resultEqualityCheck:i}=t,a=0;function o(){let t,o=n,{length:l}=arguments;for(let e=0;e<l;e++){let t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=o.o;null===e&&(o.o=e=new WeakMap);let r=e.get(t);void 0===r?(o=b(),e.set(t,o)):o=r}else{let e=o.p;null===e&&(o.p=e=new Map);let r=e.get(t);void 0===r?(o=b(),e.set(t,o)):o=r}}let c=o;if(1===o.s)t=o.v;else if(t=e.apply(null,arguments),a++,i){let e=r?.deref?.()??r;null!=e&&i(e,t)&&(t=e,0!==a&&a--),r="object"==typeof t&&null!==t||"function"==typeof t?new m(t):t}return c.s=1,c.v=t,t}return o.clearCache=()=>{n=b(),o.resetResultsCount()},o.resultsCount=()=>a,o.resetResultsCount=()=>{a=0},o}var w=function(e,...t){let r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,i=(...e)=>{let t,i=0,a=0,o={},l=e.pop();"object"==typeof l&&(o=l,l=e.pop()),function(e,t=`expected a function, instead received ${typeof e}`){if("function"!=typeof e)throw TypeError(t)}(l,`createSelector expects an output function after the inputs, but received: [${typeof l}]`);let{memoize:c,memoizeOptions:u=[],argsMemoize:s=x,argsMemoizeOptions:f=[],devModeChecks:d={}}={...r,...o},h=n(u),p=n(f),y=function(e){let t=Array.isArray(e[0])?e[0]:e;return!function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){let r=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}(e),v=c(function(){return i++,l.apply(null,arguments)},...h);return Object.assign(s(function(){a++;let e=function(e,t){let r=[],{length:n}=e;for(let i=0;i<n;i++)r.push(e[i].apply(null,t));return r}(y,arguments);return t=v.apply(null,e)},...p),{resultFunc:l,memoizedResultFunc:v,dependencies:y,dependencyRecomputations:()=>a,resetDependencyRecomputations:()=>{a=0},lastResult:()=>t,recomputations:()=>i,resetRecomputations:()=>{i=0},memoize:c,argsMemoize:s})};return Object.assign(i,{withTypes:()=>i}),i}(x),O=Object.assign((e,t=w)=>{!function(e,t=`expected an object, instead received ${typeof e}`){if("object"!=typeof e)throw TypeError(t)}(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);let r=Object.keys(e);return t(r.map(t=>e[t]),(...e)=>e.reduce((e,t,n)=>(e[r[n]]=t,e),{}))},{withTypes:()=>O})},7254:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if(!e||"object"!=typeof e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}},7269:(e,t,r)=>{"use strict";r.d(t,{W:()=>c});var n=r(2015),i=r(9486),a=r(2661),o=["children","className"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var c=n.forwardRef((e,t)=>{var{children:r,className:c}=e,u=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,o),s=(0,i.$)("recharts-layer",c);return n.createElement("g",l({className:s},(0,a.J9)(u,!0),{ref:t}),r)})},7294:(e,t,r)=>{"use strict";r.d(t,{E:()=>g});var n=r(2015),i=r(8208),a=r(3414),o=r(9491),l=r(5239),c=r(672),u=r(8352),s=r(4644),f=r(7463),d=["width","height"];function h(){return(h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var p={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},y=(0,n.forwardRef)(function(e,t){var r,i=(0,s.e)(e.categoricalChartProps,p),{width:y,height:v}=i,g=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,d);if(!(0,f.F)(y)||!(0,f.F)(v))return null;var{chartName:m,defaultTooltipEventType:b,validateTooltipEventTypes:x,tooltipPayloadSearcher:w,categoricalChartProps:O}=e;return n.createElement(a.J,{preloadedState:{options:{chartName:m,defaultTooltipEventType:b,validateTooltipEventTypes:x,tooltipPayloadSearcher:w,eventEmitter:void 0}},reduxStoreName:null!=(r=O.id)?r:m},n.createElement(o.TK,{chartData:O.data}),n.createElement(l.s,{width:y,height:v,layout:i.layout,margin:i.margin}),n.createElement(c.p,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),n.createElement(u.L,h({},g,{width:y,height:v,ref:t})))}),v=["axis","item"],g=(0,n.forwardRef)((e,t)=>n.createElement(y,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:v,tooltipPayloadSearcher:i.uN,categoricalChartProps:e,ref:t}))},7463:(e,t,r)=>{"use strict";function n(e){return Number.isFinite(e)}function i(e){return"number"==typeof e&&e>0&&Number.isFinite(e)}r.d(t,{F:()=>i,H:()=>n})},7555:(e,t,r)=>{"use strict";r.d(t,{Qx:()=>u,a6:()=>s,h4:()=>G,jM:()=>H,ss:()=>U});var n,i=Symbol.for("immer-nothing"),a=Symbol.for("immer-draftable"),o=Symbol.for("immer-state");function l(e,...t){throw Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var c=Object.getPrototypeOf;function u(e){return!!e&&!!e[o]}function s(e){return!!e&&(d(e)||Array.isArray(e)||!!e[a]||!!e.constructor?.[a]||g(e)||m(e))}var f=Object.prototype.constructor.toString();function d(e){if(!e||"object"!=typeof e)return!1;let t=c(e);if(null===t)return!0;let r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===f}function h(e,t){0===p(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function p(e){let t=e[o];return t?t.type_:Array.isArray(e)?1:g(e)?2:3*!!m(e)}function y(e,t){return 2===p(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function v(e,t,r){let n=p(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function g(e){return e instanceof Map}function m(e){return e instanceof Set}function b(e){return e.copy_||e.base_}function x(e,t){if(g(e))return new Map(e);if(m(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let r=d(e);if(!0!==t&&("class_only"!==t||r)){let t=c(e);return null!==t&&r?{...e}:Object.assign(Object.create(t),e)}{let t=Object.getOwnPropertyDescriptors(e);delete t[o];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){let i=r[n],a=t[i];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(t[i]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[i]})}return Object.create(c(e),t)}}function w(e,t=!1){return j(e)||u(e)||!s(e)||(p(e)>1&&(e.set=e.add=e.clear=e.delete=O),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>w(t,!0))),e}function O(){l(2)}function j(e){return Object.isFrozen(e)}var S={};function P(e){let t=S[e];return t||l(0,e),t}function M(e,t){t&&(P("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function A(e){E(e),e.drafts_.forEach(_),e.drafts_=null}function E(e){e===n&&(n=e.parent_)}function k(e){return n={drafts_:[],parent_:n,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function _(e){let t=e[o];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function T(e,t){t.unfinalizedDrafts_=t.drafts_.length;let r=t.drafts_[0];return void 0!==e&&e!==r?(r[o].modified_&&(A(t),l(4)),s(e)&&(e=C(t,e),t.parent_||N(t,e)),t.patches_&&P("Patches").generateReplacementPatches_(r[o].base_,e,t.patches_,t.inversePatches_)):e=C(t,r,[]),A(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==i?e:void 0}function C(e,t,r){if(j(t))return t;let n=t[o];if(!n)return h(t,(i,a)=>D(e,n,t,i,a,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return N(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;let t=n.copy_,i=t,a=!1;3===n.type_&&(i=new Set(t),t.clear(),a=!0),h(i,(i,o)=>D(e,n,t,i,o,r,a)),N(e,t,!1),r&&e.patches_&&P("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function D(e,t,r,n,i,a,o){if(u(i)){let o=C(e,i,a&&t&&3!==t.type_&&!y(t.assigned_,n)?a.concat(n):void 0);if(v(r,n,o),!u(o))return;e.canAutoFreeze_=!1}else o&&r.add(i);if(s(i)&&!j(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;C(e,i),(!t||!t.scope_.parent_)&&"symbol"!=typeof n&&Object.prototype.propertyIsEnumerable.call(r,n)&&N(e,i)}}function N(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&w(t,r)}var I={get(e,t){if(t===o)return e;let r=b(e);if(!y(r,t)){var n=e,i=r,a=t;let o=R(i,a);return o?"value"in o?o.value:o.get?.call(n.draft_):void 0}let l=r[t];return e.finalized_||!s(l)?l:l===L(e.base_,t)?(B(e),e.copy_[t]=F(l,e)):l},has:(e,t)=>t in b(e),ownKeys:e=>Reflect.ownKeys(b(e)),set(e,t,r){let n=R(b(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){let n=L(b(e),t),i=n?.[o];if(i&&i.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if((r===n?0!==r||1/r==1/n:r!=r&&n!=n)&&(void 0!==r||y(e.base_,t)))return!0;B(e),$(e)}return!!(e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t]))||(e.copy_[t]=r,e.assigned_[t]=!0,!0)},deleteProperty:(e,t)=>(void 0!==L(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,B(e),$(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){let r=b(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){l(11)},getPrototypeOf:e=>c(e.base_),setPrototypeOf(){l(12)}},z={};function L(e,t){let r=e[o];return(r?b(r):e)[t]}function R(e,t){if(!(t in e))return;let r=c(e);for(;r;){let e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=c(r)}}function $(e){!e.modified_&&(e.modified_=!0,e.parent_&&$(e.parent_))}function B(e){e.copy_||(e.copy_=x(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function F(e,t){let r=g(e)?P("MapSet").proxyMap_(e,t):m(e)?P("MapSet").proxySet_(e,t):function(e,t){let r=Array.isArray(e),i={type_:+!!r,scope_:t?t.scope_:n,modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1},a=i,o=I;r&&(a=[i],o=z);let{revoke:l,proxy:c}=Proxy.revocable(a,o);return i.draft_=c,i.revoke_=l,c}(e,t);return(t?t.scope_:n).drafts_.push(r),r}function U(e){return u(e)||l(10,e),function e(t){let r;if(!s(t)||j(t))return t;let n=t[o];if(n){if(!n.modified_)return n.base_;n.finalized_=!0,r=x(t,n.scope_.immer_.useStrictShallowCopy_)}else r=x(t,!0);return h(r,(t,n)=>{v(r,t,e(n))}),n&&(n.finalized_=!1),r}(e)}h(I,(e,t)=>{z[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),z.deleteProperty=function(e,t){return z.set.call(this,e,t,void 0)},z.set=function(e,t,r){return I.set.call(this,e[0],t,r,e[0])};var K=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{let n;if("function"==typeof e&&"function"!=typeof t){let r=t;t=e;let n=this;return function(e=r,...i){return n.produce(e,e=>t.call(this,e,...i))}}if("function"!=typeof t&&l(6),void 0!==r&&"function"!=typeof r&&l(7),s(e)){let i=k(this),a=F(e,void 0),o=!0;try{n=t(a),o=!1}finally{o?A(i):E(i)}return M(i,r),T(n,i)}if(e&&"object"==typeof e)l(1,e);else{if(void 0===(n=t(e))&&(n=e),n===i&&(n=void 0),this.autoFreeze_&&w(n,!0),r){let t=[],i=[];P("Patches").generateReplacementPatches_(e,n,t,i),r(t,i)}return n}},this.produceWithPatches=(e,t)=>{let r,n;return"function"==typeof e?(t,...r)=>this.produceWithPatches(t,t=>e(t,...r)):[this.produce(e,t,(e,t)=>{r=e,n=t}),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){s(e)||l(8),u(e)&&(e=U(e));let t=k(this),r=F(e,void 0);return r[o].isManual_=!0,E(t),r}finishDraft(e,t){let r=e&&e[o];r&&r.isManual_||l(9);let{scope_:n}=r;return M(n,t),T(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){let n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));let n=P("Patches").applyPatches_;return u(e)?n(e,t):this.produce(e,e=>n(e,t))}},H=K.produce;function G(e){return e}K.produceWithPatches.bind(K),K.setAutoFreeze.bind(K),K.setUseStrictShallowCopy.bind(K),K.applyPatches.bind(K),K.createDraft.bind(K),K.finishDraft.bind(K)},7580:(e,t,r)=>{"use strict";r.d(t,{r:()=>a});var n=r(2015),i=(0,n.createContext)(null),a=()=>null!=(0,n.useContext)(i)},7634:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isSymbol=function(e){return"symbol"==typeof e||e instanceof Symbol}},7645:(e,t,r)=>{"use strict";r(2915)},7672:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.d(t,{o:()=>u});var i=r(8732);r(2015);var a=r(9733),o=r(6281),l=r.n(o),c=e([a]);a=(c.then?(await c)():c)[0];let u=({title:e,description:t,icon:r,href:n,color:o,gradient:c,accentColor:u,disabled:s=!1,experimental:f=!1})=>{let d=n&&"#"!==n&&!s,h=(0,i.jsx)(a.Box,{px:10,py:5,bg:c?`linear-gradient(135deg, ${c.from}, ${c.to})`:"gray.800",borderRadius:"lg",border:"1px solid",borderColor:s?"whiteAlpha.100":"whiteAlpha.200",transition:"all 0.3s",h:"140px",minW:"360px",w:"full",overflow:"hidden",display:"flex",flexDirection:"column",cursor:d?"pointer":s?"not-allowed":"default",position:"relative",opacity:s?.6:1,_before:{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,bg:f?"repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(0,0,0,0.1) 10px, rgba(0,0,0,0.1) 20px)":"none",opacity:.5,pointerEvents:"none"},_hover:d?{transform:"translateY(-3px)",boxShadow:`0 6px 14px ${u||`var(--chakra-colors-${o}-900)`}40`,borderColor:`${o}.400`,_before:{opacity:.7}}:{},children:(0,i.jsxs)(a.VStack,{spacing:4,align:"start",flex:"1",justify:"flex-start",h:"full",position:"relative",zIndex:1,children:[(0,i.jsxs)(a.HStack,{spacing:3,children:[(0,i.jsx)(a.Icon,{as:r,boxSize:6,color:u||`${o}.300`,filter:f?"drop-shadow(0 0 2px currentColor)":"none"}),(0,i.jsx)(a.Heading,{size:"md",color:"white",noOfLines:1,whiteSpace:"nowrap",children:e})]}),(0,i.jsx)(a.Text,{color:s?"gray.500":"gray.300",fontSize:"sm",lineHeight:"1.4",noOfLines:3,overflow:"hidden",textOverflow:"ellipsis",flex:"1",children:t})]})});return d?(0,i.jsx)(l(),{href:n,passHref:!0,children:h}):h};n()}catch(e){n(e)}})},7812:(e,t,r)=>{e.exports=r(5492).range},7910:e=>{"use strict";e.exports=require("stream")},8050:(e,t,r)=>{"use strict";r.d(t,{P:()=>u});var n=r(4056);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var o={widthCache:{},cacheCount:0},l={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},c="recharts_measurement_span",u=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||n.m.isSsr)return{width:0,height:0};var i=(Object.keys(t=a({},r)).forEach(e=>{t[e]||delete t[e]}),t),u=JSON.stringify({text:e,copyStyle:i});if(o.widthCache[u])return o.widthCache[u];try{var s=document.getElementById(c);s||((s=document.createElement("span")).setAttribute("id",c),s.setAttribute("aria-hidden","true"),document.body.appendChild(s));var f=a(a({},l),i);Object.assign(s.style,f),s.textContent="".concat(e);var d=s.getBoundingClientRect(),h={width:d.width,height:d.height};return o.widthCache[u]=h,++o.cacheCount>2e3&&(o.cacheCount=0,o.widthCache={}),h}catch(e){return{width:0,height:0}}}},8097:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.last=function(e){return e[e.length-1]}},8098:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObjectLike=function(e){return"object"==typeof e&&null!==e}},8208:(e,t,r)=>{"use strict";r.d(t,{dl:()=>c,lJ:()=>l,uN:()=>a});var n=r(2481),i=r(4504);function a(e,t){if(t){var r=Number.parseInt(t,10);if(!(0,i.M8)(r))return null==e?void 0:e[r]}}var o=(0,n.Z0)({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:e=>{null==e.eventEmitter&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),l=o.reducer,{createEventEmitter:c}=o.actions},8221:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.uniqBy=function(e,t){let r=new Map;for(let n=0;n<e.length;n++){let i=e[n],a=t(i);r.has(a)||r.set(a,i)}return Array.from(r.values())}},8224:(e,t,r)=>{"use strict";r.d(t,{B_:()=>i,JK:()=>a,Vp:()=>c,gX:()=>o,hF:()=>l});var n=(0,r(2481).Z0)({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:i,setLayout:a,setChartSize:o,setScale:l}=n.actions,c=n.reducer},8232:(e,t,r)=>{"use strict";r.d(t,{i:()=>c});let n=Math.PI,i=2*n,a=i-1e-6;function o(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class l{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?o:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return o;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,i,a){this._append`C${+e},${+t},${+r},${+n},${this._x1=+i},${this._y1=+a}`}arcTo(e,t,r,i,a){if(e*=1,t*=1,r*=1,i*=1,(a*=1)<0)throw Error(`negative radius: ${a}`);let o=this._x1,l=this._y1,c=r-e,u=i-t,s=o-e,f=l-t,d=s*s+f*f;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(d>1e-6)if(Math.abs(f*c-u*s)>1e-6&&a){let h=r-o,p=i-l,y=c*c+u*u,v=Math.sqrt(y),g=Math.sqrt(d),m=a*Math.tan((n-Math.acos((y+d-(h*h+p*p))/(2*v*g)))/2),b=m/g,x=m/v;Math.abs(b-1)>1e-6&&this._append`L${e+b*s},${t+b*f}`,this._append`A${a},${a},0,0,${+(f*h>s*p)},${this._x1=e+x*c},${this._y1=t+x*u}`}else this._append`L${this._x1=e},${this._y1=t}`}arc(e,t,r,o,l,c){if(e*=1,t*=1,r*=1,c=!!c,r<0)throw Error(`negative radius: ${r}`);let u=r*Math.cos(o),s=r*Math.sin(o),f=e+u,d=t+s,h=1^c,p=c?o-l:l-o;null===this._x1?this._append`M${f},${d}`:(Math.abs(this._x1-f)>1e-6||Math.abs(this._y1-d)>1e-6)&&this._append`L${f},${d}`,r&&(p<0&&(p=p%i+i),p>a?this._append`A${r},${r},0,1,${h},${e-u},${t-s}A${r},${r},0,1,${h},${this._x1=f},${this._y1=d}`:p>1e-6&&this._append`A${r},${r},0,${+(p>=n)},${h},${this._x1=e+r*Math.cos(l)},${this._y1=t+r*Math.sin(l)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function c(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new l(t)}l.prototype},8258:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8923);t.isArguments=function(e){return null!==e&&"object"==typeof e&&"[object Arguments]"===n.getTag(e)}},8352:(e,t,r)=>{"use strict";r.d(t,{L:()=>L});var n=r(2015),i=r(2661),a=r(4397),o=r(2579),l=r(7580),c=r(9486),u=["children","width","height","viewBox","className","style","title","desc"];function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var f=(0,n.forwardRef)((e,t)=>{var{children:r,width:a,height:o,viewBox:l,className:f,style:d,title:h,desc:p}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,u),v=l||{width:a,height:o,x:0,y:0},g=(0,c.$)("recharts-surface",f);return n.createElement("svg",s({},(0,i.J9)(y,!0,"svg"),{className:g,width:a,height:o,style:d,viewBox:"".concat(v.x," ").concat(v.y," ").concat(v.width," ").concat(v.height),ref:t}),n.createElement("title",null,h),n.createElement("desc",null,p),r)}),d=r(9684),h=r(4335),p=r(7463),y=["children"];function v(){return(v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var g={width:"100%",height:"100%"},m=(0,n.forwardRef)((e,t)=>{var r,i,l=(0,a.yi)(),c=(0,a.rY)(),u=(0,o.$)();if(!(0,p.F)(l)||!(0,p.F)(c))return null;var{children:s,otherAttributes:d,title:h,desc:y}=e;return r="number"==typeof d.tabIndex?d.tabIndex:u?0:void 0,i="string"==typeof d.role?d.role:u?"application":void 0,n.createElement(f,v({},d,{title:h,desc:y,role:i,tabIndex:r,width:l,height:c,style:g,ref:t}),s)}),b=e=>{var{children:t}=e,r=(0,d.G)(h.U);if(!r)return null;var{width:i,height:a,y:o,x:l}=r;return n.createElement(f,{width:i,height:a,x:l,y:o},t)},x=(0,n.forwardRef)((e,t)=>{var{children:r}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,y);return(0,l.r)()?n.createElement(b,null,r):n.createElement(m,v({ref:t},i),r)}),w=r(3497),O=r(8934),j=r(3713),S=r(2428),P=r(5336);r(8224);var M=r(6942),A=r(6626),E=r(5046),k=(0,n.createContext)(null);function _(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var T=(0,n.forwardRef)((e,t)=>{var{children:r,className:i,height:a,onClick:o,onContextMenu:l,onDoubleClick:u,onMouseDown:s,onMouseEnter:f,onMouseLeave:h,onMouseMove:p,onMouseUp:y,onTouchEnd:v,onTouchMove:g,onTouchStart:m,style:b,width:x}=e,T=(0,d.j)(),[C,D]=(0,n.useState)(null),[N,I]=(0,n.useState)(null);(0,j.l3)();var z=function(){(0,d.j)();var[e,t]=(0,n.useState)(null);return(0,d.G)(P.et),t}(),L=(0,n.useCallback)(e=>{z(e),"function"==typeof t&&t(e),D(e),I(e)},[z,t,D,I]),R=(0,n.useCallback)(e=>{T((0,O.ky)(e)),T((0,M.y)({handler:o,reactEvent:e}))},[T,o]),$=(0,n.useCallback)(e=>{T((0,O.dj)(e)),T((0,M.y)({handler:f,reactEvent:e}))},[T,f]),B=(0,n.useCallback)(e=>{T((0,w.xS)()),T((0,M.y)({handler:h,reactEvent:e}))},[T,h]),F=(0,n.useCallback)(e=>{T((0,O.dj)(e)),T((0,M.y)({handler:p,reactEvent:e}))},[T,p]),U=(0,n.useCallback)(()=>{T((0,S.Ru)())},[T]),K=(0,n.useCallback)(e=>{T((0,S.uZ)(e.key))},[T]),H=(0,n.useCallback)(e=>{T((0,M.y)({handler:l,reactEvent:e}))},[T,l]),G=(0,n.useCallback)(e=>{T((0,M.y)({handler:u,reactEvent:e}))},[T,u]),W=(0,n.useCallback)(e=>{T((0,M.y)({handler:s,reactEvent:e}))},[T,s]),Z=(0,n.useCallback)(e=>{T((0,M.y)({handler:y,reactEvent:e}))},[T,y]),q=(0,n.useCallback)(e=>{T((0,M.y)({handler:m,reactEvent:e}))},[T,m]),V=(0,n.useCallback)(e=>{T((0,A.e)(e)),T((0,M.y)({handler:g,reactEvent:e}))},[T,g]),Y=(0,n.useCallback)(e=>{T((0,M.y)({handler:v,reactEvent:e}))},[T,v]);return n.createElement(E.$.Provider,{value:C},n.createElement(k.Provider,{value:N},n.createElement("div",{className:(0,c.$)("recharts-wrapper",i),style:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({position:"relative",cursor:"default",width:x,height:a},b),onClick:R,onContextMenu:H,onDoubleClick:G,onFocus:U,onKeyDown:K,onMouseDown:W,onMouseEnter:$,onMouseLeave:B,onMouseMove:F,onMouseUp:Z,onTouchEnd:Y,onTouchMove:V,onTouchStart:q,ref:L},r)))}),C=r(4504),D=r(3362),N=(0,n.createContext)(void 0),I=e=>{var{children:t}=e,[r]=(0,n.useState)("".concat((0,C.NF)("recharts"),"-clip")),i=(0,D.oM)();if(null==i)return null;var{x:a,y:o,width:l,height:c}=i;return n.createElement(N.Provider,{value:r},n.createElement("defs",null,n.createElement("clipPath",{id:r},n.createElement("rect",{x:a,y:o,height:c,width:l}))),t)},z=["children","className","width","height","style","compact","title","desc"],L=(0,n.forwardRef)((e,t)=>{var{children:r,className:a,width:o,height:l,style:c,compact:u,title:s,desc:f}=e,d=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,z),h=(0,i.J9)(d,!1);return u?n.createElement(x,{otherAttributes:h,title:s,desc:f},r):n.createElement(T,{className:a,style:c,width:o,height:l,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},n.createElement(x,{otherAttributes:h,title:s,desc:f,ref:t},n.createElement(I,null,r)))})},8702:(e,t,r)=>{"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}r.d(t,{IZ:()=>l,Kg:()=>a,lY:()=>c,yy:()=>h}),r(2015);var a=Math.PI/180,o=e=>180*e/Math.PI,l=(e,t,r,n)=>({x:e+Math.cos(-a*n)*r,y:t+Math.sin(-a*n)*r}),c=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},u=(e,t)=>{var{x:r,y:n}=e,{x:i,y:a}=t;return Math.sqrt((r-i)**2+(n-a)**2)},s=(e,t)=>{var{x:r,y:n}=e,{cx:i,cy:a}=t,l=u({x:r,y:n},{x:i,y:a});if(l<=0)return{radius:l,angle:0};var c=Math.acos((r-i)/l);return n>a&&(c=2*Math.PI-c),{radius:l,angle:o(c),angleInRadian:c}},f=e=>{var{startAngle:t,endAngle:r}=e,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}},d=(e,t)=>{var{startAngle:r,endAngle:n}=t;return e+360*Math.min(Math.floor(r/360),Math.floor(n/360))},h=(e,t)=>{var r,{x:n,y:a}=e,{radius:o,angle:l}=s({x:n,y:a},t),{innerRadius:c,outerRadius:u}=t;if(o<c||o>u||0===o)return null;var{startAngle:h,endAngle:p}=f(t),y=l;if(h<=p){for(;y>p;)y-=360;for(;y<h;)y+=360;r=y>=h&&y<=p}else{for(;y>h;)y-=360;for(;y<p;)y+=360;r=y>=p&&y<=h}return r?i(i({},t),{},{radius:o,angle:d(y,t)}):null}},8708:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(753),i=r(360),a=r(9359),o=r(7157);t.isIterateeCall=function(e,t,r){return!!a.isObject(r)&&(!!("number"==typeof t&&i.isArrayLike(r)&&n.isIndex(t))&&t<r.length||"string"==typeof t&&t in r)&&o.eq(r[t],e)}},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")},8776:(e,t,r)=>{"use strict";r.d(t,{w:()=>n});var n=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}}},8782:(e,t)=>{"use strict";function r(e){return"symbol"==typeof e?1:null===e?2:void 0===e?3:4*(e!=e)}Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.compareValues=(e,t,n)=>{if(e!==t){let i=r(e),a=r(t);if(i===a&&0===i){if(e<t)return"desc"===n?1:-1;if(e>t)return"desc"===n?-1:1}return"desc"===n?a-i:i-a}return 0}},8923:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getTag=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}},8934:(e,t,r)=>{"use strict";r.d(t,{YF:()=>u,dj:()=>s,fP:()=>f,ky:()=>c});var n=r(2481),i=r(3497),a=r(3764),o=r(5306),l=r(8776),c=(0,n.VP)("mouseClick"),u=(0,n.Nc)();u.startListening({actionCreator:c,effect:(e,t)=>{var r=e.payload,n=(0,a.g)(t.getState(),(0,l.w)(r));(null==n?void 0:n.activeIndex)!=null&&t.dispatch((0,i.jF)({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var s=(0,n.VP)("mouseMove"),f=(0,n.Nc)();f.startListening({actionCreator:s,effect:(e,t)=>{var r=e.payload,n=t.getState(),c=(0,o.au)(n,n.tooltip.settings.shared),u=(0,a.g)(n,(0,l.w)(r));"axis"===c&&((null==u?void 0:u.activeIndex)!=null?t.dispatch((0,i.Nt)({activeIndex:u.activeIndex,activeDataKey:void 0,activeCoordinate:u.activeCoordinate})):t.dispatch((0,i.xS)()))}})},8997:(e,t,r)=>{"use strict";var n=r(2015),i=r(6315),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=i.useSyncExternalStore,l=n.useRef,c=n.useEffect,u=n.useMemo,s=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var f=l(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;var h=o(e,(f=u(function(){function e(e){if(!c){if(c=!0,o=e,e=n(e),void 0!==i&&d.hasValue){var t=d.value;if(i(t,e))return l=t}return l=e}if(t=l,a(o,e))return t;var r=n(e);return void 0!==i&&i(t,r)?(o=e,t):(o=e,l=r)}var o,l,c=!1,u=void 0===r?null:r;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]},[t,r,n,i]))[0],f[1]);return c(function(){d.hasValue=!0,d.value=h},[h]),s(h),h}},9021:e=>{"use strict";e.exports=require("fs")},9044:(e,t,r)=>{"use strict";r.d(t,{QQ:()=>i,VU:()=>o,XC:()=>s,_U:()=>c,j2:()=>l});var n=r(2015),i=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],a=["points","pathLength"],o={svg:["viewBox","children"],polygon:a,polyline:a},l=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],c=(e,t)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,n.isValidElement)(e)&&(r=e.props),"object"!=typeof r&&"function"!=typeof r)return null;var i={};return Object.keys(r).forEach(e=>{l.includes(e)&&(i[e]=t||(t=>r[e](r,t)))}),i},u=(e,t,r)=>n=>(e(t,r,n),null),s=(e,t,r)=>{if(null===e||"object"!=typeof e&&"function"!=typeof e)return null;var n=null;return Object.keys(e).forEach(i=>{var a=e[i];l.includes(i)&&"function"==typeof a&&(n||(n={}),n[i]=u(a,t,r))}),n}},9058:(e,t,r)=>{"use strict";r.d(t,{qx:()=>N,IH:()=>D,s0:()=>b,gH:()=>m,SW:()=>B,YB:()=>j,bk:()=>$,Hj:()=>I,DW:()=>k,y2:()=>E,PW:()=>w,Mk:()=>C,$8:()=>A,yy:()=>M,Rh:()=>O,GF:()=>z,uM:()=>L,kr:()=>g,r4:()=>R,_L:()=>x,_f:()=>S});var n=r(7206),i=r.n(n),a=r(7063),o=r.n(a);function l(e,t){if((i=e.length)>1)for(var r,n,i,a=1,o=e[t[0]],l=o.length;a<i;++a)for(n=o,o=e[t[a]],r=0;r<l;++r)o[r][1]+=o[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}var c=r(2835),u=r(2004);function s(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function f(e,t){return e[t]}function d(e){let t=[];return t.key=e,t}var h=r(4504),p=r(8702);function y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function g(e,t,r){return(0,h.uy)(e)||(0,h.uy)(t)?r:(0,h.vh)(t)?o()(e,t,r):"function"==typeof t?t(e):r}var m=(e,t,r,n,i)=>{var a,o=-1,l=null!=(a=null==t?void 0:t.length)?a:0;if(l<=1||null==e)return 0;if("angleAxis"===n&&null!=i&&1e-6>=Math.abs(Math.abs(i[1]-i[0])-360))for(var c=0;c<l;c++){var u=c>0?r[c-1].coordinate:r[l-1].coordinate,s=r[c].coordinate,f=c>=l-1?r[0].coordinate:r[c+1].coordinate,d=void 0;if((0,h.sA)(s-u)!==(0,h.sA)(f-s)){var p=[];if((0,h.sA)(f-s)===(0,h.sA)(i[1]-i[0])){d=f;var y=s+i[1]-i[0];p[0]=Math.min(y,(y+u)/2),p[1]=Math.max(y,(y+u)/2)}else{d=u;var v=f+i[1]-i[0];p[0]=Math.min(s,(v+s)/2),p[1]=Math.max(s,(v+s)/2)}var g=[Math.min(s,(d+s)/2),Math.max(s,(d+s)/2)];if(e>g[0]&&e<=g[1]||e>=p[0]&&e<=p[1]){({index:o}=r[c]);break}}else{var m=Math.min(u,f),b=Math.max(u,f);if(e>(m+s)/2&&e<=(b+s)/2){({index:o}=r[c]);break}}}else if(t){for(var x=0;x<l;x++)if(0===x&&e<=(t[x].coordinate+t[x+1].coordinate)/2||x>0&&x<l-1&&e>(t[x].coordinate+t[x-1].coordinate)/2&&e<=(t[x].coordinate+t[x+1].coordinate)/2||x===l-1&&e>(t[x].coordinate+t[x-1].coordinate)/2){({index:o}=t[x]);break}}return o},b=(e,t,r)=>{if(t&&r){var{width:n,height:i}=r,{align:a,verticalAlign:o,layout:l}=t;if(("vertical"===l||"horizontal"===l&&"middle"===o)&&"center"!==a&&(0,h.Et)(e[a]))return v(v({},e),{},{[a]:e[a]+(n||0)});if(("horizontal"===l||"vertical"===l&&"center"===a)&&"middle"!==o&&(0,h.Et)(e[o]))return v(v({},e),{},{[o]:e[o]+(i||0)})}return e},x=(e,t)=>"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t,w=(e,t,r,n)=>{if(n)return e.map(e=>e.coordinate);var i,a,o=e.map(e=>(e.coordinate===t&&(i=!0),e.coordinate===r&&(a=!0),e.coordinate));return i||o.push(t),a||o.push(r),o},O=(e,t,r)=>{if(!e)return null;var{duplicateDomain:n,type:i,range:a,scale:o,realScaleType:l,isCategorical:c,categoricalDomain:u,tickCount:s,ticks:f,niceTicks:d,axisType:p}=e;if(!o)return null;var y="scaleBand"===l&&o.bandwidth?o.bandwidth()/2:2,v=(t||r)&&"category"===i&&o.bandwidth?o.bandwidth()/y:0;return(v="angleAxis"===p&&a&&a.length>=2?2*(0,h.sA)(a[0]-a[1])*v:v,t&&(f||d))?(f||d||[]).map((e,t)=>({coordinate:o(n?n.indexOf(e):e)+v,value:e,offset:v,index:t})).filter(e=>!(0,h.M8)(e.coordinate)):c&&u?u.map((e,t)=>({coordinate:o(e)+v,value:e,index:t,offset:v})):o.ticks&&!r&&null!=s?o.ticks(s).map((e,t)=>({coordinate:o(e)+v,value:e,offset:v,index:t})):o.domain().map((e,t)=>({coordinate:o(e)+v,value:n?n[e]:e,index:t,offset:v}))},j=e=>{var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),i=Math.min(n[0],n[1])-1e-4,a=Math.max(n[0],n[1])+1e-4,o=e(t[0]),l=e(t[r-1]);(o<i||o>a||l<i||l>a)&&e.domain([t[0],t[r-1]])}},S=(e,t)=>{if(!t||2!==t.length||!(0,h.Et)(t[0])||!(0,h.Et)(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),i=[e[0],e[1]];return(!(0,h.Et)(e[0])||e[0]<r)&&(i[0]=r),(!(0,h.Et)(e[1])||e[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},P={sign:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0,o=0;o<t;++o){var l=(0,h.M8)(e[o][r][1])?e[o][r][0]:e[o][r][1];l>=0?(e[o][r][0]=i,e[o][r][1]=i+l,i=e[o][r][1]):(e[o][r][0]=a,e[o][r][1]=a+l,a=e[o][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,i,a=0,o=e[0].length;a<o;++a){for(i=r=0;r<n;++r)i+=e[r][a][1]||0;if(i)for(r=0;r<n;++r)e[r][a][1]/=i}l(e,t)}},none:l,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,i=e[t[0]],a=i.length;n<a;++n){for(var o=0,c=0;o<r;++o)c+=e[o][n][1]||0;i[n][1]+=i[n][0]=-c/2}l(e,t)}},wiggle:function(e,t){if((i=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,i,a=0,o=1;o<n;++o){for(var c=0,u=0,s=0;c<i;++c){for(var f=e[t[c]],d=f[o][1]||0,h=(d-(f[o-1][1]||0))/2,p=0;p<c;++p){var y=e[t[p]];h+=(y[o][1]||0)-(y[o-1][1]||0)}u+=d,s+=h*d}r[o-1][1]+=r[o-1][0]=a,u&&(a-=s/u)}r[o-1][1]+=r[o-1][0]=a,l(e,t)}},positive:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0;a<t;++a){var o=(0,h.M8)(e[a][r][1])?e[a][r][0]:e[a][r][1];o>=0?(e[a][r][0]=i,e[a][r][1]=i+o,i=e[a][r][1]):(e[a][r][0]=0,e[a][r][1]=0)}}},M=(e,t,r)=>{var n=P[r];return(function(){var e=(0,u.A)([]),t=s,r=l,n=f;function i(i){var a,o,l=Array.from(e.apply(this,arguments),d),u=l.length,s=-1;for(let e of i)for(a=0,++s;a<u;++a)(l[a][s]=[0,+n(e,l[a].key,s,i)]).data=e;for(a=0,o=(0,c.A)(t(l));a<u;++a)l[o[a]].index=a;return r(l,o),l}return i.keys=function(t){return arguments.length?(e="function"==typeof t?t:(0,u.A)(Array.from(t)),i):e},i.value=function(e){return arguments.length?(n="function"==typeof e?e:(0,u.A)(+e),i):n},i.order=function(e){return arguments.length?(t=null==e?s:"function"==typeof e?e:(0,u.A)(Array.from(e)),i):t},i.offset=function(e){return arguments.length?(r=null==e?l:e,i):r},i})().keys(t).value((e,t)=>+g(e,t,0)).order(s).offset(n)(e)};function A(e){return null==e?void 0:String(e)}var E=e=>{var{axis:t,ticks:r,offset:n,bandSize:i,entry:a,index:o}=e;if("category"===t.type)return r[o]?r[o].coordinate+n:null;var l=g(a,t.dataKey,t.scale.domain()[o]);return(0,h.uy)(l)?null:t.scale(l)-i/2+n},k=e=>{var{numericAxis:t}=e,r=t.scale.domain();if("number"===t.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},_=e=>{var t=e.flat(2).filter(h.Et);return[Math.min(...t),Math.max(...t)]},T=e=>[e[0]===1/0?0:e[0],e[1]===-1/0?0:e[1]],C=(e,t,r)=>{if(null!=e)return T(Object.keys(e).reduce((n,i)=>{var{stackedData:a}=e[i],o=a.reduce((e,n)=>{var i=_(n.slice(t,r+1));return[Math.min(e[0],i[0]),Math.max(e[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]))},D=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,N=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,I=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var a=i()(t,e=>e.coordinate),o=1/0,l=1,c=a.length;l<c;l++){var u=a[l],s=a[l-1];o=Math.min((u.coordinate||0)-(s.coordinate||0),o)}return o===1/0?0:o}return r?void 0:0};function z(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:i,name:a}=e;return v(v({},t),{},{dataKey:r,payload:n,value:i,name:a})}function L(e,t){return e?String(e):"string"==typeof t?t:void 0}function R(e,t,r,n,i){return"horizontal"===r||"vertical"===r?e>=i.left&&e<=i.left+i.width&&t>=i.top&&t<=i.top+i.height?{x:e,y:t}:null:n?(0,p.yy)({x:e,y:t},n):null}var $=(e,t,r,n)=>{var i=t.find(e=>e&&e.index===r);if(i){if("horizontal"===e)return{x:i.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:i.coordinate};if("centric"===e){var a=i.coordinate,{radius:o}=n;return v(v(v({},n),(0,p.IZ)(n.cx,n.cy,o,a)),{},{angle:a,radius:o})}var l=i.coordinate,{angle:c}=n;return v(v(v({},n),(0,p.IZ)(n.cx,n.cy,l,c)),{},{angle:c,radius:l})}return{x:0,y:0}},B=(e,t)=>"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius},9165:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isUnsafeProperty=function(e){return"__proto__"===e}},9213:(e,t,r)=>{e.exports=r(1626).last},9322:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isTypedArray=function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}},9359:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObject=function(e){return null!==e&&("object"==typeof e||"function"==typeof e)}},9361:(e,t,r)=>{"use strict";r.d(t,{x:()=>n});var n=e=>e.options.tooltipPayloadSearcher},9405:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(9649);t.isMatch=function(e,t){return n.isMatchWith(e,t,()=>void 0)}},9486:(e,t,r)=>{"use strict";function n(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=function e(t){var r,n,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(n=e(t[r]))&&(i&&(i+=" "),i+=n)}else for(n in t)t[n]&&(i&&(i+=" "),i+=n);return i}(e))&&(n&&(n+=" "),n+=t);return n}r.d(t,{$:()=>n})},9491:(e,t,r)=>{"use strict";r.d(t,{TK:()=>l});var n=r(2015),i=r(7202),a=r(9684),o=r(7580),l=e=>{var{chartData:t}=e,r=(0,a.j)(),l=(0,o.r)();return(0,n.useEffect)(()=>l?()=>{}:(r((0,i.hq)(t)),()=>{r((0,i.hq)(void 0))}),[t,r,l]),null}},9530:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(1442);t.cloneDeep=function(e){return n.cloneDeepWithImpl(e,void 0,e,new Map,void 0)}},9649:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(9405),i=r(9359),a=r(2284),o=r(7157);function l(e,t,r,n){if(t===e)return!0;switch(typeof t){case"object":return function(e,t,r,n){if(null==t)return!0;if(Array.isArray(t))return c(e,t,r,n);if(t instanceof Map){var i=e,o=t,l=r,s=n;if(0===o.size)return!0;if(!(i instanceof Map))return!1;for(let[e,t]of o.entries())if(!1===l(i.get(e),t,e,i,o,s))return!1;return!0}if(t instanceof Set)return u(e,t,r,n);let f=Object.keys(t);if(null==e)return 0===f.length;if(0===f.length)return!0;if(n&&n.has(t))return n.get(t)===e;n&&n.set(t,e);try{for(let i=0;i<f.length;i++){let o=f[i];if(!a.isPrimitive(e)&&!(o in e)||void 0===t[o]&&void 0!==e[o]||null===t[o]&&null!==e[o]||!r(e[o],t[o],o,e,t,n))return!1}return!0}finally{n&&n.delete(t)}}(e,t,r,n);case"function":if(Object.keys(t).length>0)return l(e,{...t},r,n);return o.eq(e,t);default:if(!i.isObject(e))return o.eq(e,t);if("string"==typeof t)return""===t;return!0}}function c(e,t,r,n){if(0===t.length)return!0;if(!Array.isArray(e))return!1;let i=new Set;for(let a=0;a<t.length;a++){let o=t[a],l=!1;for(let c=0;c<e.length;c++){if(i.has(c))continue;let u=e[c],s=!1;if(r(u,o,a,e,t,n)&&(s=!0),s){i.add(c),l=!0;break}}if(!l)return!1}return!0}function u(e,t,r,n){return 0===t.size||e instanceof Set&&c([...e],[...t],r,n)}t.isMatchWith=function(e,t,r){return"function"!=typeof r?n.isMatch(e,t):l(e,t,function e(t,n,i,a,o,c){let u=r(t,n,i,a,o,c);return void 0!==u?!!u:l(t,n,e,c)},new Map)},t.isSetMatch=u},9684:(e,t,r)=>{"use strict";r.d(t,{G:()=>f,j:()=>l});var n=r(1053),i=r(2015),a=r(3251),o=e=>e,l=()=>{var e=(0,i.useContext)(a.E);return e?e.store.dispatch:o},c=()=>{},u=()=>c,s=(e,t)=>e===t;function f(e){var t=(0,i.useContext)(a.E);return(0,n.useSyncExternalStoreWithSelector)(t?t.subscription.addNestedSub:u,t?t.store.getState:c,t?t.store.getState:c,t?e:c,s)}},9713:(e,t,r)=>{"use strict";r.d(t,{u:()=>O});var n=r(2015),i=r(7063),a=r.n(i),o=r(9486);function l(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}var c=r(7269),u=r(1094),s=r(4351),f=r(4504),d=r(9044),h=r(2661),p=r(1278),y=["viewBox"],v=["viewBox"];function g(){return(g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m(Object(r),!0).forEach(function(t){w(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function x(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function w(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class O extends n.Component{constructor(e){super(e),this.tickRefs=n.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}shouldComponentUpdate(e,t){var{viewBox:r}=e,n=x(e,y),i=this.props,{viewBox:a}=i,o=x(i,v);return!l(r,a)||!l(n,o)||!l(t,this.state)}getTickLineCoord(e){var t,r,n,i,a,o,{x:l,y:c,width:u,height:s,orientation:d,tickSize:h,mirror:p,tickMargin:y}=this.props,v=p?-1:1,g=e.tickSize||h,m=(0,f.Et)(e.tickCoord)?e.tickCoord:e.coordinate;switch(d){case"top":t=r=e.coordinate,o=(n=(i=c+!p*s)-v*g)-v*y,a=m;break;case"left":n=i=e.coordinate,a=(t=(r=l+!p*u)-v*g)-v*y,o=m;break;case"right":n=i=e.coordinate,a=(t=(r=l+p*u)+v*g)+v*y,o=m;break;default:t=r=e.coordinate,o=(n=(i=c+p*s)+v*g)+v*y,a=m}return{line:{x1:t,y1:n,x2:r,y2:i},tick:{x:a,y:o}}}getTickTextAnchor(){var e,{orientation:t,mirror:r}=this.props;switch(t){case"left":e=r?"start":"end";break;case"right":e=r?"end":"start";break;default:e="middle"}return e}getTickVerticalAnchor(){var{orientation:e,mirror:t}=this.props;switch(e){case"left":case"right":return"middle";case"top":return t?"start":"end";default:return t?"end":"start"}}renderAxisLine(){var{x:e,y:t,width:r,height:i,orientation:l,mirror:c,axisLine:u}=this.props,s=b(b(b({},(0,h.J9)(this.props,!1)),(0,h.J9)(u,!1)),{},{fill:"none"});if("top"===l||"bottom"===l){var f=+("top"===l&&!c||"bottom"===l&&c);s=b(b({},s),{},{x1:e,y1:t+f*i,x2:e+r,y2:t+f*i})}else{var d=+("left"===l&&!c||"right"===l&&c);s=b(b({},s),{},{x1:e+d*r,y1:t,x2:e+d*r,y2:t+i})}return n.createElement("line",g({},s,{className:(0,o.$)("recharts-cartesian-axis-line",a()(u,"className"))}))}static renderTickItem(e,t,r){var i,a=(0,o.$)(t.className,"recharts-cartesian-axis-tick-value");if(n.isValidElement(e))i=n.cloneElement(e,b(b({},t),{},{className:a}));else if("function"==typeof e)i=e(b(b({},t),{},{className:a}));else{var l="recharts-cartesian-axis-tick-value";"boolean"!=typeof e&&(l=(0,o.$)(l,e.className)),i=n.createElement(u.E,g({},t,{className:l}),r)}return i}renderTicks(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:i,stroke:l,tick:u,tickFormatter:s,unit:f}=this.props,y=(0,p.f)(b(b({},this.props),{},{ticks:r}),e,t),v=this.getTickTextAnchor(),m=this.getTickVerticalAnchor(),x=(0,h.J9)(this.props,!1),w=(0,h.J9)(u,!1),j=b(b({},x),{},{fill:"none"},(0,h.J9)(i,!1)),S=y.map((e,t)=>{var{line:r,tick:h}=this.getTickLineCoord(e),p=b(b(b(b({textAnchor:v,verticalAnchor:m},x),{},{stroke:"none",fill:l},w),h),{},{index:t,payload:e,visibleTicksCount:y.length,tickFormatter:s});return n.createElement(c.W,g({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},(0,d.XC)(this.props,e,t)),i&&n.createElement("line",g({},j,r,{className:(0,o.$)("recharts-cartesian-axis-tick-line",a()(i,"className"))})),u&&O.renderTickItem(u,p,"".concat("function"==typeof s?s(e.value,t):e.value).concat(f||"")))});return S.length>0?n.createElement("g",{className:"recharts-cartesian-axis-ticks"},S):null}render(){var{axisLine:e,width:t,height:r,className:i,hide:a}=this.props;if(a)return null;var{ticks:l}=this.props;return null!=t&&t<=0||null!=r&&r<=0?null:n.createElement(c.W,{className:(0,o.$)("recharts-cartesian-axis",i),ref:e=>{if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(t);var r=t[0];if(r){var n=window.getComputedStyle(r).fontSize,i=window.getComputedStyle(r).letterSpacing;(n!==this.state.fontSize||i!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},e&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,l),s.J.renderCallByParent(this.props))}}w(O,"displayName","CartesianAxis"),w(O,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},9733:e=>{"use strict";e.exports=import("@chakra-ui/react")},9751:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toArray=function(e){return Array.isArray(e)?e:Array.from(e)}},9778:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,n,a,o){if("function"!=typeof n)throw TypeError("The listener must be a function");var l=new i(n,a||e,o),c=r?r+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],l]:e._events[c].push(l):(e._events[c]=l,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function l(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),l.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},l.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,o=Array(a);i<a;i++)o[i]=n[i].fn;return o},l.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},l.prototype.emit=function(e,t,n,i,a,o){var l=r?r+e:e;if(!this._events[l])return!1;var c,u,s=this._events[l],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(e,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,t),!0;case 3:return s.fn.call(s.context,t,n),!0;case 4:return s.fn.call(s.context,t,n,i),!0;case 5:return s.fn.call(s.context,t,n,i,a),!0;case 6:return s.fn.call(s.context,t,n,i,a,o),!0}for(u=1,c=Array(f-1);u<f;u++)c[u-1]=arguments[u];s.fn.apply(s.context,c)}else{var d,h=s.length;for(u=0;u<h;u++)switch(s[u].once&&this.removeListener(e,s[u].fn,void 0,!0),f){case 1:s[u].fn.call(s[u].context);break;case 2:s[u].fn.call(s[u].context,t);break;case 3:s[u].fn.call(s[u].context,t,n);break;case 4:s[u].fn.call(s[u].context,t,n,i);break;default:if(!c)for(d=1,c=Array(f-1);d<f;d++)c[d-1]=arguments[d];s[u].fn.apply(s[u].context,c)}}return!0},l.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},l.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},l.prototype.removeListener=function(e,t,n,i){var a=r?r+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var l=this._events[a];if(l.fn)l.fn!==t||i&&!l.once||n&&l.context!==n||o(this,a);else{for(var c=0,u=[],s=l.length;c<s;c++)(l[c].fn!==t||i&&!l[c].once||n&&l[c].context!==n)&&u.push(l[c]);u.length?this._events[a]=1===u.length?u[0]:u:o(this,a)}return this},l.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=r,l.EventEmitter=l,e.exports=l},9841:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(9405),i=r(9530);t.matches=function(e){return e=i.cloneDeep(e),t=>n.isMatch(t,e)}},9897:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toKey=function(e){return"string"==typeof e||"symbol"==typeof e?e:Object.is(e?.valueOf?.(),-0)?"-0":String(e)}},9952:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(9165),i=r(5350),a=r(9897),o=r(4817);t.get=function e(t,r,l){if(null==t)return l;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return l;let a=t[r];if(void 0===a)if(i.isDeepKey(r))return e(t,o.toPath(r),l);else return l;return a}case"number":case"symbol":{"number"==typeof r&&(r=a.toKey(r));let e=t[r];if(void 0===e)return l;return e}default:{if(Array.isArray(r)){var c=t,u=r,s=l;if(0===u.length)return s;let e=c;for(let t=0;t<u.length;t++){if(null==e||n.isUnsafeProperty(u[t]))return s;e=e[u[t]]}return void 0===e?s:e}if(r=Object.is(r?.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return l;let e=t[r];if(void 0===e)return l;return e}}}}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8270,4874,752,6281,5333],()=>r(4682));module.exports=n})();