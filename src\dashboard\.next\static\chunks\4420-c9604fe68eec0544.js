"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4420],{5095:(e,t,a)=>{a.d(t,{EO:()=>r,Lt:()=>o});var n=a(94513),l=a(9557),s=a(52922);function o(e){let{leastDestructiveRef:t,...a}=e;return(0,n.jsx)(l.aF,{...a,initialFocusRef:t})}let r=(0,a(2923).R)((e,t)=>(0,n.jsx)(s.$,{ref:t,role:"alertdialog",...e}))},7627:(e,t,a)=>{a.d(t,{T:()=>m});var n=a(94513),l=a(75387),s=a(25195),o=a(22697),r=a(44637),i=a(2923),c=a(56915),d=a(33225);let u=["h","minH","height","minHeight"],m=(0,i.R)((e,t)=>{let a=(0,c.V)("Textarea",e),{className:i,rows:m,...f}=(0,l.M)(e),h=(0,r.t)(f),p=m?(0,s.c)(a,u):a;return(0,n.jsx)(d.B.textarea,{ref:t,rows:m,...h,className:(0,o.cx)("chakra-textarea",i),__css:p})});m.displayName="Textarea"},18303:(e,t,a)=>{a.d(t,{i:()=>s});var n=a(94285),l=a(65507);function s(e){let{value:t,defaultValue:a,onChange:s,shouldUpdate:o=(e,t)=>e!==t}=e,r=(0,l.c)(s),i=(0,l.c)(o),[c,d]=(0,n.useState)(a),u=void 0!==t,m=u?t:c,f=(0,l.c)(e=>{let t="function"==typeof e?e(m):e;i(m,t)&&(u||d(t),r(t))},[u,r,m,i]);return[m,f]}},22237:(e,t,a)=>{a.d(t,{S:()=>j});var n=a(94513),l=a(75387),s=a(50614),o=a(72097),r=a(22697),i=a(610),c=a(94285),d=a(70423),u=a(33225);function m(e){return(0,n.jsx)(u.B.svg,{width:"1.2em",viewBox:"0 0 12 10",style:{fill:"none",strokeWidth:2,stroke:"currentColor",strokeDasharray:16},...e,children:(0,n.jsx)("polyline",{points:"1.5 6 4.5 9 10.5 1"})})}function f(e){return(0,n.jsx)(u.B.svg,{width:"1.2em",viewBox:"0 0 24 24",style:{stroke:"currentColor",strokeWidth:4},...e,children:(0,n.jsx)("line",{x1:"21",x2:"3",y1:"12",y2:"12"})})}function h(e){let{isIndeterminate:t,isChecked:a,...l}=e;return a||t?(0,n.jsx)(u.B.div,{style:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%"},children:(0,n.jsx)(t?f:m,{...l})}):null}var p=a(96027),x=a(2923),b=a(56915);let v={display:"inline-flex",alignItems:"center",justifyContent:"center",verticalAlign:"top",userSelect:"none",flexShrink:0},_={cursor:"pointer",display:"inline-flex",alignItems:"center",verticalAlign:"top",position:"relative"},y=(0,i.i7)({from:{opacity:0,strokeDashoffset:16,transform:"scale(0.95)"},to:{opacity:1,strokeDashoffset:0,transform:"scale(1)"}}),k=(0,i.i7)({from:{opacity:0},to:{opacity:1}}),N=(0,i.i7)({from:{transform:"scaleX(0.65)"},to:{transform:"scaleX(1)"}}),j=(0,x.R)(function(e,t){let a=(0,d.L)(),i={...a,...e},m=(0,b.o)("Checkbox",i),f=(0,l.M)(e),{spacing:x="0.5rem",className:j,children:C,iconColor:g,iconSize:w,icon:A=(0,n.jsx)(h,{}),isChecked:I,isDisabled:B=a?.isDisabled,onChange:E,inputProps:S,...R}=f,D=I;a?.value&&f.value&&(D=a.value.includes(f.value));let M=E;a?.onChange&&f.value&&(M=(0,s.O)(a.onChange,E));let{state:T,getInputProps:F,getCheckboxProps:H,getLabelProps:$,getRootProps:O}=(0,p.v)({...R,isDisabled:B,isChecked:D,onChange:M}),V=function(e){let[t,a]=(0,c.useState)(e),[n,l]=(0,c.useState)(!1);return e!==t&&(l(!0),a(e)),n}(T.isChecked),q=(0,c.useMemo)(()=>({animation:V?T.isIndeterminate?`${k} 20ms linear, ${N} 200ms linear`:`${y} 200ms linear`:void 0,...m.icon,...(0,o.o)({fontSize:w,color:g})}),[g,w,V,T.isIndeterminate,m.icon]),z=(0,c.cloneElement)(A,{__css:q,isIndeterminate:T.isIndeterminate,isChecked:T.isChecked});return(0,n.jsxs)(u.B.label,{__css:{..._,...m.container},className:(0,r.cx)("chakra-checkbox",j),...O(),children:[(0,n.jsx)("input",{className:"chakra-checkbox__input",...F(S,t)}),(0,n.jsx)(u.B.span,{__css:{...v,...m.control},className:"chakra-checkbox__control",...H(),children:z}),C&&(0,n.jsx)(u.B.span,{className:"chakra-checkbox__label",...$(),__css:{marginStart:x,...m.label},children:C})]})});j.displayName="Checkbox"},22680:(e,t,a)=>{a.d(t,{J:()=>i});var n=a(94513),l=a(22697),s=a(62999),o=a(2923),r=a(33225);let i=(0,o.R)(function(e,t){let{getButtonProps:a}=(0,s.AV)(),o=a(e,t),i={display:"flex",alignItems:"center",width:"100%",outline:0,...(0,s.EF)().button};return(0,n.jsx)(r.B.button,{...o,className:(0,l.cx)("chakra-accordion__button",e.className),__css:i})});i.displayName="AccordionButton"},25964:(e,t,a)=>{a.d(t,{l:()=>p});var n=a(94513),l=a(75387),s=a(16229),o=a(54338),r=a(81405),i=a(94285),c=a(22697),d=a(2923),u=a(33225);let m=(0,d.R)(function(e,t){let{children:a,placeholder:l,className:s,...o}=e;return(0,n.jsxs)(u.B.select,{...o,ref:t,className:(0,c.cx)("chakra-select",s),children:[l&&(0,n.jsx)("option",{value:"",children:l}),a]})});m.displayName="SelectField";var f=a(44637),h=a(56915);let p=(0,d.R)((e,t)=>{let a=(0,h.o)("Select",e),{rootProps:i,placeholder:c,icon:d,color:p,height:x,h:b,minH:_,minHeight:y,iconColor:k,iconSize:N,...j}=(0,l.M)(e),[C,g]=(0,o.l)(j,s.GF),w=(0,f.t)(g),A={paddingEnd:"2rem",...a.field,_focus:{zIndex:"unset",...a.field?._focus}};return(0,n.jsxs)(u.B.div,{className:"chakra-select__wrapper",__css:{width:"100%",height:"fit-content",position:"relative",color:p},...C,...i,children:[(0,n.jsx)(m,{ref:t,height:b??x,minH:_??y,placeholder:c,...w,__css:A,children:e.children}),(0,n.jsx)(v,{"data-disabled":(0,r.s)(w.disabled),...(k||p)&&{color:k||p},__css:a.icon,...N&&{fontSize:N},children:d})]})});p.displayName="Select";let x=e=>(0,n.jsx)("svg",{viewBox:"0 0 24 24",...e,children:(0,n.jsx)("path",{fill:"currentColor",d:"M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"})}),b=(0,u.B)("div",{baseStyle:{position:"absolute",display:"inline-flex",alignItems:"center",justifyContent:"center",pointerEvents:"none",top:"50%",transform:"translateY(-50%)"}}),v=e=>{let{children:t=(0,n.jsx)(x,{}),...a}=e,l=(0,i.cloneElement)(t,{role:"presentation",className:"chakra-select__icon",focusable:!1,"aria-hidden":!0,style:{width:"1em",height:"1em",color:"currentColor"}});return(0,n.jsx)(b,{...a,className:"chakra-select__icon-wrapper",children:(0,i.isValidElement)(t)?l:null})};v.displayName="SelectIcon"},28245:(e,t,a)=>{a.d(t,{j:()=>c});var n=a(94513),l=a(55100),s=a(22697),o=a(9557),r=a(2923),i=a(33225);let c=(0,r.R)((e,t)=>{let{className:a,...r}=e,c=(0,s.cx)("chakra-modal__footer",a),d=(0,o.x5)(),u=(0,l.H2)({display:"flex",alignItems:"center",justifyContent:"flex-end",...d.footer});return(0,n.jsx)(i.B.footer,{ref:t,...r,__css:u,className:c})});c.displayName="ModalFooter"},31637:(e,t,a)=>{a.d(t,{$c:()=>_,Jn:()=>C,O_:()=>b,Vh:()=>y,at:()=>m,uc:()=>x,uo:()=>j});var n=a(18303),l=a(78961),s=a(29035),o=a(50614),r=a(47133),i=a(18654),c=a(94285),d=a(87888),u=a(70011);let[m,f,h,p]=(0,d.D)();function x(e){let{defaultIndex:t,onChange:a,index:l,isManual:s,isLazy:o,lazyBehavior:r="unmount",orientation:i="horizontal",direction:d="ltr",...u}=e,[m,f]=(0,c.useState)(t??0),[p,x]=(0,n.i)({defaultValue:t??0,value:l,onChange:a});(0,c.useEffect)(()=>{null!=l&&f(l)},[l]);let b=h(),v=(0,c.useId)(),_=e.id??v;return{id:`tabs-${_}`,selectedIndex:p,focusedIndex:m,setSelectedIndex:x,setFocusedIndex:f,isManual:s,isLazy:o,lazyBehavior:r,orientation:i,descendants:b,direction:d,htmlProps:u}}let[b,v]=(0,s.q)({name:"TabsContext",errorMessage:"useTabsContext: `context` is undefined. Seems you forgot to wrap all tabs components within <Tabs />"});function _(e){let{focusedIndex:t,orientation:a,direction:n}=v(),l=f(),s=(0,c.useCallback)(e=>{let s=()=>{let e=l.nextEnabled(t);e&&e.node?.focus()},o=()=>{let e=l.prevEnabled(t);e&&e.node?.focus()},r="horizontal"===a,i="vertical"===a,c=e.key,d={["ltr"===n?"ArrowLeft":"ArrowRight"]:()=>r&&o(),["ltr"===n?"ArrowRight":"ArrowLeft"]:()=>r&&s(),ArrowDown:()=>i&&s(),ArrowUp:()=>i&&o(),Home:()=>{let e=l.firstEnabled();e&&e.node?.focus()},End:()=>{let e=l.lastEnabled();e&&e.node?.focus()}}[c];d&&(e.preventDefault(),d(e))},[l,t,a,n]);return{...e,role:"tablist","aria-orientation":a,onKeyDown:(0,o.H)(e.onKeyDown,s)}}function y(e){let{isDisabled:t=!1,isFocusable:a=!1,...n}=e,{setSelectedIndex:s,isManual:r,id:i,setFocusedIndex:c,selectedIndex:d}=v(),{index:m,register:f}=p({disabled:t&&!a}),h=m===d;return{...(0,u.I)({...n,ref:(0,l.Px)(f,e.ref),isDisabled:t,isFocusable:a,onClick:(0,o.H)(e.onClick,()=>{s(m)})}),id:g(i,m),role:"tab",tabIndex:h?0:-1,type:"button","aria-selected":h,"aria-controls":w(i,m),onFocus:t?void 0:(0,o.H)(e.onFocus,()=>{c(m);let e=t&&a;r||e||s(m)})}}let[k,N]=(0,s.q)({});function j(e){let{id:t,selectedIndex:a}=v(),n=(0,r.a)(e.children).map((e,n)=>(0,c.createElement)(k,{key:e.key??n,value:{isSelected:n===a,id:w(t,n),tabId:g(t,n),selectedIndex:a}},e));return{...e,children:n}}function C(e){let{children:t,...a}=e,{isLazy:n,lazyBehavior:l}=v(),{isSelected:s,id:o,tabId:r}=N(),d=(0,c.useRef)(!1);s&&(d.current=!0);let u=(0,i.q)({wasSelected:d.current,isSelected:s,enabled:n,mode:l});return{tabIndex:0,...a,children:u?t:null,role:"tabpanel","aria-labelledby":r,hidden:!s,id:o}}function g(e,t){return`${e}--tab-${t}`}function w(e,t){return`${e}--tabpanel-${t}`}},47402:(e,t,a)=>{a.d(t,{o:()=>d});var n=a(94513),l=a(55100),s=a(22697),o=a(91047),r=a(31637),i=a(2923),c=a(33225);let d=(0,i.R)(function(e,t){let a=(0,o.e)(),i=(0,r.Vh)({...e,ref:t}),d=(0,l.H2)({outline:"0",display:"flex",alignItems:"center",justifyContent:"center",...a.tab});return(0,n.jsx)(c.B.button,{...i,className:(0,s.cx)("chakra-tabs__tab",e.className),__css:d})});d.displayName="Tab"},54338:(e,t,a)=>{a.d(t,{l:()=>n});function n(e,t){let a={},n={};for(let[l,s]of Object.entries(e))t.includes(l)?a[l]=s:n[l]=s;return[a,n]}},59365:(e,t,a)=>{a.d(t,{s:()=>i});var n=a(94513),l=a(22697),s=a(50614),o=a(9557),r=a(33021);let i=(0,a(2923).R)((e,t)=>{let{onClick:a,className:i,...c}=e,{onClose:d}=(0,o.k3)(),u=(0,l.cx)("chakra-modal__close-btn",i),m=(0,o.x5)();return(0,n.jsx)(r.J,{ref:t,__css:m.closeButton,className:u,onClick:(0,s.H)(a,e=>{e.stopPropagation(),d()}),...c})});i.displayName="ModalCloseButton"},62999:(e,t,a)=>{a.d(t,{AV:()=>i,C3:()=>c,EF:()=>o,Of:()=>u,TG:()=>r,gm:()=>s,v3:()=>m});var n=a(29035),l=a(87888);let[s,o]=(0,n.q)({name:"AccordionStylesContext",hookName:"useAccordionStyles",providerName:"<Accordion />"}),[r,i]=(0,n.q)({name:"AccordionItemContext",hookName:"useAccordionItemContext",providerName:"<AccordionItem />"}),[c,d,u,m]=(0,l.D)()},70423:(e,t,a)=>{a.d(t,{L:()=>l,a:()=>n});let[n,l]=(0,a(29035).q)({name:"CheckboxGroupContext",strict:!1})},72671:(e,t,a)=>{a.d(t,{K:()=>c});var n=a(94513),l=a(22697),s=a(91047),o=a(31637),r=a(2923),i=a(33225);let c=(0,r.R)(function(e,t){let a=(0,o.Jn)({...e,ref:t}),r=(0,s.e)();return(0,n.jsx)(i.B.div,{outline:"0",...a,className:(0,l.cx)("chakra-tabs__tab-panel",e.className),__css:r.tabpanel})});c.displayName="TabPanel"},75697:(e,t,a)=>{a.d(t,{n:()=>m});var n=a(94513),l=a(75387),s=a(22697),o=a(94285),r=a(62999),i=a(92635),c=a(2923),d=a(56915),u=a(33225);let m=(0,c.R)(function({children:e,reduceMotion:t,...a},c){let m=(0,d.o)("Accordion",a),f=(0,l.M)(a),{htmlProps:h,descendants:p,...x}=(0,i.O3)(f),b=(0,o.useMemo)(()=>({...x,reduceMotion:!!t}),[x,t]);return(0,n.jsx)(r.C3,{value:p,children:(0,n.jsx)(i.If,{value:b,children:(0,n.jsx)(r.gm,{value:m,children:(0,n.jsx)(u.B.div,{ref:c,...h,className:(0,s.cx)("chakra-accordion",a.className),__css:m.root,children:e})})})})});m.displayName="Accordion"},76857:(e,t,a)=>{a.d(t,{o:()=>o});var n=a(94513),l=a(33225),s=a(2923);let o=(0,l.B)("div",{baseStyle:{display:"flex",alignItems:"center",justifyContent:"center"}});o.displayName="Center";let r={horizontal:{insetStart:"50%",transform:"translateX(-50%)"},vertical:{top:"50%",transform:"translateY(-50%)"},both:{insetStart:"50%",top:"50%",transform:"translate(-50%, -50%)"}};(0,s.R)(function(e,t){let{axis:a="both",...s}=e;return(0,n.jsx)(l.B.div,{ref:t,__css:r[a],...s,position:"absolute"})})},79961:(e,t,a)=>{a.d(t,{C:()=>c});var n=a(94513),l=a(75387),s=a(22697),o=a(2923),r=a(56915),i=a(33225);let c=(0,o.R)(function(e,t){let a=(0,r.V)("Code",e),{className:o,...c}=(0,l.M)(e);return(0,n.jsx)(i.B.code,{ref:t,className:(0,s.cx)("chakra-code",e.className),...c,__css:{display:"inline-block",...a}})});c.displayName="Code"},83881:(e,t,a)=>{a.d(t,{w:()=>d});var n=a(94513),l=a(55100),s=a(22697),o=a(91047),r=a(31637),i=a(2923),c=a(33225);let d=(0,i.R)(function(e,t){let a=(0,r.$c)({...e,ref:t}),i=(0,o.e)(),d=(0,l.H2)({display:"flex",...i.tablist});return(0,n.jsx)(c.B.div,{...a,className:(0,s.cx)("chakra-tabs__tablist",e.className),__css:d})});d.displayName="TabList"},84443:(e,t,a)=>{a.d(t,{Q:()=>i});var n=a(94513),l=a(22697),s=a(62999),o=a(92635),r=a(49217);function i(e){let{isOpen:t,isDisabled:a}=(0,s.AV)(),{reduceMotion:i}=(0,o.Dr)(),c=(0,l.cx)("chakra-accordion__icon",e.className),d={opacity:a?.4:1,transform:t?"rotate(-180deg)":void 0,transition:i?void 0:"transform 0.2s",transformOrigin:"center",...(0,s.EF)().icon};return(0,n.jsx)(r.I,{viewBox:"0 0 24 24","aria-hidden":!0,className:c,__css:d,...e,children:(0,n.jsx)("path",{fill:"currentColor",d:"M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"})})}i.displayName="AccordionIcon"},86293:(e,t,a)=>{a.d(t,{A:()=>u});var n=a(94513),l=a(55100),s=a(22697),o=a(94285),r=a(62999),i=a(92635),c=a(2923),d=a(33225);let u=(0,c.R)(function(e,t){let{children:a,className:c}=e,{htmlProps:u,...m}=(0,i.r9)(e),f=(0,r.EF)(),h=(0,l.H2)({...f.container,overflowAnchor:"none"}),p=(0,o.useMemo)(()=>m,[m]);return(0,n.jsx)(r.TG,{value:p,children:(0,n.jsx)(d.B.div,{ref:t,...u,className:(0,s.cx)("chakra-accordion__item",c),__css:h,children:"function"==typeof a?a({isExpanded:!!m.isOpen,isDisabled:!!m.isDisabled}):a})})});u.displayName="AccordionItem"},90020:(e,t,a)=>{a.d(t,{v:()=>d});var n=a(94513),l=a(22697),s=a(62999),o=a(92635),r=a(83901),i=a(2923),c=a(33225);let d=(0,i.R)(function(e,t){let{className:a,motionProps:i,...d}=e,{reduceMotion:u}=(0,o.Dr)(),{getPanelProps:m,isOpen:f}=(0,s.AV)(),h=m(d,t),p=(0,l.cx)("chakra-accordion__panel",a),x=(0,s.EF)();u||delete h.hidden;let b=(0,n.jsx)(c.B.div,{...h,__css:x.panel,className:p});return u?b:(0,n.jsx)(r.S,{in:f,...i,children:b})});d.displayName="AccordionPanel"},91047:(e,t,a)=>{a.d(t,{e:()=>f,t:()=>h});var n=a(94513),l=a(75387),s=a(29035),o=a(22697),r=a(94285),i=a(31637),c=a(2923),d=a(56915),u=a(33225);let[m,f]=(0,s.q)({name:"TabsStylesContext",errorMessage:"useTabsStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Tabs />\" "}),h=(0,c.R)(function(e,t){let a=(0,d.o)("Tabs",e),{children:s,className:c,...f}=(0,l.M)(e),{htmlProps:h,descendants:p,...x}=(0,i.uc)(f),b=(0,r.useMemo)(()=>x,[x]),{isFitted:v,..._}=h,y={position:"relative",...a.root};return(0,n.jsx)(i.at,{value:p,children:(0,n.jsx)(i.O_,{value:b,children:(0,n.jsx)(m,{value:a,children:(0,n.jsx)(u.B.div,{className:(0,o.cx)("chakra-tabs",c),ref:t,..._,__css:y,children:s})})})})});h.displayName="Tabs"},92635:(e,t,a)=>{a.d(t,{Dr:()=>m,If:()=>u,O3:()=>d,r9:()=>f});var n=a(18303),l=a(78961),s=a(29035),o=a(50614),r=a(61060),i=a(94285),c=a(62999);function d(e){var t;let{onChange:a,defaultIndex:l,index:s,allowMultiple:o,allowToggle:d,...u}=e;(function(e){let t=e.index||e.defaultIndex,a=null!=t&&!Array.isArray(t)&&e.allowMultiple;(0,r.R)({condition:!!a,message:`If 'allowMultiple' is passed, then 'index' or 'defaultIndex' must be an array. You passed: ${typeof t},`})})(e),t=e,(0,r.R)({condition:!!(t.allowMultiple&&t.allowToggle),message:"If 'allowMultiple' is passed, 'allowToggle' will be ignored. Either remove 'allowToggle' or 'allowMultiple' depending on whether you want multiple accordions visible or not"});let m=(0,c.Of)(),[f,h]=(0,i.useState)(-1);(0,i.useEffect)(()=>()=>{h(-1)},[]);let[p,x]=(0,n.i)({value:s,defaultValue:()=>o?l??[]:l??-1,onChange:a});return{index:p,setIndex:x,htmlProps:u,getAccordionItemProps:e=>{let t=!1;return null!==e&&(t=Array.isArray(p)?p.includes(e):p===e),{isOpen:t,onChange:t=>{null!==e&&(o&&Array.isArray(p)?x(t?p.concat(e):p.filter(t=>t!==e)):t?x(e):d&&x(-1))}}},focusedIndex:f,setFocusedIndex:h,descendants:m}}let[u,m]=(0,s.q)({name:"AccordionContext",hookName:"useAccordionContext",providerName:"Accordion"});function f(e){var t,a;let{isDisabled:n,isFocusable:s,id:d,...u}=e,{getAccordionItemProps:f,setFocusedIndex:h}=m(),p=(0,i.useRef)(null),x=(0,i.useId)(),b=d??x,v=`accordion-button-${b}`,_=`accordion-panel-${b}`;t=e,(0,r.R)({condition:!!(t.isFocusable&&!t.isDisabled),message:`Using only 'isFocusable', this prop is reserved for situations where you pass 'isDisabled' but you still want the element to receive focus (A11y). Either remove it or pass 'isDisabled' as well.
    `});let{register:y,index:k,descendants:N}=(0,c.v3)({disabled:n&&!s}),{isOpen:j,onChange:C}=f(-1===k?null:k);a={isOpen:j,isDisabled:n},(0,r.R)({condition:a.isOpen&&!!a.isDisabled,message:"Cannot open a disabled accordion item"});let g=(0,i.useCallback)(()=>{C?.(!j),h(k)},[k,h,j,C]),w=(0,i.useCallback)(e=>{let t={ArrowDown:()=>{let e=N.nextEnabled(k);e?.node.focus()},ArrowUp:()=>{let e=N.prevEnabled(k);e?.node.focus()},Home:()=>{let e=N.firstEnabled();e?.node.focus()},End:()=>{let e=N.lastEnabled();e?.node.focus()}}[e.key];t&&(e.preventDefault(),t(e))},[N,k]),A=(0,i.useCallback)(()=>{h(k)},[h,k]),I=(0,i.useCallback)(function(e={},t=null){return{...e,type:"button",ref:(0,l.Px)(y,p,t),id:v,disabled:!!n,"aria-expanded":!!j,"aria-controls":_,onClick:(0,o.H)(e.onClick,g),onFocus:(0,o.H)(e.onFocus,A),onKeyDown:(0,o.H)(e.onKeyDown,w)}},[v,n,j,g,A,w,_,y]),B=(0,i.useCallback)(function(e={},t=null){return{...e,ref:t,role:"region",id:_,"aria-labelledby":v,hidden:!j}},[v,j,_]);return{isOpen:j,isDisabled:n,isFocusable:s,onOpen:()=>{C?.(!0)},onClose:()=>{C?.(!1)},getButtonProps:I,getPanelProps:B,htmlProps:u}}},99820:(e,t,a)=>{a.d(t,{T:()=>c});var n=a(94513),l=a(22697),s=a(91047),o=a(31637),r=a(2923),i=a(33225);let c=(0,r.R)(function(e,t){let a=(0,o.uo)(e),r=(0,s.e)();return(0,n.jsx)(i.B.div,{...a,width:"100%",ref:t,className:(0,l.cx)("chakra-tabs__tab-panels",e.className),__css:r.tabpanels})});c.displayName="TabPanels"}}]);