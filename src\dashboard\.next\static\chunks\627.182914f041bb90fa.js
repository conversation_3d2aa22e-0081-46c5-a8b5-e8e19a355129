"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[627],{60627:(e,s,o)=>{o.r(s),o.d(s,{default:()=>ee});var n=o(94513),l=o(22907),i=o(9557),a=o(7680),r=o(52922),t=o(47847),c=o(78902),d=o(49217),h=o(41611),m=o(59365),u=o(85104),g=o(79156),b=o(57561),j=o(91047),x=o(83881),p=o(47402),y=o(99820),w=o(72671),f=o(68443),v=o(20429),D=o(31678),C=o(55631),k=o(59818),T=o(40443),z=o(63730),S=o(25964),E=o(71601),F=o(62690),N=o(51961),A=o(24792),M=o(73011),W=o(64057),G=o(7627),I=o(58382),R=o(25680),J=o(22237),_=o(75697),U=o(86293),B=o(22680),P=o(84443),L=o(90020),H=o(61481),O=o(26977),K=o(49451),Z=o(52216),Q=o(55920),Y=o(28245),$=o(94285),q=o(97146);let X={welcome:{enabled:!1,channelId:"",messages:[{title:"\uD83C\uDFAE Welcome to {guild}, {userName}!",description:"Pull up a chair and make yourself comfortable. We've got games, chaos, and questionable life choices waiting for you!\n\n\uD83D\uDCC5 Account Created: {UserCreation}\n\uD83C\uDFAF Join Date: {joinDate}",color:"#FF6B35",footer:"You're our {memberCount} member - let's get weird!"},{title:"\uD83D\uDEA8 Error 404: Chill Not Found! \uD83D\uDEA8",description:"Hey {user}, looks like you've stumbled into our chaotic corner of the internet. Hope you brought snacks and a sense of humor!\n\n\uD83C\uDFAE Member #{memberCountNumeric}\n\uD83D\uDCC5 Joined: {joinTime}",color:"#8B0000",footer:"Welcome to the madness!"},{title:"\uD83C\uDFAF Player {userName} has entered the game!",description:"Welcome to {server} where the games are wild and the conversations are... well, let's just say we're not your typical Discord server.\n\n\uD83D\uDCC5 Account Age: {UserCreation}\n\uD83C\uDFAF Member Since: {user-joinedAt}",color:"#1E90FF",footer:"Time to level up your social life!"},{title:"\uD83D\uDD25 New challenger has appeared!",description:"It's {user}! Welcome to our adult gaming sanctuary where we take games seriously but ourselves... not so much.\n\n\uD83C\uDFAE Member #{memberCountNumeric}\n⏰ Joined: {joinTime}",color:"#DC143C",footer:"Ready to game and chill?"},{title:"\uD83C\uDFB2 Welcome to the chaos, {userName}!",description:"You've joined {server} - where good decisions come to die and epic gaming moments are born. Buckle up!\n\n\uD83D\uDCC5 Account Created: {user-createdAt}\n\uD83C\uDFAF Join Time: {joinTime}",color:"#9932CC",footer:"Let the games begin!"},{title:"⚡ Level Up! New member unlocked!",description:"{user} just joined the {guild} crew! We promise we're friendlier than our name suggests... mostly.\n\n\uD83C\uDFAE You're member #{memberCountNumeric}\n⏰ Account Age: {UserCreation}",color:"#FFD700",footer:"Achievement unlocked: Found the cool kids table"},{title:"\uD83C\uDFAA Welcome to the circus, {userName}!",description:"Hope you're ready for some adult gaming fun and conversations that would make your mother question your life choices.\n\n\uD83D\uDCC5 Joined: {joinDate}\n\uD83C\uDFAF Member #{memberCountNumeric}",color:"#FF1493",footer:"We're all mad here, but in a good way!"},{title:"\uD83C\uDFA8 Another wild gamer appears!",description:"Hey {user}! Welcome to {server} where we game hard, laugh harder, and occasionally make sense.\n\n\uD83D\uDCC5 Account Created: {UserCreation}\n⏰ Join Time: {joinTime}",color:"#20B2AA",footer:"Time to make some questionable decisions together!"},{title:"\uD83D\uDE80 Mission accepted: Welcome {userName}!",description:"You've successfully infiltrated {guild}. Your mission: have fun, play games, and embrace the chaos.\n\n\uD83C\uDFAE Member #{memberCountNumeric}\n\uD83D\uDCC5 Join Date: {joinDate}",color:"#4169E1",footer:"Good luck, you'll need it!"},{title:"\uD83C\uDFAF Critical hit! New member joined!",description:"Welcome {user} to our den of gaming degeneracy and adult conversation. Check your sanity at the door!\n\n\uD83D\uDCC5 Account Age: {UserCreation}\n⏰ Member Since: {user-joinedAt}",color:"#32CD32",footer:"RIP your free time"}],autoRole:{enabled:!1,roleIds:[],delay:1e3,retry:{enabled:!0,maxAttempts:3,delayBetweenAttempts:5e3}},nekosGif:{enabled:!0,type:"wave"}},goodbye:{enabled:!1,channelId:"",messages:[{title:"\uD83D\uDC80 Game Over for {userName}",description:"Looks like {userName} has rage quit from {server}.\nThanks for the memories and questionable life choices!\n\n{kickStatus}\n⏰ Member Since: {user-joinedAt}",color:"#8B0000",footer:"Press F to pay respects"},{title:"\uD83D\uDEAA {userName} has left the building",description:"Another one bites the dust! {userName} decided our chaos wasn't for them.\nCan't win 'em all, I guess.\n\n{kickStatus}\n\uD83D\uDD52 Left: {leaveTime}",color:"#696969",footer:"The door's always open... maybe"},{title:"\uD83D\uDCE4 Connection lost: {userName}",description:"{userName} has disconnected from {guild}.\nHope they found what they were looking for!\n\n{kickStatus}\n⏰ Member Since: {user-joinedAt}",color:"#FF6347",footer:"Thanks for gaming with us!"},{title:"\uD83C\uDFAD Plot twist: {userName} vanished!",description:"In a shocking turn of events, {userName} has left {server}.\nThe show must go on!\n\n{kickStatus}\n\uD83D\uDD52 Left: {leaveTime}",color:"#4B0082",footer:"Until we meet again in another lobby"},{title:"\uD83C\uDFC3‍♂️ {userName} speed-ran their exit",description:"Well, that was quick! {userName} decided to bounce from {server}.\nNo hard feelings... probably.\n\n{kickStatus}\n⏰ Member Since: {user-joinedAt}",color:"#FF8C00",footer:"See ya, wouldn't wanna be ya!"},{title:"\uD83C\uDFAA The circus lost a performer",description:"{userName} has left the madness that is {server}.\nHope they find their chill somewhere else!\n\n{kickStatus}\n\uD83D\uDD52 Left: {leaveTime}",color:"#20B2AA",footer:"Thanks for adding to the chaos!"},{title:"\uD83D\uDCA5 {userName} signed off",description:"Another gamer has left {guild}.\nMay your framerate be high and your ping be low!\n\n{kickStatus}\n⏰ Member Since: {user-joinedAt}",color:"#FF69B4",footer:"GG, no re"},{title:"\uD83C\uDFAE Player {userName} has disconnected",description:"Looks like {userName} found the exit door in {server}.\nHope our brand of chaos was entertaining!\n\n{kickStatus}\n\uD83D\uDD52 Left: {leaveTime}",color:"#32CD32",footer:"Thanks for playing with us!"},{title:"\uD83D\uDE80 {userName} has left orbit",description:"Mission complete! {userName} has successfully escaped {server}.\nSafe travels, space cadet!\n\n{kickStatus}\n⏰ Member Since: {user-joinedAt}",color:"#1E90FF",footer:"Houston, we have a departure"},{title:"\uD83C\uDFAF Target eliminated: {userName}",description:"{userName} has been removed from the game... wait, they left voluntarily.\nWell, that's less dramatic than expected.\n\n{kickStatus}\n\uD83D\uDD52 Left: {leaveTime}",color:"#B22222",footer:"Better luck next server!"}],nekosGif:{enabled:!0,type:"cry"}}},V={user:[{name:"{user}",description:"Mentions the user, e.g., @Username"},{name:"{userName}",description:"The user's name, e.g., Username"},{name:"{userTag}",description:"The user's tag, e.g., Username#1234"},{name:"{userId}",description:"The user's ID, e.g., 123456789012345678"},{name:"{userBanner}",description:"URL of the user's banner (if they have one)"},{name:"{user-createdAt}",description:"The date the user's account was created"},{name:"{UserCreation}",description:'How long ago the user\'s account was created (e.g., "2 years ago")'}],guild:[{name:"{server} / {guild}",description:"The server's name"},{name:"{guildIcon}",description:"URL of the server's icon"},{name:"{memberCount}",description:'Total members with ordinal suffix, e.g., "100th"'},{name:"{memberCountNumeric}",description:'Total members as a number, e.g., "100"'}],time:[{name:"{longTime}",description:'Current date and time, e.g., "June 1, 2024 12:00 PM"'},{name:"{shortTime}",description:'Current time, e.g., "12:00 PM"'}],welcome:[{name:"{joinDate}",description:"The date the user joined"},{name:"{joinTime}",description:"The time the user joined"},{name:"{user-joinedAt}",description:"Full date and time the user joined"}],goodbye:[{name:"{leaveDate}",description:"The date the user left"},{name:"{leaveTime}",description:"The time the user left"},{name:"{kickStatus}",description:"If the user was kicked/banned, shows the status. Otherwise, hidden."}]};function ee(e){var s,o,ee,es,eo,en,el,ei,ea,er,et,ec,ed,eh;let{isOpen:em,onClose:eu,channels:eg=[],roles:eb=[]}=e,ej=(0,l.d)(),[ex,ep]=(0,$.useState)(X),[ey,ew]=(0,$.useState)(!0),[ef,ev]=(0,$.useState)(!1),[eD,eC]=(0,$.useState)(0),[ek,eT]=(0,$.useState)(0),[ez,eS]=(0,$.useState)(!1),eE=eg.filter(e=>0===e.type||5===e.type||"GUILD_TEXT"===e.type||"GUILD_ANNOUNCEMENT"===e.type),eF=eb.filter(e=>"@everyone"!==e.name),eN=async()=>{ew(!0);try{let e=await fetch("/api/automation/welcome");if(!e.ok)throw Error("Failed to fetch settings");let s=await e.json();ep(s)}catch(e){ej({title:"Error loading settings",description:e.message,status:"error",duration:5e3})}finally{ew(!1)}},eA=async()=>{ev(!0);try{if(!(await fetch("/api/automation/welcome",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(ex)})).ok)throw Error("Failed to save settings");ej({title:"Settings Saved",status:"success",duration:3e3}),eu()}catch(e){ej({title:"Error saving settings",description:e.message,status:"error",duration:5e3})}finally{ev(!1)}};(0,$.useEffect)(()=>{em&&eN()},[em]);let eM=(e,s)=>{ep(o=>({...o,welcome:{...o.welcome,[e]:s}}))},eW=(e,s)=>{ep(o=>({...o,goodbye:{...o.goodbye,[e]:s}}))},eG=(e,s)=>{ep(o=>({...o,welcome:{...o.welcome,nekosGif:{...o.welcome.nekosGif,[e]:s}}}))},eI=(e,s)=>{ep(o=>({...o,goodbye:{...o.goodbye,nekosGif:{...o.goodbye.nekosGif,[e]:s}}}))},eR=(e,s)=>{ep(o=>({...o,welcome:{...o.welcome,autoRole:{...o.welcome.autoRole,[e]:s}}}))},eJ=(e,s)=>{ep(o=>({...o,welcome:{...o.welcome,autoRole:{...o.welcome.autoRole,retry:{...o.welcome.autoRole.retry,[e]:s}}}}))},e_=e=>{if(ex.welcome.messages.length<=1)return void ej({title:"Cannot delete",description:"You must have at least one welcome message.",status:"warning",duration:3e3});ep(s=>({...s,welcome:{...s.welcome,messages:s.welcome.messages.filter((s,o)=>o!==e)}})),eD>=ex.welcome.messages.length-1&&eC(Math.max(0,eD-1))},eU=e=>{if(ex.goodbye.messages.length<=1)return void ej({title:"Cannot delete",description:"You must have at least one goodbye message.",status:"warning",duration:3e3});ep(s=>({...s,goodbye:{...s.goodbye,messages:s.goodbye.messages.filter((s,o)=>o!==e)}})),ek>=ex.goodbye.messages.length-1&&eT(Math.max(0,ek-1))},eB=(e,s,o)=>{ep(n=>({...n,welcome:{...n.welcome,messages:n.welcome.messages.map((n,l)=>l===e?{...n,[s]:o}:n)}}))},eP=(e,s,o)=>{ep(n=>({...n,goodbye:{...n.goodbye,messages:n.goodbye.messages.map((n,l)=>l===e?{...n,[s]:o}:n)}}))},eL=e=>{let s={...ex.welcome.messages[e]};s.title="".concat(s.title," (Copy)"),ep(e=>({...e,welcome:{...e.welcome,messages:[...e.welcome.messages,s]}})),eC(ex.welcome.messages.length)},eH=e=>{let s={...ex.goodbye.messages[e]};s.title="".concat(s.title," (Copy)"),ep(e=>({...e,goodbye:{...e.goodbye,messages:[...e.goodbye.messages,s]}})),eT(ex.goodbye.messages.length)};return(0,n.jsxs)(i.aF,{isOpen:em,onClose:eu,size:"6xl",scrollBehavior:"inside",children:[(0,n.jsx)(a.m,{}),(0,n.jsxs)(r.$,{maxH:"90vh",children:[(0,n.jsx)(t.r,{children:(0,n.jsxs)(c.z,{children:[(0,n.jsx)(d.I,{as:q.mEP}),(0,n.jsx)(h.E,{children:"Advanced Welcome & Goodbye System"})]})}),(0,n.jsx)(m.s,{}),(0,n.jsx)(u.c,{pb:6,children:ey?(0,n.jsxs)(g.T,{justify:"center",h:"400px",children:[(0,n.jsx)(b.y,{size:"xl"}),(0,n.jsx)(h.E,{children:"Loading Settings..."})]}):(0,n.jsxs)(j.t,{isFitted:!0,variant:"enclosed",colorScheme:"blue",children:[(0,n.jsxs)(x.w,{children:[(0,n.jsxs)(p.o,{children:[(0,n.jsx)(d.I,{as:q.mEP,mr:2})," Welcome Messages"]}),(0,n.jsxs)(p.o,{children:[(0,n.jsx)(d.I,{as:q.QeK,mr:2})," Goodbye Messages"]}),(0,n.jsxs)(p.o,{children:[(0,n.jsx)(d.I,{as:q.VSk,mr:2})," Placeholders"]})]}),(0,n.jsxs)(y.T,{children:[(0,n.jsx)(w.K,{children:(0,n.jsxs)(g.T,{spacing:6,align:"stretch",children:[(0,n.jsxs)(f.Z,{children:[(0,n.jsx)(v.a,{children:(0,n.jsxs)(c.z,{justify:"space-between",children:[(0,n.jsx)(D.D,{size:"md",children:"Welcome System"}),(0,n.jsx)(C.d,{size:"lg",isChecked:ex.welcome.enabled,onChange:e=>eM("enabled",e.target.checked)})]})}),(0,n.jsx)(k.b,{children:(0,n.jsx)(g.T,{spacing:4,align:"stretch",children:(0,n.jsxs)(T.MJ,{isDisabled:!ex.welcome.enabled,children:[(0,n.jsx)(z.l,{children:(0,n.jsxs)(c.z,{children:[(0,n.jsx)(d.I,{as:q.Qz2}),(0,n.jsx)(h.E,{children:"Welcome Channel"})]})}),(0,n.jsx)(S.l,{placeholder:"Select a channel",value:ex.welcome.channelId||"",onChange:e=>eM("channelId",e.target.value),children:eE.map(e=>(0,n.jsxs)("option",{value:e.id,children:["#",e.name]},e.id))}),0===eE.length&&(0,n.jsx)(h.E,{fontSize:"sm",color:"gray.500",mt:1,children:"No text channels found. Make sure the bot has permission to view channels."})]})})})]}),(0,n.jsxs)(f.Z,{opacity:ex.welcome.enabled?1:.6,children:[(0,n.jsx)(v.a,{children:(0,n.jsxs)(c.z,{justify:"space-between",children:[(0,n.jsxs)(g.T,{align:"start",spacing:1,children:[(0,n.jsx)(D.D,{size:"md",children:"Welcome Message Templates"}),(0,n.jsx)(h.E,{fontSize:"sm",color:"gray.500",children:"Create multiple templates - one will be randomly selected for each new member"})]}),(0,n.jsxs)(c.z,{children:[(0,n.jsxs)(E.E,{colorScheme:"green",variant:"subtle",children:[ex.welcome.messages.length," template",1!==ex.welcome.messages.length?"s":""]}),(0,n.jsx)(F.$,{leftIcon:(0,n.jsx)(q.GGD,{}),colorScheme:"blue",size:"sm",onClick:()=>{let e={title:"\uD83C\uDFAE Welcome to {guild}, {userName}!",description:"Welcome to our server! We're glad to have you here.\n\n\uD83D\uDCC5 Account Created: {UserCreation}\n\uD83C\uDFAF Join Date: {joinDate}",color:"#00FF00",footer:"You're our {memberCount} member!"};ep(s=>({...s,welcome:{...s.welcome,messages:[...s.welcome.messages,e]}})),eC(ex.welcome.messages.length)},isDisabled:!ex.welcome.enabled,children:"Add Template"})]})]})}),(0,n.jsx)(k.b,{children:(0,n.jsxs)(g.T,{spacing:4,align:"stretch",children:[(0,n.jsx)(c.z,{spacing:2,wrap:"wrap",children:ex.welcome.messages.map((e,s)=>(0,n.jsxs)(F.$,{size:"sm",variant:eD===s?"solid":"outline",colorScheme:"blue",onClick:()=>eC(s),isDisabled:!ex.welcome.enabled,children:["Template ",s+1]},s))}),ex.welcome.messages[eD]&&(0,n.jsx)(N.a,{p:4,borderWidth:1,borderRadius:"md",bg:"gray.50",_dark:{bg:"gray.800"},children:(0,n.jsxs)(g.T,{spacing:4,align:"stretch",children:[(0,n.jsxs)(c.z,{justify:"space-between",children:[(0,n.jsxs)(h.E,{fontWeight:"bold",children:["Template ",eD+1]}),(0,n.jsxs)(c.z,{children:[(0,n.jsx)(A.m,{label:"Duplicate Template",children:(0,n.jsx)(M.K,{"aria-label":"Duplicate template",icon:(0,n.jsx)(q.nxz,{}),size:"sm",variant:"ghost",onClick:()=>eL(eD),isDisabled:!ex.welcome.enabled})}),(0,n.jsx)(A.m,{label:"Delete Template",children:(0,n.jsx)(M.K,{"aria-label":"Delete template",icon:(0,n.jsx)(q.IXo,{}),size:"sm",variant:"ghost",colorScheme:"red",onClick:()=>e_(eD),isDisabled:!ex.welcome.enabled||ex.welcome.messages.length<=1})})]})]}),(0,n.jsxs)(T.MJ,{isDisabled:!ex.welcome.enabled,children:[(0,n.jsx)(z.l,{children:"Embed Title"}),(0,n.jsx)(W.p,{value:(null==(s=ex.welcome.messages[eD])?void 0:s.title)||"",onChange:e=>eB(eD,"title",e.target.value),placeholder:"e.g., \uD83C\uDFAE Welcome to {guild}, {userName}!"})]}),(0,n.jsxs)(T.MJ,{isDisabled:!ex.welcome.enabled,children:[(0,n.jsx)(z.l,{children:"Embed Description"}),(0,n.jsx)(G.T,{value:(null==(o=ex.welcome.messages[eD])?void 0:o.description)||"",onChange:e=>eB(eD,"description",e.target.value),placeholder:"Enter your welcome message description...",rows:6})]}),(0,n.jsxs)(c.z,{spacing:4,children:[(0,n.jsxs)(T.MJ,{isDisabled:!ex.welcome.enabled,children:[(0,n.jsx)(z.l,{children:"Embed Color"}),(0,n.jsx)(W.p,{type:"color",value:(null==(ee=ex.welcome.messages[eD])?void 0:ee.color)||"#00FF00",onChange:e=>eB(eD,"color",e.target.value),w:"100px"})]}),(0,n.jsxs)(T.MJ,{isDisabled:!ex.welcome.enabled,children:[(0,n.jsx)(z.l,{children:"Embed Footer"}),(0,n.jsx)(W.p,{value:(null==(es=ex.welcome.messages[eD])?void 0:es.footer)||"",onChange:e=>eB(eD,"footer",e.target.value),placeholder:"e.g., You're our {memberCount} member!"})]})]})]})})]})})]}),(0,n.jsxs)(f.Z,{opacity:ex.welcome.enabled?1:.6,children:[(0,n.jsx)(v.a,{children:(0,n.jsxs)(c.z,{justify:"space-between",children:[(0,n.jsxs)(g.T,{align:"start",spacing:1,children:[(0,n.jsx)(D.D,{size:"md",children:"Auto-Role Assignment"}),(0,n.jsx)(h.E,{fontSize:"sm",color:"gray.500",children:"Automatically assign roles to new members when they join"})]}),(0,n.jsx)(C.d,{isChecked:ex.welcome.autoRole.enabled,onChange:e=>eR("enabled",e.target.checked),isDisabled:!ex.welcome.enabled})]})}),(0,n.jsx)(k.b,{children:(0,n.jsxs)(g.T,{spacing:4,align:"stretch",children:[(0,n.jsxs)(T.MJ,{isDisabled:!ex.welcome.enabled||!ex.welcome.autoRole.enabled,children:[(0,n.jsx)(z.l,{children:"Roles to Assign"}),(0,n.jsx)(N.a,{p:3,borderWidth:1,borderRadius:"md",maxH:"200px",overflowY:"auto",children:(0,n.jsx)(I.$,{value:ex.welcome.autoRole.roleIds,onChange:e=>eR("roleIds",e),children:(0,n.jsx)(R.r,{columns:{base:1,md:2},spacing:2,children:eF.map(e=>(0,n.jsx)(J.S,{value:e.id,children:(0,n.jsxs)(c.z,{children:[(0,n.jsx)(N.a,{w:3,h:3,borderRadius:"full",bg:"#".concat(e.color.toString(16).padStart(6,"0"))}),(0,n.jsx)(h.E,{children:e.name})]})},e.id))})})}),0===eF.length&&(0,n.jsx)(h.E,{fontSize:"sm",color:"gray.500",mt:1,children:"No manageable roles found. Make sure the bot has permission to manage roles."})]}),(0,n.jsx)(_.n,{allowToggle:!0,children:(0,n.jsxs)(U.A,{children:[(0,n.jsxs)(B.J,{children:[(0,n.jsx)(N.a,{flex:"1",textAlign:"left",children:(0,n.jsx)(h.E,{fontWeight:"medium",children:"Advanced Auto-Role Settings"})}),(0,n.jsx)(P.Q,{})]}),(0,n.jsx)(L.v,{pb:4,children:(0,n.jsxs)(g.T,{spacing:4,align:"stretch",children:[(0,n.jsxs)(T.MJ,{isDisabled:!ex.welcome.enabled||!ex.welcome.autoRole.enabled,children:[(0,n.jsx)(z.l,{children:"Assignment Delay (milliseconds)"}),(0,n.jsxs)(H.Q7,{value:ex.welcome.autoRole.delay,onChange:(e,s)=>eR("delay",s),min:0,max:6e4,step:100,children:[(0,n.jsx)(H.OO,{}),(0,n.jsxs)(H.lw,{children:[(0,n.jsx)(H.Q0,{}),(0,n.jsx)(H.Sh,{})]})]}),(0,n.jsx)(h.E,{fontSize:"xs",color:"gray.500",mt:1,children:"Delay before assigning roles (useful if other bots need to process first)"})]}),(0,n.jsxs)(T.MJ,{display:"flex",alignItems:"center",isDisabled:!ex.welcome.enabled||!ex.welcome.autoRole.enabled,children:[(0,n.jsx)(z.l,{htmlFor:"retry-enabled",mb:"0",children:"Enable Retry on Failure"}),(0,n.jsx)(C.d,{id:"retry-enabled",isChecked:ex.welcome.autoRole.retry.enabled,onChange:e=>eJ("enabled",e.target.checked)})]}),ex.welcome.autoRole.retry.enabled&&(0,n.jsxs)(c.z,{spacing:4,children:[(0,n.jsxs)(T.MJ,{isDisabled:!ex.welcome.enabled||!ex.welcome.autoRole.enabled,children:[(0,n.jsx)(z.l,{children:"Max Retry Attempts"}),(0,n.jsxs)(H.Q7,{value:ex.welcome.autoRole.retry.maxAttempts,onChange:(e,s)=>eJ("maxAttempts",s),min:1,max:10,children:[(0,n.jsx)(H.OO,{}),(0,n.jsxs)(H.lw,{children:[(0,n.jsx)(H.Q0,{}),(0,n.jsx)(H.Sh,{})]})]})]}),(0,n.jsxs)(T.MJ,{isDisabled:!ex.welcome.enabled||!ex.welcome.autoRole.enabled,children:[(0,n.jsx)(z.l,{children:"Retry Delay (ms)"}),(0,n.jsxs)(H.Q7,{value:ex.welcome.autoRole.retry.delayBetweenAttempts,onChange:(e,s)=>eJ("delayBetweenAttempts",s),min:1e3,max:3e4,step:1e3,children:[(0,n.jsx)(H.OO,{}),(0,n.jsxs)(H.lw,{children:[(0,n.jsx)(H.Q0,{}),(0,n.jsx)(H.Sh,{})]})]})]})]})]})})]})})]})})]}),(0,n.jsxs)(f.Z,{opacity:ex.welcome.enabled?1:.6,children:[(0,n.jsx)(v.a,{children:(0,n.jsxs)(c.z,{justify:"space-between",children:[(0,n.jsxs)(g.T,{align:"start",spacing:1,children:[(0,n.jsx)(D.D,{size:"md",children:"Random GIF Images"}),(0,n.jsx)(h.E,{fontSize:"sm",color:"gray.500",children:"Show random anime GIFs for users without Nitro banners (powered by Nekos.best)"})]}),(0,n.jsx)(C.d,{isChecked:(null==(eo=ex.welcome.nekosGif)?void 0:eo.enabled)||!1,onChange:e=>eG("enabled",e.target.checked),isDisabled:!ex.welcome.enabled})]})}),(0,n.jsx)(k.b,{children:(0,n.jsxs)(g.T,{spacing:4,align:"stretch",children:[(0,n.jsxs)(T.MJ,{isDisabled:!ex.welcome.enabled||!(null==(en=ex.welcome.nekosGif)?void 0:en.enabled),children:[(0,n.jsx)(z.l,{children:"GIF Type"}),(0,n.jsxs)(S.l,{value:(null==(el=ex.welcome.nekosGif)?void 0:el.type)||"wave",onChange:e=>eG("type",e.target.value),children:[(0,n.jsx)("option",{value:"wave",children:"\uD83D\uDC4B Wave (Greeting)"}),(0,n.jsx)("option",{value:"hug",children:"\uD83E\uDD17 Hug (Welcoming)"}),(0,n.jsx)("option",{value:"pat",children:"\uD83D\uDC4B Pat (Friendly)"}),(0,n.jsx)("option",{value:"happy",children:"\uD83D\uDE0A Happy (Cheerful)"}),(0,n.jsx)("option",{value:"dance",children:"\uD83D\uDC83 Dance (Celebration)"}),(0,n.jsx)("option",{value:"thumbsup",children:"\uD83D\uDC4D Thumbs Up (Approval)"})]}),(0,n.jsx)(h.E,{fontSize:"xs",color:"gray.500",mt:1,children:"Choose the type of GIF to show when users don't have a custom banner"})]}),(0,n.jsxs)(O.F,{status:"info",size:"sm",children:[(0,n.jsx)(K._,{}),(0,n.jsx)(N.a,{children:(0,n.jsx)(Z.T,{fontSize:"sm",children:"GIFs are only shown for users without Nitro banners. Users with custom banners will display their banner instead."})})]})]})})]})]})}),(0,n.jsx)(w.K,{children:(0,n.jsxs)(g.T,{spacing:6,align:"stretch",children:[(0,n.jsxs)(f.Z,{children:[(0,n.jsx)(v.a,{children:(0,n.jsxs)(c.z,{justify:"space-between",children:[(0,n.jsx)(D.D,{size:"md",children:"Goodbye System"}),(0,n.jsx)(C.d,{size:"lg",isChecked:ex.goodbye.enabled,onChange:e=>eW("enabled",e.target.checked)})]})}),(0,n.jsx)(k.b,{children:(0,n.jsx)(g.T,{spacing:4,align:"stretch",children:(0,n.jsxs)(T.MJ,{isDisabled:!ex.goodbye.enabled,children:[(0,n.jsx)(z.l,{children:(0,n.jsxs)(c.z,{children:[(0,n.jsx)(d.I,{as:q.Qz2}),(0,n.jsx)(h.E,{children:"Goodbye Channel"})]})}),(0,n.jsx)(S.l,{placeholder:"Select a channel",value:ex.goodbye.channelId||"",onChange:e=>eW("channelId",e.target.value),children:eE.map(e=>(0,n.jsxs)("option",{value:e.id,children:["#",e.name]},e.id))}),0===eE.length&&(0,n.jsx)(h.E,{fontSize:"sm",color:"gray.500",mt:1,children:"No text channels found. Make sure the bot has permission to view channels."})]})})})]}),(0,n.jsxs)(f.Z,{opacity:ex.goodbye.enabled?1:.6,children:[(0,n.jsx)(v.a,{children:(0,n.jsxs)(c.z,{justify:"space-between",children:[(0,n.jsxs)(g.T,{align:"start",spacing:1,children:[(0,n.jsx)(D.D,{size:"md",children:"Goodbye Message Templates"}),(0,n.jsx)(h.E,{fontSize:"sm",color:"gray.500",children:"Create multiple templates - one will be randomly selected for each departing member"})]}),(0,n.jsxs)(c.z,{children:[(0,n.jsxs)(E.E,{colorScheme:"red",variant:"subtle",children:[ex.goodbye.messages.length," template",1!==ex.goodbye.messages.length?"s":""]}),(0,n.jsx)(F.$,{leftIcon:(0,n.jsx)(q.GGD,{}),colorScheme:"red",size:"sm",onClick:()=>{let e={title:"\uD83D\uDC4B Goodbye {userName}",description:"Thanks for being part of {guild}! We'll miss you.\n\n{kickStatus}\n⏰ Member Since: {user-joinedAt}",color:"#FF0000",footer:"Safe travels!"};ep(s=>({...s,goodbye:{...s.goodbye,messages:[...s.goodbye.messages,e]}})),eT(ex.goodbye.messages.length)},isDisabled:!ex.goodbye.enabled,children:"Add Template"})]})]})}),(0,n.jsx)(k.b,{children:(0,n.jsxs)(g.T,{spacing:4,align:"stretch",children:[(0,n.jsx)(c.z,{spacing:2,wrap:"wrap",children:ex.goodbye.messages.map((e,s)=>(0,n.jsxs)(F.$,{size:"sm",variant:ek===s?"solid":"outline",colorScheme:"red",onClick:()=>eT(s),isDisabled:!ex.goodbye.enabled,children:["Template ",s+1]},s))}),ex.goodbye.messages[ek]&&(0,n.jsx)(N.a,{p:4,borderWidth:1,borderRadius:"md",bg:"gray.50",_dark:{bg:"gray.800"},children:(0,n.jsxs)(g.T,{spacing:4,align:"stretch",children:[(0,n.jsxs)(c.z,{justify:"space-between",children:[(0,n.jsxs)(h.E,{fontWeight:"bold",children:["Template ",ek+1]}),(0,n.jsxs)(c.z,{children:[(0,n.jsx)(A.m,{label:"Duplicate Template",children:(0,n.jsx)(M.K,{"aria-label":"Duplicate template",icon:(0,n.jsx)(q.nxz,{}),size:"sm",variant:"ghost",onClick:()=>eH(ek),isDisabled:!ex.goodbye.enabled})}),(0,n.jsx)(A.m,{label:"Delete Template",children:(0,n.jsx)(M.K,{"aria-label":"Delete template",icon:(0,n.jsx)(q.IXo,{}),size:"sm",variant:"ghost",colorScheme:"red",onClick:()=>eU(ek),isDisabled:!ex.goodbye.enabled||ex.goodbye.messages.length<=1})})]})]}),(0,n.jsxs)(T.MJ,{isDisabled:!ex.goodbye.enabled,children:[(0,n.jsx)(z.l,{children:"Embed Title"}),(0,n.jsx)(W.p,{value:(null==(ei=ex.goodbye.messages[ek])?void 0:ei.title)||"",onChange:e=>eP(ek,"title",e.target.value),placeholder:"e.g., \uD83D\uDC4B Goodbye {userName}"})]}),(0,n.jsxs)(T.MJ,{isDisabled:!ex.goodbye.enabled,children:[(0,n.jsx)(z.l,{children:"Embed Description"}),(0,n.jsx)(G.T,{value:(null==(ea=ex.goodbye.messages[ek])?void 0:ea.description)||"",onChange:e=>eP(ek,"description",e.target.value),placeholder:"Enter your goodbye message description...",rows:6})]}),(0,n.jsxs)(c.z,{spacing:4,children:[(0,n.jsxs)(T.MJ,{isDisabled:!ex.goodbye.enabled,children:[(0,n.jsx)(z.l,{children:"Embed Color"}),(0,n.jsx)(W.p,{type:"color",value:(null==(er=ex.goodbye.messages[ek])?void 0:er.color)||"#FF0000",onChange:e=>eP(ek,"color",e.target.value),w:"100px"})]}),(0,n.jsxs)(T.MJ,{isDisabled:!ex.goodbye.enabled,children:[(0,n.jsx)(z.l,{children:"Embed Footer"}),(0,n.jsx)(W.p,{value:(null==(et=ex.goodbye.messages[ek])?void 0:et.footer)||"",onChange:e=>eP(ek,"footer",e.target.value),placeholder:"e.g., Safe travels!"})]})]})]})})]})})]}),(0,n.jsxs)(f.Z,{opacity:ex.goodbye.enabled?1:.6,children:[(0,n.jsx)(v.a,{children:(0,n.jsxs)(c.z,{justify:"space-between",children:[(0,n.jsxs)(g.T,{align:"start",spacing:1,children:[(0,n.jsx)(D.D,{size:"md",children:"Random GIF Images"}),(0,n.jsx)(h.E,{fontSize:"sm",color:"gray.500",children:"Show random anime GIFs for users without Nitro banners (powered by Nekos.best)"})]}),(0,n.jsx)(C.d,{isChecked:(null==(ec=ex.goodbye.nekosGif)?void 0:ec.enabled)||!1,onChange:e=>eI("enabled",e.target.checked),isDisabled:!ex.goodbye.enabled})]})}),(0,n.jsx)(k.b,{children:(0,n.jsxs)(g.T,{spacing:4,align:"stretch",children:[(0,n.jsxs)(T.MJ,{isDisabled:!ex.goodbye.enabled||!(null==(ed=ex.goodbye.nekosGif)?void 0:ed.enabled),children:[(0,n.jsx)(z.l,{children:"GIF Type"}),(0,n.jsxs)(S.l,{value:(null==(eh=ex.goodbye.nekosGif)?void 0:eh.type)||"cry",onChange:e=>eI("type",e.target.value),children:[(0,n.jsx)("option",{value:"cry",children:"\uD83D\uDE22 Cry (Sad Goodbye)"}),(0,n.jsx)("option",{value:"wave",children:"\uD83D\uDC4B Wave (Farewell)"}),(0,n.jsx)("option",{value:"sad",children:"\uD83D\uDE14 Sad (Melancholy)"}),(0,n.jsx)("option",{value:"sleep",children:"\uD83D\uDE34 Sleep (Peaceful)"}),(0,n.jsx)("option",{value:"pat",children:"\uD83D\uDC4B Pat (Comforting)"}),(0,n.jsx)("option",{value:"hug",children:"\uD83E\uDD17 Hug (Supportive)"})]}),(0,n.jsx)(h.E,{fontSize:"xs",color:"gray.500",mt:1,children:"Choose the type of GIF to show when users don't have a custom banner"})]}),(0,n.jsxs)(O.F,{status:"info",size:"sm",children:[(0,n.jsx)(K._,{}),(0,n.jsx)(N.a,{children:(0,n.jsx)(Z.T,{fontSize:"sm",children:"GIFs are only shown for users without Nitro banners. Users with custom banners will display their banner instead."})})]})]})})]})]})}),(0,n.jsx)(w.K,{children:(0,n.jsxs)(g.T,{spacing:6,align:"stretch",children:[(0,n.jsxs)(O.F,{status:"info",children:[(0,n.jsx)(K._,{}),(0,n.jsxs)(N.a,{children:[(0,n.jsx)(Q.X,{children:"Placeholder Guide"}),(0,n.jsx)(Z.T,{children:"Use these placeholders in your message templates. They will be automatically replaced with actual values when messages are sent."})]})]}),(0,n.jsxs)(R.r,{columns:{base:1,lg:2},spacing:6,children:[(0,n.jsxs)(f.Z,{children:[(0,n.jsx)(v.a,{children:(0,n.jsx)(D.D,{size:"md",color:"blue.500",children:"\uD83D\uDC64 User Placeholders"})}),(0,n.jsx)(k.b,{children:(0,n.jsx)(g.T,{spacing:3,align:"stretch",children:V.user.map((e,s)=>(0,n.jsx)(N.a,{p:3,borderWidth:1,borderRadius:"md",bg:"blue.50",_dark:{bg:"blue.900"},children:(0,n.jsxs)(c.z,{justify:"space-between",children:[(0,n.jsxs)(g.T,{align:"start",spacing:1,children:[(0,n.jsx)(h.E,{fontFamily:"mono",fontWeight:"bold",color:"blue.600",_dark:{color:"blue.300"},children:e.name}),(0,n.jsx)(h.E,{fontSize:"sm",color:"gray.600",_dark:{color:"gray.400"},children:e.description})]}),(0,n.jsx)(M.K,{"aria-label":"Copy placeholder",icon:(0,n.jsx)(q.nxz,{}),size:"sm",variant:"ghost",onClick:()=>{navigator.clipboard.writeText(e.name),ej({title:"Copied!",description:"".concat(e.name," copied to clipboard"),status:"success",duration:2e3})}})]})},s))})})]}),(0,n.jsxs)(f.Z,{children:[(0,n.jsx)(v.a,{children:(0,n.jsx)(D.D,{size:"md",color:"green.500",children:"\uD83C\uDFF0 Server Placeholders"})}),(0,n.jsx)(k.b,{children:(0,n.jsx)(g.T,{spacing:3,align:"stretch",children:V.guild.map((e,s)=>(0,n.jsx)(N.a,{p:3,borderWidth:1,borderRadius:"md",bg:"green.50",_dark:{bg:"green.900"},children:(0,n.jsxs)(c.z,{justify:"space-between",children:[(0,n.jsxs)(g.T,{align:"start",spacing:1,children:[(0,n.jsx)(h.E,{fontFamily:"mono",fontWeight:"bold",color:"green.600",_dark:{color:"green.300"},children:e.name}),(0,n.jsx)(h.E,{fontSize:"sm",color:"gray.600",_dark:{color:"gray.400"},children:e.description})]}),(0,n.jsx)(M.K,{"aria-label":"Copy placeholder",icon:(0,n.jsx)(q.nxz,{}),size:"sm",variant:"ghost",onClick:()=>{navigator.clipboard.writeText(e.name),ej({title:"Copied!",description:"".concat(e.name," copied to clipboard"),status:"success",duration:2e3})}})]})},s))})})]}),(0,n.jsxs)(f.Z,{children:[(0,n.jsx)(v.a,{children:(0,n.jsx)(D.D,{size:"md",color:"purple.500",children:"⏰ Time Placeholders"})}),(0,n.jsx)(k.b,{children:(0,n.jsx)(g.T,{spacing:3,align:"stretch",children:V.time.map((e,s)=>(0,n.jsx)(N.a,{p:3,borderWidth:1,borderRadius:"md",bg:"purple.50",_dark:{bg:"purple.900"},children:(0,n.jsxs)(c.z,{justify:"space-between",children:[(0,n.jsxs)(g.T,{align:"start",spacing:1,children:[(0,n.jsx)(h.E,{fontFamily:"mono",fontWeight:"bold",color:"purple.600",_dark:{color:"purple.300"},children:e.name}),(0,n.jsx)(h.E,{fontSize:"sm",color:"gray.600",_dark:{color:"gray.400"},children:e.description})]}),(0,n.jsx)(M.K,{"aria-label":"Copy placeholder",icon:(0,n.jsx)(q.nxz,{}),size:"sm",variant:"ghost",onClick:()=>{navigator.clipboard.writeText(e.name),ej({title:"Copied!",description:"".concat(e.name," copied to clipboard"),status:"success",duration:2e3})}})]})},s))})})]}),(0,n.jsxs)(f.Z,{children:[(0,n.jsx)(v.a,{children:(0,n.jsx)(D.D,{size:"md",color:"orange.500",children:"\uD83C\uDF89 Welcome Only"})}),(0,n.jsx)(k.b,{children:(0,n.jsx)(g.T,{spacing:3,align:"stretch",children:V.welcome.map((e,s)=>(0,n.jsx)(N.a,{p:3,borderWidth:1,borderRadius:"md",bg:"orange.50",_dark:{bg:"orange.900"},children:(0,n.jsxs)(c.z,{justify:"space-between",children:[(0,n.jsxs)(g.T,{align:"start",spacing:1,children:[(0,n.jsx)(h.E,{fontFamily:"mono",fontWeight:"bold",color:"orange.600",_dark:{color:"orange.300"},children:e.name}),(0,n.jsx)(h.E,{fontSize:"sm",color:"gray.600",_dark:{color:"gray.400"},children:e.description})]}),(0,n.jsx)(M.K,{"aria-label":"Copy placeholder",icon:(0,n.jsx)(q.nxz,{}),size:"sm",variant:"ghost",onClick:()=>{navigator.clipboard.writeText(e.name),ej({title:"Copied!",description:"".concat(e.name," copied to clipboard"),status:"success",duration:2e3})}})]})},s))})})]}),(0,n.jsxs)(f.Z,{children:[(0,n.jsx)(v.a,{children:(0,n.jsx)(D.D,{size:"md",color:"red.500",children:"\uD83D\uDC4B Goodbye Only"})}),(0,n.jsx)(k.b,{children:(0,n.jsx)(g.T,{spacing:3,align:"stretch",children:V.goodbye.map((e,s)=>(0,n.jsx)(N.a,{p:3,borderWidth:1,borderRadius:"md",bg:"red.50",_dark:{bg:"red.900"},children:(0,n.jsxs)(c.z,{justify:"space-between",children:[(0,n.jsxs)(g.T,{align:"start",spacing:1,children:[(0,n.jsx)(h.E,{fontFamily:"mono",fontWeight:"bold",color:"red.600",_dark:{color:"red.300"},children:e.name}),(0,n.jsx)(h.E,{fontSize:"sm",color:"gray.600",_dark:{color:"gray.400"},children:e.description})]}),(0,n.jsx)(M.K,{"aria-label":"Copy placeholder",icon:(0,n.jsx)(q.nxz,{}),size:"sm",variant:"ghost",onClick:()=>{navigator.clipboard.writeText(e.name),ej({title:"Copied!",description:"".concat(e.name," copied to clipboard"),status:"success",duration:2e3})}})]})},s))})})]})]})]})})]})]})}),(0,n.jsx)(Y.j,{children:(0,n.jsxs)(c.z,{spacing:3,children:[(0,n.jsx)(F.$,{variant:"ghost",onClick:eu,children:"Cancel"}),(0,n.jsx)(F.$,{colorScheme:"blue",onClick:eA,isLoading:ef,isDisabled:ey,leftIcon:(0,n.jsx)(q.de5,{}),children:"Save Welcome & Goodbye System"})]})})]})]})}}}]);