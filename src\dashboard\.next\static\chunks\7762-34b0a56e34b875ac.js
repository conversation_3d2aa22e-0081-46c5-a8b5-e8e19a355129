(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7762],{5095:(t,e,n)=>{"use strict";n.d(e,{EO:()=>s,Lt:()=>a});var r=n(94513),i=n(9557),o=n(52922);function a(t){let{leastDestructiveRef:e,...n}=t;return(0,r.jsx)(i.aF,{...n,initialFocusRef:e})}let s=(0,n(2923).R)((t,e)=>(0,r.jsx)(o.$,{ref:e,role:"alertdialog",...t}))},6159:(t,e,n)=>{"use strict";n.d(e,{M:()=>v,Z:()=>d});var r=n(94513),i=n(75387),o=n(29035),a=n(22697),s=n(47133),u=n(72097),l=n(94285),c=n(2923),h=n(56915),f=n(33225);let[p,d]=(0,o.q)({name:"InputGroupStylesContext",errorMessage:"useInputGroupStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<InputGroup />\" "}),v=(0,c.R)(function(t,e){let n=(0,h.o)("Input",t),{children:o,className:c,...d}=(0,i.M)(t),v=(0,a.cx)("chakra-input__group",c),m={},y=(0,s.a)(o),_=n.field;y.forEach(t=>{n&&(_&&"InputLeftElement"===t.type.id&&(m.paddingStart=_.height??_.h),_&&"InputRightElement"===t.type.id&&(m.paddingEnd=_.height??_.h),"InputRightAddon"===t.type.id&&(m.borderEndRadius=0),"InputLeftAddon"===t.type.id&&(m.borderStartRadius=0))});let g=y.map(e=>{let n=(0,u.o)({size:e.props?.size||t.size,variant:e.props?.variant||t.variant});return"Input"!==e.type.id?(0,l.cloneElement)(e,n):(0,l.cloneElement)(e,Object.assign(n,m,e.props))});return(0,r.jsx)(f.B.div,{className:v,ref:e,__css:{width:"100%",display:"flex",position:"relative",isolation:"isolate",...n.group},"data-group":!0,...d,children:(0,r.jsx)(p,{value:n,children:g})})});v.displayName="InputGroup"},16791:(t,e,n)=>{"use strict";n.d(e,{w:()=>u});var r=n(94513),i=n(22697),o=n(51413),a=n(2923),s=n(33225);let u=(0,a.R)(function(t,e){let{className:n,justify:a,...u}=t,l=(0,o.Q)();return(0,r.jsx)(s.B.div,{ref:e,className:(0,i.cx)("chakra-card__footer",n),__css:{display:"flex",justifyContent:a,...l.footer},...u})})},18303:(t,e,n)=>{"use strict";n.d(e,{i:()=>o});var r=n(94285),i=n(65507);function o(t){let{value:e,defaultValue:n,onChange:o,shouldUpdate:a=(t,e)=>t!==e}=t,s=(0,i.c)(o),u=(0,i.c)(a),[l,c]=(0,r.useState)(n),h=void 0!==e,f=h?e:l,p=(0,i.c)(t=>{let e="function"==typeof t?t(f):t;u(f,e)&&(h||c(e),s(e))},[h,s,f,u]);return[f,p]}},19653:(t,e,n)=>{"use strict";n.d(e,{Ay:()=>a,GK:()=>o,Rw:()=>i,vr:()=>r});let r={passive:!1},i={capture:!0,passive:!1};function o(t){t.stopImmediatePropagation()}function a(t){t.preventDefault(),t.stopImmediatePropagation()}},20429:(t,e,n)=>{"use strict";n.d(e,{a:()=>u});var r=n(94513),i=n(22697),o=n(51413),a=n(2923),s=n(33225);let u=(0,a.R)(function(t,e){let{className:n,...a}=t,u=(0,o.Q)();return(0,r.jsx)(s.B.div,{ref:e,className:(0,i.cx)("chakra-card__header",n),__css:u.header,...a})})},22237:(t,e,n)=>{"use strict";n.d(e,{S:()=>A});var r=n(94513),i=n(75387),o=n(50614),a=n(72097),s=n(22697),u=n(610),l=n(94285),c=n(70423),h=n(33225);function f(t){return(0,r.jsx)(h.B.svg,{width:"1.2em",viewBox:"0 0 12 10",style:{fill:"none",strokeWidth:2,stroke:"currentColor",strokeDasharray:16},...t,children:(0,r.jsx)("polyline",{points:"1.5 6 4.5 9 10.5 1"})})}function p(t){return(0,r.jsx)(h.B.svg,{width:"1.2em",viewBox:"0 0 24 24",style:{stroke:"currentColor",strokeWidth:4},...t,children:(0,r.jsx)("line",{x1:"21",x2:"3",y1:"12",y2:"12"})})}function d(t){let{isIndeterminate:e,isChecked:n,...i}=t;return n||e?(0,r.jsx)(h.B.div,{style:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%"},children:(0,r.jsx)(e?p:f,{...i})}):null}var v=n(96027),m=n(2923),y=n(56915);let _={display:"inline-flex",alignItems:"center",justifyContent:"center",verticalAlign:"top",userSelect:"none",flexShrink:0},g={cursor:"pointer",display:"inline-flex",alignItems:"center",verticalAlign:"top",position:"relative"},w=(0,u.i7)({from:{opacity:0,strokeDashoffset:16,transform:"scale(0.95)"},to:{opacity:1,strokeDashoffset:0,transform:"scale(1)"}}),x=(0,u.i7)({from:{opacity:0},to:{opacity:1}}),b=(0,u.i7)({from:{transform:"scaleX(0.65)"},to:{transform:"scaleX(1)"}}),A=(0,m.R)(function(t,e){let n=(0,c.L)(),u={...n,...t},f=(0,y.o)("Checkbox",u),p=(0,i.M)(t),{spacing:m="0.5rem",className:A,children:k,iconColor:E,iconSize:j,icon:S=(0,r.jsx)(d,{}),isChecked:N,isDisabled:T=n?.isDisabled,onChange:C,inputProps:M,...z}=p,I=N;n?.value&&p.value&&(I=n.value.includes(p.value));let R=C;n?.onChange&&p.value&&(R=(0,o.O)(n.onChange,C));let{state:B,getInputProps:O,getCheckboxProps:P,getLabelProps:D,getRootProps:L}=(0,v.v)({...z,isDisabled:T,isChecked:I,onChange:R}),X=function(t){let[e,n]=(0,l.useState)(t),[r,i]=(0,l.useState)(!1);return t!==e&&(i(!0),n(t)),r}(B.isChecked),q=(0,l.useMemo)(()=>({animation:X?B.isIndeterminate?`${x} 20ms linear, ${b} 200ms linear`:`${w} 200ms linear`:void 0,...f.icon,...(0,a.o)({fontSize:j,color:E})}),[E,j,X,B.isIndeterminate,f.icon]),Y=(0,l.cloneElement)(S,{__css:q,isIndeterminate:B.isIndeterminate,isChecked:B.isChecked});return(0,r.jsxs)(h.B.label,{__css:{...g,...f.container},className:(0,s.cx)("chakra-checkbox",A),...L(),children:[(0,r.jsx)("input",{className:"chakra-checkbox__input",...O(M,e)}),(0,r.jsx)(h.B.span,{__css:{..._,...f.control},className:"chakra-checkbox__control",...P(),children:Y}),k&&(0,r.jsx)(h.B.span,{className:"chakra-checkbox__label",...D(),__css:{marginStart:m,...f.label},children:k})]})});A.displayName="Checkbox"},24472:()=>{},25257:(t,e,n)=>{"use strict";n.d(e,{A:()=>o,y:()=>a});var r=n(31530),i=n(19653);function o(t){var e=t.document.documentElement,n=(0,r.A)(t).on("dragstart.drag",i.Ay,i.Rw);"onselectstart"in e?n.on("selectstart.drag",i.Ay,i.Rw):(e.__noselect=e.style.MozUserSelect,e.style.MozUserSelect="none")}function a(t,e){var n=t.document.documentElement,o=(0,r.A)(t).on("dragstart.drag",null);e&&(o.on("click.drag",i.Ay,i.Rw),setTimeout(function(){o.on("click.drag",null)},0)),"onselectstart"in n?o.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}},25964:(t,e,n)=>{"use strict";n.d(e,{l:()=>v});var r=n(94513),i=n(75387),o=n(16229),a=n(54338),s=n(81405),u=n(94285),l=n(22697),c=n(2923),h=n(33225);let f=(0,c.R)(function(t,e){let{children:n,placeholder:i,className:o,...a}=t;return(0,r.jsxs)(h.B.select,{...a,ref:e,className:(0,l.cx)("chakra-select",o),children:[i&&(0,r.jsx)("option",{value:"",children:i}),n]})});f.displayName="SelectField";var p=n(44637),d=n(56915);let v=(0,c.R)((t,e)=>{let n=(0,d.o)("Select",t),{rootProps:u,placeholder:l,icon:c,color:v,height:m,h:y,minH:g,minHeight:w,iconColor:x,iconSize:b,...A}=(0,i.M)(t),[k,E]=(0,a.l)(A,o.GF),j=(0,p.t)(E),S={paddingEnd:"2rem",...n.field,_focus:{zIndex:"unset",...n.field?._focus}};return(0,r.jsxs)(h.B.div,{className:"chakra-select__wrapper",__css:{width:"100%",height:"fit-content",position:"relative",color:v},...k,...u,children:[(0,r.jsx)(f,{ref:e,height:y??m,minH:g??w,placeholder:l,...j,__css:S,children:t.children}),(0,r.jsx)(_,{"data-disabled":(0,s.s)(j.disabled),...(x||v)&&{color:x||v},__css:n.icon,...b&&{fontSize:b},children:c})]})});v.displayName="Select";let m=t=>(0,r.jsx)("svg",{viewBox:"0 0 24 24",...t,children:(0,r.jsx)("path",{fill:"currentColor",d:"M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"})}),y=(0,h.B)("div",{baseStyle:{position:"absolute",display:"inline-flex",alignItems:"center",justifyContent:"center",pointerEvents:"none",top:"50%",transform:"translateY(-50%)"}}),_=t=>{let{children:e=(0,r.jsx)(m,{}),...n}=t,i=(0,u.cloneElement)(e,{role:"presentation",className:"chakra-select__icon",focusable:!1,"aria-hidden":!0,style:{width:"1em",height:"1em",color:"currentColor"}});return(0,r.jsx)(y,{...n,className:"chakra-select__icon-wrapper",children:(0,u.isValidElement)(e)?i:null})};_.displayName="SelectIcon"},28276:(t,e,n)=>{"use strict";function r(t){return function(){return this.matches(t)}}function i(t){return function(e){return e.matches(t)}}n.d(e,{A:()=>r,j:()=>i})},30662:(t,e,n)=>{"use strict";n.d(e,{A:()=>function t(e){if("string"==typeof e||"number"==typeof e)return""+e;let n="";if(Array.isArray(e))for(let r=0,i;r<e.length;r++)""!==(i=t(e[r]))&&(n+=(n&&" ")+i);else for(let t in e)e[t]&&(n+=(n&&" ")+t);return n}})},31530:(t,e,n)=>{"use strict";n.d(e,{A:()=>i});var r=n(71409);function i(t){return"string"==typeof t?new r.LN([[document.querySelector(t)]],[document.documentElement]):new r.LN([[t]],r.zr)}},31637:(t,e,n)=>{"use strict";n.d(e,{$c:()=>g,Jn:()=>k,O_:()=>y,Vh:()=>w,at:()=>f,uc:()=>m,uo:()=>A});var r=n(18303),i=n(78961),o=n(29035),a=n(50614),s=n(47133),u=n(18654),l=n(94285),c=n(87888),h=n(70011);let[f,p,d,v]=(0,c.D)();function m(t){let{defaultIndex:e,onChange:n,index:i,isManual:o,isLazy:a,lazyBehavior:s="unmount",orientation:u="horizontal",direction:c="ltr",...h}=t,[f,p]=(0,l.useState)(e??0),[v,m]=(0,r.i)({defaultValue:e??0,value:i,onChange:n});(0,l.useEffect)(()=>{null!=i&&p(i)},[i]);let y=d(),_=(0,l.useId)(),g=t.id??_;return{id:`tabs-${g}`,selectedIndex:v,focusedIndex:f,setSelectedIndex:m,setFocusedIndex:p,isManual:o,isLazy:a,lazyBehavior:s,orientation:u,descendants:y,direction:c,htmlProps:h}}let[y,_]=(0,o.q)({name:"TabsContext",errorMessage:"useTabsContext: `context` is undefined. Seems you forgot to wrap all tabs components within <Tabs />"});function g(t){let{focusedIndex:e,orientation:n,direction:r}=_(),i=p(),o=(0,l.useCallback)(t=>{let o=()=>{let t=i.nextEnabled(e);t&&t.node?.focus()},a=()=>{let t=i.prevEnabled(e);t&&t.node?.focus()},s="horizontal"===n,u="vertical"===n,l=t.key,c={["ltr"===r?"ArrowLeft":"ArrowRight"]:()=>s&&a(),["ltr"===r?"ArrowRight":"ArrowLeft"]:()=>s&&o(),ArrowDown:()=>u&&o(),ArrowUp:()=>u&&a(),Home:()=>{let t=i.firstEnabled();t&&t.node?.focus()},End:()=>{let t=i.lastEnabled();t&&t.node?.focus()}}[l];c&&(t.preventDefault(),c(t))},[i,e,n,r]);return{...t,role:"tablist","aria-orientation":n,onKeyDown:(0,a.H)(t.onKeyDown,o)}}function w(t){let{isDisabled:e=!1,isFocusable:n=!1,...r}=t,{setSelectedIndex:o,isManual:s,id:u,setFocusedIndex:l,selectedIndex:c}=_(),{index:f,register:p}=v({disabled:e&&!n}),d=f===c;return{...(0,h.I)({...r,ref:(0,i.Px)(p,t.ref),isDisabled:e,isFocusable:n,onClick:(0,a.H)(t.onClick,()=>{o(f)})}),id:E(u,f),role:"tab",tabIndex:d?0:-1,type:"button","aria-selected":d,"aria-controls":j(u,f),onFocus:e?void 0:(0,a.H)(t.onFocus,()=>{l(f);let t=e&&n;s||t||o(f)})}}let[x,b]=(0,o.q)({});function A(t){let{id:e,selectedIndex:n}=_(),r=(0,s.a)(t.children).map((t,r)=>(0,l.createElement)(x,{key:t.key??r,value:{isSelected:r===n,id:j(e,r),tabId:E(e,r),selectedIndex:n}},t));return{...t,children:r}}function k(t){let{children:e,...n}=t,{isLazy:r,lazyBehavior:i}=_(),{isSelected:o,id:a,tabId:s}=b(),c=(0,l.useRef)(!1);o&&(c.current=!0);let h=(0,u.q)({wasSelected:c.current,isSelected:o,enabled:r,mode:i});return{tabIndex:0,...n,children:h?e:null,role:"tabpanel","aria-labelledby":s,hidden:!o,id:a}}function E(t,e){return`${t}--tab-${e}`}function j(t,e){return`${t}--tabpanel-${e}`}},39767:(t,e,n)=>{"use strict";function r(){}function i(t){return null==t?r:function(){return this.querySelector(t)}}n.d(e,{A:()=>i})},40318:(t,e,n)=>{"use strict";n.d(e,{A:()=>i,j:()=>o});var r=n(43206);function i(t,e,n){return arguments.length>1?this.each((null==e?function(t){return function(){this.style.removeProperty(t)}}:"function"==typeof e?function(t,e,n){return function(){var r=e.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,n)}}:function(t,e,n){return function(){this.style.setProperty(t,e,n)}})(t,e,null==n?"":n)):o(this.node(),t)}function o(t,e){return t.style.getPropertyValue(e)||(0,r.A)(t).getComputedStyle(t,null).getPropertyValue(e)}},40713:(t,e,n)=>{"use strict";n.d(e,{A:()=>d});var r=n(53037),i=n(31530),o=n(53995),a=n(25257),s=n(19653);let u=t=>()=>t;function l(t,{sourceEvent:e,subject:n,target:r,identifier:i,active:o,x:a,y:s,dx:u,dy:l,dispatch:c}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:e,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:i,enumerable:!0,configurable:!0},active:{value:o,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:s,enumerable:!0,configurable:!0},dx:{value:u,enumerable:!0,configurable:!0},dy:{value:l,enumerable:!0,configurable:!0},_:{value:c}})}function c(t){return!t.ctrlKey&&!t.button}function h(){return this.parentNode}function f(t,e){return null==e?{x:t.x,y:t.y}:e}function p(){return navigator.maxTouchPoints||"ontouchstart"in this}function d(){var t,e,n,d,v=c,m=h,y=f,_=p,g={},w=(0,r.A)("start","drag","end"),x=0,b=0;function A(t){t.on("mousedown.drag",k).filter(_).on("touchstart.drag",S).on("touchmove.drag",N,s.vr).on("touchend.drag touchcancel.drag",T).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function k(r,o){if(!d&&v.call(this,r,o)){var u=C(this,m.call(this,r,o),r,o,"mouse");u&&((0,i.A)(r.view).on("mousemove.drag",E,s.Rw).on("mouseup.drag",j,s.Rw),(0,a.A)(r.view),(0,s.GK)(r),n=!1,t=r.clientX,e=r.clientY,u("start",r))}}function E(r){if((0,s.Ay)(r),!n){var i=r.clientX-t,o=r.clientY-e;n=i*i+o*o>b}g.mouse("drag",r)}function j(t){(0,i.A)(t.view).on("mousemove.drag mouseup.drag",null),(0,a.y)(t.view,n),(0,s.Ay)(t),g.mouse("end",t)}function S(t,e){if(v.call(this,t,e)){var n,r,i=t.changedTouches,o=m.call(this,t,e),a=i.length;for(n=0;n<a;++n)(r=C(this,o,t,e,i[n].identifier,i[n]))&&((0,s.GK)(t),r("start",t,i[n]))}}function N(t){var e,n,r=t.changedTouches,i=r.length;for(e=0;e<i;++e)(n=g[r[e].identifier])&&((0,s.Ay)(t),n("drag",t,r[e]))}function T(t){var e,n,r=t.changedTouches,i=r.length;for(d&&clearTimeout(d),d=setTimeout(function(){d=null},500),e=0;e<i;++e)(n=g[r[e].identifier])&&((0,s.GK)(t),n("end",t,r[e]))}function C(t,e,n,r,i,a){var s,u,c,h=w.copy(),f=(0,o.A)(a||n,e);if(null!=(c=y.call(t,new l("beforestart",{sourceEvent:n,target:A,identifier:i,active:x,x:f[0],y:f[1],dx:0,dy:0,dispatch:h}),r)))return s=c.x-f[0]||0,u=c.y-f[1]||0,function n(a,p,d){var v,m=f;switch(a){case"start":g[i]=n,v=x++;break;case"end":delete g[i],--x;case"drag":f=(0,o.A)(d||p,e),v=x}h.call(a,t,new l(a,{sourceEvent:p,subject:c,target:A,identifier:i,active:v,x:f[0]+s,y:f[1]+u,dx:f[0]-m[0],dy:f[1]-m[1],dispatch:h}),r)}}return A.filter=function(t){return arguments.length?(v="function"==typeof t?t:u(!!t),A):v},A.container=function(t){return arguments.length?(m="function"==typeof t?t:u(t),A):m},A.subject=function(t){return arguments.length?(y="function"==typeof t?t:u(t),A):y},A.touchable=function(t){return arguments.length?(_="function"==typeof t?t:u(!!t),A):_},A.on=function(){var t=w.on.apply(w,arguments);return t===w?A:t},A.clickDistance=function(t){return arguments.length?(b=(t*=1)*t,A):Math.sqrt(b)},A}l.prototype.on=function(){var t=this._.on.apply(this._,arguments);return t===this._?this:t}},43206:(t,e,n)=>{"use strict";function r(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}n.d(e,{A:()=>r})},44150:(t,e,n)=>{"use strict";n.d(e,{A:()=>i,g:()=>r});var r="http://www.w3.org/1999/xhtml";let i={svg:"http://www.w3.org/2000/svg",xhtml:r,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"}},45240:(t,e,n)=>{"use strict";n.d(e,{s_:()=>ty,GS:()=>tu});var r,i=n(53037),o=n(25257);function a(t){return((t=Math.exp(t))+1/t)/2}let s=function t(e,n,r){function i(t,i){var o,s,u=t[0],l=t[1],c=t[2],h=i[0],f=i[1],p=i[2],d=h-u,v=f-l,m=d*d+v*v;if(m<1e-12)s=Math.log(p/c)/e,o=function(t){return[u+t*d,l+t*v,c*Math.exp(e*t*s)]};else{var y=Math.sqrt(m),_=(p*p-c*c+r*m)/(2*c*n*y),g=(p*p-c*c-r*m)/(2*p*n*y),w=Math.log(Math.sqrt(_*_+1)-_);s=(Math.log(Math.sqrt(g*g+1)-g)-w)/e,o=function(t){var r,i,o=t*s,h=a(w),f=c/(n*y)*(h*(((r=Math.exp(2*(r=e*o+w)))-1)/(r+1))-((i=Math.exp(i=w))-1/i)/2);return[u+f*d,l+f*v,c*h/a(e*o+w)]}}return o.duration=1e3*s*e/Math.SQRT2,o}return i.rho=function(e){var n=Math.max(.001,+e),r=n*n;return t(n,r,r*r)},i}(Math.SQRT2,2,4);var u,l,c=n(31530),h=n(53995),f=n(71409),p=0,d=0,v=0,m=0,y=0,_=0,g="object"==typeof performance&&performance.now?performance:Date,w="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function x(){return y||(w(b),y=g.now()+_)}function b(){y=0}function A(){this._call=this._time=this._next=null}function k(t,e,n){var r=new A;return r.restart(t,e,n),r}function E(){y=(m=g.now())+_,p=d=0;try{x(),++p;for(var t,e=u;e;)(t=y-e._time)>=0&&e._call.call(void 0,t),e=e._next;--p}finally{p=0,function(){for(var t,e,n=u,r=1/0;n;)n._call?(r>n._time&&(r=n._time),t=n,n=n._next):(e=n._next,n._next=null,n=t?t._next=e:u=e);l=t,S(r)}(),y=0}}function j(){var t=g.now(),e=t-m;e>1e3&&(_-=e,m=t)}function S(t){!p&&(d&&(d=clearTimeout(d)),t-y>24?(t<1/0&&(d=setTimeout(E,t-g.now()-_)),v&&(v=clearInterval(v))):(v||(m=g.now(),v=setInterval(j,1e3)),p=1,w(E)))}function N(t,e,n){var r=new A;return e=null==e?0:+e,r.restart(n=>{r.stop(),t(n+e)},e,n),r}A.prototype=k.prototype={constructor:A,restart:function(t,e,n){if("function"!=typeof t)throw TypeError("callback is not a function");n=(null==n?x():+n)+(null==e?0:+e),this._next||l===this||(l?l._next=this:u=this,l=this),this._call=t,this._time=n,S()},stop:function(){this._call&&(this._call=null,this._time=1/0,S())}};var T=(0,i.A)("start","end","cancel","interrupt"),C=[];function M(t,e,n,r,i,o){var a=t.__transition;if(a){if(n in a)return}else t.__transition={};!function(t,e,n){var r,i=t.__transition;function o(u){var l,c,h,f;if(1!==n.state)return s();for(l in i)if((f=i[l]).name===n.name){if(3===f.state)return N(o);4===f.state?(f.state=6,f.timer.stop(),f.on.call("interrupt",t,t.__data__,f.index,f.group),delete i[l]):+l<e&&(f.state=6,f.timer.stop(),f.on.call("cancel",t,t.__data__,f.index,f.group),delete i[l])}if(N(function(){3===n.state&&(n.state=4,n.timer.restart(a,n.delay,n.time),a(u))}),n.state=2,n.on.call("start",t,t.__data__,n.index,n.group),2===n.state){for(l=0,n.state=3,r=Array(h=n.tween.length),c=-1;l<h;++l)(f=n.tween[l].value.call(t,t.__data__,n.index,n.group))&&(r[++c]=f);r.length=c+1}}function a(e){for(var i=e<n.duration?n.ease.call(null,e/n.duration):(n.timer.restart(s),n.state=5,1),o=-1,a=r.length;++o<a;)r[o].call(t,i);5===n.state&&(n.on.call("end",t,t.__data__,n.index,n.group),s())}function s(){for(var r in n.state=6,n.timer.stop(),delete i[e],i)return;delete t.__transition}i[e]=n,n.timer=k(function(t){n.state=1,n.timer.restart(o,n.delay,n.time),n.delay<=t&&o(t-n.delay)},0,n.time)}(t,n,{name:e,index:r,group:i,on:T,tween:C,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:0})}function z(t,e){var n=R(t,e);if(n.state>0)throw Error("too late; already scheduled");return n}function I(t,e){var n=R(t,e);if(n.state>3)throw Error("too late; already running");return n}function R(t,e){var n=t.__transition;if(!n||!(n=n[e]))throw Error("transition not found");return n}function B(t,e){var n,r,i,o=t.__transition,a=!0;if(o){for(i in e=null==e?null:e+"",o){if((n=o[i]).name!==e){a=!1;continue}r=n.state>2&&n.state<5,n.state=6,n.timer.stop(),n.on.call(r?"interrupt":"cancel",t,t.__data__,n.index,n.group),delete o[i]}a&&delete t.__transition}}var O=n(60622),P=180/Math.PI,D={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function L(t,e,n,r,i,o){var a,s,u;return(a=Math.sqrt(t*t+e*e))&&(t/=a,e/=a),(u=t*n+e*r)&&(n-=t*u,r-=e*u),(s=Math.sqrt(n*n+r*r))&&(n/=s,r/=s,u/=s),t*r<e*n&&(t=-t,e=-e,u=-u,a=-a),{translateX:i,translateY:o,rotate:Math.atan2(e,t)*P,skewX:Math.atan(u)*P,scaleX:a,scaleY:s}}function X(t,e,n,r){function i(t){return t.length?t.pop()+" ":""}return function(o,a){var s,u,l,c,h=[],f=[];return o=t(o),a=t(a),!function(t,r,i,o,a,s){if(t!==i||r!==o){var u=a.push("translate(",null,e,null,n);s.push({i:u-4,x:(0,O.A)(t,i)},{i:u-2,x:(0,O.A)(r,o)})}else(i||o)&&a.push("translate("+i+e+o+n)}(o.translateX,o.translateY,a.translateX,a.translateY,h,f),s=o.rotate,u=a.rotate,s!==u?(s-u>180?u+=360:u-s>180&&(s+=360),f.push({i:h.push(i(h)+"rotate(",null,r)-2,x:(0,O.A)(s,u)})):u&&h.push(i(h)+"rotate("+u+r),l=o.skewX,c=a.skewX,l!==c?f.push({i:h.push(i(h)+"skewX(",null,r)-2,x:(0,O.A)(l,c)}):c&&h.push(i(h)+"skewX("+c+r),!function(t,e,n,r,o,a){if(t!==n||e!==r){var s=o.push(i(o)+"scale(",null,",",null,")");a.push({i:s-4,x:(0,O.A)(t,n)},{i:s-2,x:(0,O.A)(e,r)})}else(1!==n||1!==r)&&o.push(i(o)+"scale("+n+","+r+")")}(o.scaleX,o.scaleY,a.scaleX,a.scaleY,h,f),o=a=null,function(t){for(var e,n=-1,r=f.length;++n<r;)h[(e=f[n]).i]=e.x(t);return h.join("")}}}var q=X(function(t){let e=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return e.isIdentity?D:L(e.a,e.b,e.c,e.d,e.e,e.f)},"px, ","px)","deg)"),Y=X(function(t){return null==t?D:(r||(r=document.createElementNS("http://www.w3.org/2000/svg","g")),r.setAttribute("transform",t),t=r.transform.baseVal.consolidate())?L((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):D},", ",")",")"),V=n(67649);function $(t,e,n){var r=t._id;return t.each(function(){var t=I(this,r);(t.value||(t.value={}))[e]=n.apply(this,arguments)}),function(t){return R(t,r).value[e]}}var G=n(62191),H=n(83576),K=n(48406);function U(t,e){var n;return("number"==typeof e?O.A:e instanceof G.Ay?H.Ay:(n=(0,G.Ay)(e))?(e=n,H.Ay):K.A)(t,e)}var F=n(28276),W=n(39767),Q=n(47942),J=f.Ay.prototype.constructor,Z=n(40318);function tt(t){return function(){this.style.removeProperty(t)}}var te=0;function tn(t,e,n,r){this._groups=t,this._parents=e,this._name=n,this._id=r}var tr=f.Ay.prototype;tn.prototype=(function(t){return(0,f.Ay)().transition(t)}).prototype={constructor:tn,select:function(t){var e=this._name,n=this._id;"function"!=typeof t&&(t=(0,W.A)(t));for(var r=this._groups,i=r.length,o=Array(i),a=0;a<i;++a)for(var s,u,l=r[a],c=l.length,h=o[a]=Array(c),f=0;f<c;++f)(s=l[f])&&(u=t.call(s,s.__data__,f,l))&&("__data__"in s&&(u.__data__=s.__data__),h[f]=u,M(h[f],e,n,f,h,R(s,n)));return new tn(o,this._parents,e,n)},selectAll:function(t){var e=this._name,n=this._id;"function"!=typeof t&&(t=(0,Q.A)(t));for(var r=this._groups,i=r.length,o=[],a=[],s=0;s<i;++s)for(var u,l=r[s],c=l.length,h=0;h<c;++h)if(u=l[h]){for(var f,p=t.call(u,u.__data__,h,l),d=R(u,n),v=0,m=p.length;v<m;++v)(f=p[v])&&M(f,e,n,v,p,d);o.push(p),a.push(u)}return new tn(o,a,e,n)},selectChild:tr.selectChild,selectChildren:tr.selectChildren,filter:function(t){"function"!=typeof t&&(t=(0,F.A)(t));for(var e=this._groups,n=e.length,r=Array(n),i=0;i<n;++i)for(var o,a=e[i],s=a.length,u=r[i]=[],l=0;l<s;++l)(o=a[l])&&t.call(o,o.__data__,l,a)&&u.push(o);return new tn(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw Error();for(var e=this._groups,n=t._groups,r=e.length,i=n.length,o=Math.min(r,i),a=Array(r),s=0;s<o;++s)for(var u,l=e[s],c=n[s],h=l.length,f=a[s]=Array(h),p=0;p<h;++p)(u=l[p]||c[p])&&(f[p]=u);for(;s<r;++s)a[s]=e[s];return new tn(a,this._parents,this._name,this._id)},selection:function(){return new J(this._groups,this._parents)},transition:function(){for(var t=this._name,e=this._id,n=++te,r=this._groups,i=r.length,o=0;o<i;++o)for(var a,s=r[o],u=s.length,l=0;l<u;++l)if(a=s[l]){var c=R(a,e);M(a,t,n,l,s,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new tn(r,this._parents,t,n)},call:tr.call,nodes:tr.nodes,node:tr.node,size:tr.size,empty:tr.empty,each:tr.each,on:function(t,e){var n,r,i,o,a,s,u=this._id;return arguments.length<2?R(this.node(),u).on.on(t):this.each((n=u,r=t,i=e,s=(r+"").trim().split(/^|\s+/).every(function(t){var e=t.indexOf(".");return e>=0&&(t=t.slice(0,e)),!t||"start"===t})?z:I,function(){var t=s(this,n),e=t.on;e!==o&&(a=(o=e).copy()).on(r,i),t.on=a}))},attr:function(t,e){var n=(0,V.A)(t),r="transform"===n?Y:U;return this.attrTween(t,"function"==typeof e?(n.local?function(t,e,n){var r,i,o;return function(){var a,s,u=n(this);return null==u?void this.removeAttributeNS(t.space,t.local):(a=this.getAttributeNS(t.space,t.local))===(s=u+"")?null:a===r&&s===i?o:(i=s,o=e(r=a,u))}}:function(t,e,n){var r,i,o;return function(){var a,s,u=n(this);return null==u?void this.removeAttribute(t):(a=this.getAttribute(t))===(s=u+"")?null:a===r&&s===i?o:(i=s,o=e(r=a,u))}})(n,r,$(this,"attr."+t,e)):null==e?(n.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}})(n):(n.local?function(t,e,n){var r,i,o=n+"";return function(){var a=this.getAttributeNS(t.space,t.local);return a===o?null:a===r?i:i=e(r=a,n)}}:function(t,e,n){var r,i,o=n+"";return function(){var a=this.getAttribute(t);return a===o?null:a===r?i:i=e(r=a,n)}})(n,r,e))},attrTween:function(t,e){var n="attr."+t;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==e)return this.tween(n,null);if("function"!=typeof e)throw Error();var r=(0,V.A)(t);return this.tween(n,(r.local?function(t,e){var n,r;function i(){var i=e.apply(this,arguments);return i!==r&&(n=(r=i)&&function(e){this.setAttributeNS(t.space,t.local,i.call(this,e))}),n}return i._value=e,i}:function(t,e){var n,r;function i(){var i=e.apply(this,arguments);return i!==r&&(n=(r=i)&&function(e){this.setAttribute(t,i.call(this,e))}),n}return i._value=e,i})(r,e))},style:function(t,e,n){var r,i,o,a,s,u,l,c,h,f,p,d,v,m,y,_,g,w,x,b,A,k="transform"==(t+="")?q:U;return null==e?this.styleTween(t,(r=t,function(){var t=(0,Z.j)(this,r),e=(this.style.removeProperty(r),(0,Z.j)(this,r));return t===e?null:t===i&&e===o?a:a=k(i=t,o=e)})).on("end.style."+t,tt(t)):"function"==typeof e?this.styleTween(t,(s=t,u=$(this,"style."+t,e),function(){var t=(0,Z.j)(this,s),e=u(this),n=e+"";return null==e&&(this.style.removeProperty(s),n=e=(0,Z.j)(this,s)),t===n?null:t===l&&n===c?h:(c=n,h=k(l=t,e))})).each((f=this._id,g="end."+(_="style."+(p=t)),function(){var t=I(this,f),e=t.on,n=null==t.value[_]?y||(y=tt(p)):void 0;(e!==d||m!==n)&&(v=(d=e).copy()).on(g,m=n),t.on=v})):this.styleTween(t,(w=t,A=e+"",function(){var t=(0,Z.j)(this,w);return t===A?null:t===x?b:b=k(x=t,e)}),n).on("end.style."+t,null)},styleTween:function(t,e,n){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==e)return this.tween(r,null);if("function"!=typeof e)throw Error();return this.tween(r,function(t,e,n){var r,i;function o(){var o=e.apply(this,arguments);return o!==i&&(r=(i=o)&&function(e){this.style.setProperty(t,o.call(this,e),n)}),r}return o._value=e,o}(t,e,null==n?"":n))},text:function(t){var e,n;return this.tween("text","function"==typeof t?(e=$(this,"text",t),function(){var t=e(this);this.textContent=null==t?"":t}):(n=null==t?"":t+"",function(){this.textContent=n}))},textTween:function(t){var e="text";if(arguments.length<1)return(e=this.tween(e))&&e._value;if(null==t)return this.tween(e,null);if("function"!=typeof t)throw Error();return this.tween(e,function(t){var e,n;function r(){var r=t.apply(this,arguments);return r!==n&&(e=(n=r)&&function(t){this.textContent=r.call(this,t)}),e}return r._value=t,r}(t))},remove:function(){var t;return this.on("end.remove",(t=this._id,function(){var e=this.parentNode;for(var n in this.__transition)if(+n!==t)return;e&&e.removeChild(this)}))},tween:function(t,e){var n=this._id;if(t+="",arguments.length<2){for(var r,i=R(this.node(),n).tween,o=0,a=i.length;o<a;++o)if((r=i[o]).name===t)return r.value;return null}return this.each((null==e?function(t,e){var n,r;return function(){var i=I(this,t),o=i.tween;if(o!==n){r=n=o;for(var a=0,s=r.length;a<s;++a)if(r[a].name===e){(r=r.slice()).splice(a,1);break}}i.tween=r}}:function(t,e,n){var r,i;if("function"!=typeof n)throw Error();return function(){var o=I(this,t),a=o.tween;if(a!==r){i=(r=a).slice();for(var s={name:e,value:n},u=0,l=i.length;u<l;++u)if(i[u].name===e){i[u]=s;break}u===l&&i.push(s)}o.tween=i}})(n,t,e))},delay:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?function(t,e){return function(){z(this,t).delay=+e.apply(this,arguments)}}:function(t,e){return e*=1,function(){z(this,t).delay=e}})(e,t)):R(this.node(),e).delay},duration:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?function(t,e){return function(){I(this,t).duration=+e.apply(this,arguments)}}:function(t,e){return e*=1,function(){I(this,t).duration=e}})(e,t)):R(this.node(),e).duration},ease:function(t){var e=this._id;return arguments.length?this.each(function(t,e){if("function"!=typeof e)throw Error();return function(){I(this,t).ease=e}}(e,t)):R(this.node(),e).ease},easeVarying:function(t){var e;if("function"!=typeof t)throw Error();return this.each((e=this._id,function(){var n=t.apply(this,arguments);if("function"!=typeof n)throw Error();I(this,e).ease=n}))},end:function(){var t,e,n=this,r=n._id,i=n.size();return new Promise(function(o,a){var s={value:a},u={value:function(){0==--i&&o()}};n.each(function(){var n=I(this,r),i=n.on;i!==t&&((e=(t=i).copy())._.cancel.push(s),e._.interrupt.push(s),e._.end.push(u)),n.on=e}),0===i&&o()})},[Symbol.iterator]:tr[Symbol.iterator]};var ti={time:null,delay:0,duration:250,ease:function(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}};f.Ay.prototype.interrupt=function(t){return this.each(function(){B(this,t)})},f.Ay.prototype.transition=function(t){var e,n;t instanceof tn?(e=t._id,t=t._name):(e=++te,(n=ti).time=x(),t=null==t?null:t+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var a,s=r[o],u=s.length,l=0;l<u;++l)(a=s[l])&&M(a,t,e,l,s,n||function(t,e){for(var n;!(n=t.__transition)||!(n=n[e]);)if(!(t=t.parentNode))throw Error(`transition ${e} not found`);return n}(a,e));return new tn(r,this._parents,t,e)};let to=t=>()=>t;function ta(t,{sourceEvent:e,target:n,transform:r,dispatch:i}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:e,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:i}})}function ts(t,e,n){this.k=t,this.x=e,this.y=n}ts.prototype={constructor:ts,scale:function(t){return 1===t?this:new ts(this.k*t,this.x,this.y)},translate:function(t,e){return 0===t&0===e?this:new ts(this.k,this.x+this.k*t,this.y+this.k*e)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var tu=new ts(1,0,0);function tl(t){t.stopImmediatePropagation()}function tc(t){t.preventDefault(),t.stopImmediatePropagation()}function th(t){return(!t.ctrlKey||"wheel"===t.type)&&!t.button}function tf(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t).hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]:[[0,0],[t.clientWidth,t.clientHeight]]}function tp(){return this.__zoom||tu}function td(t){return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function tv(){return navigator.maxTouchPoints||"ontouchstart"in this}function tm(t,e,n){var r=t.invertX(e[0][0])-n[0][0],i=t.invertX(e[1][0])-n[1][0],o=t.invertY(e[0][1])-n[0][1],a=t.invertY(e[1][1])-n[1][1];return t.translate(i>r?(r+i)/2:Math.min(0,r)||Math.max(0,i),a>o?(o+a)/2:Math.min(0,o)||Math.max(0,a))}function ty(){var t,e,n,r=th,a=tf,u=tm,l=td,f=tv,p=[0,1/0],d=[[-1/0,-1/0],[1/0,1/0]],v=250,m=s,y=(0,i.A)("start","zoom","end"),_=0,g=10;function w(t){t.property("__zoom",tp).on("wheel.zoom",S,{passive:!1}).on("mousedown.zoom",N).on("dblclick.zoom",T).filter(f).on("touchstart.zoom",C).on("touchmove.zoom",M).on("touchend.zoom touchcancel.zoom",z).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function x(t,e){return(e=Math.max(p[0],Math.min(p[1],e)))===t.k?t:new ts(e,t.x,t.y)}function b(t,e,n){var r=e[0]-n[0]*t.k,i=e[1]-n[1]*t.k;return r===t.x&&i===t.y?t:new ts(t.k,r,i)}function A(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function k(t,e,n,r){t.on("start.zoom",function(){E(this,arguments).event(r).start()}).on("interrupt.zoom end.zoom",function(){E(this,arguments).event(r).end()}).tween("zoom",function(){var t=arguments,i=E(this,t).event(r),o=a.apply(this,t),s=null==n?A(o):"function"==typeof n?n.apply(this,t):n,u=Math.max(o[1][0]-o[0][0],o[1][1]-o[0][1]),l=this.__zoom,c="function"==typeof e?e.apply(this,t):e,h=m(l.invert(s).concat(u/l.k),c.invert(s).concat(u/c.k));return function(t){if(1===t)t=c;else{var e=h(t),n=u/e[2];t=new ts(n,s[0]-e[0]*n,s[1]-e[1]*n)}i.zoom(null,t)}})}function E(t,e,n){return!n&&t.__zooming||new j(t,e)}function j(t,e){this.that=t,this.args=e,this.active=0,this.sourceEvent=null,this.extent=a.apply(t,e),this.taps=0}function S(t,...e){if(r.apply(this,arguments)){var n=E(this,e).event(t),i=this.__zoom,o=Math.max(p[0],Math.min(p[1],i.k*Math.pow(2,l.apply(this,arguments)))),a=(0,h.A)(t);if(n.wheel)(n.mouse[0][0]!==a[0]||n.mouse[0][1]!==a[1])&&(n.mouse[1]=i.invert(n.mouse[0]=a)),clearTimeout(n.wheel);else{if(i.k===o)return;n.mouse=[a,i.invert(a)],B(this),n.start()}tc(t),n.wheel=setTimeout(function(){n.wheel=null,n.end()},150),n.zoom("mouse",u(b(x(i,o),n.mouse[0],n.mouse[1]),n.extent,d))}}function N(t,...e){if(!n&&r.apply(this,arguments)){var i=t.currentTarget,a=E(this,e,!0).event(t),s=(0,c.A)(t.view).on("mousemove.zoom",function(t){if(tc(t),!a.moved){var e=t.clientX-f,n=t.clientY-p;a.moved=e*e+n*n>_}a.event(t).zoom("mouse",u(b(a.that.__zoom,a.mouse[0]=(0,h.A)(t,i),a.mouse[1]),a.extent,d))},!0).on("mouseup.zoom",function(t){s.on("mousemove.zoom mouseup.zoom",null),(0,o.y)(t.view,a.moved),tc(t),a.event(t).end()},!0),l=(0,h.A)(t,i),f=t.clientX,p=t.clientY;(0,o.A)(t.view),tl(t),a.mouse=[l,this.__zoom.invert(l)],B(this),a.start()}}function T(t,...e){if(r.apply(this,arguments)){var n=this.__zoom,i=(0,h.A)(t.changedTouches?t.changedTouches[0]:t,this),o=n.invert(i),s=n.k*(t.shiftKey?.5:2),l=u(b(x(n,s),i,o),a.apply(this,e),d);tc(t),v>0?(0,c.A)(this).transition().duration(v).call(k,l,i,t):(0,c.A)(this).call(w.transform,l,i,t)}}function C(n,...i){if(r.apply(this,arguments)){var o,a,s,u,l=n.touches,c=l.length,f=E(this,i,n.changedTouches.length===c).event(n);for(tl(n),a=0;a<c;++a)s=l[a],u=[u=(0,h.A)(s,this),this.__zoom.invert(u),s.identifier],f.touch0?f.touch1||f.touch0[2]===u[2]||(f.touch1=u,f.taps=0):(f.touch0=u,o=!0,f.taps=1+!!t);t&&(t=clearTimeout(t)),o&&(f.taps<2&&(e=u[0],t=setTimeout(function(){t=null},500)),B(this),f.start())}}function M(t,...e){if(this.__zooming){var n,r,i,o,a=E(this,e).event(t),s=t.changedTouches,l=s.length;for(tc(t),n=0;n<l;++n)r=s[n],i=(0,h.A)(r,this),a.touch0&&a.touch0[2]===r.identifier?a.touch0[0]=i:a.touch1&&a.touch1[2]===r.identifier&&(a.touch1[0]=i);if(r=a.that.__zoom,a.touch1){var c=a.touch0[0],f=a.touch0[1],p=a.touch1[0],v=a.touch1[1],m=(m=p[0]-c[0])*m+(m=p[1]-c[1])*m,y=(y=v[0]-f[0])*y+(y=v[1]-f[1])*y;r=x(r,Math.sqrt(m/y)),i=[(c[0]+p[0])/2,(c[1]+p[1])/2],o=[(f[0]+v[0])/2,(f[1]+v[1])/2]}else{if(!a.touch0)return;i=a.touch0[0],o=a.touch0[1]}a.zoom("touch",u(b(r,i,o),a.extent,d))}}function z(t,...r){if(this.__zooming){var i,o,a=E(this,r).event(t),s=t.changedTouches,u=s.length;for(tl(t),n&&clearTimeout(n),n=setTimeout(function(){n=null},500),i=0;i<u;++i)o=s[i],a.touch0&&a.touch0[2]===o.identifier?delete a.touch0:a.touch1&&a.touch1[2]===o.identifier&&delete a.touch1;if(a.touch1&&!a.touch0&&(a.touch0=a.touch1,delete a.touch1),a.touch0)a.touch0[1]=this.__zoom.invert(a.touch0[0]);else if(a.end(),2===a.taps&&(o=(0,h.A)(o,this),Math.hypot(e[0]-o[0],e[1]-o[1])<g)){var l=(0,c.A)(this).on("dblclick.zoom");l&&l.apply(this,arguments)}}}return w.transform=function(t,e,n,r){var i=t.selection?t.selection():t;i.property("__zoom",tp),t!==i?k(t,e,n,r):i.interrupt().each(function(){E(this,arguments).event(r).start().zoom(null,"function"==typeof e?e.apply(this,arguments):e).end()})},w.scaleBy=function(t,e,n,r){w.scaleTo(t,function(){var t=this.__zoom.k,n="function"==typeof e?e.apply(this,arguments):e;return t*n},n,r)},w.scaleTo=function(t,e,n,r){w.transform(t,function(){var t=a.apply(this,arguments),r=this.__zoom,i=null==n?A(t):"function"==typeof n?n.apply(this,arguments):n,o=r.invert(i),s="function"==typeof e?e.apply(this,arguments):e;return u(b(x(r,s),i,o),t,d)},n,r)},w.translateBy=function(t,e,n,r){w.transform(t,function(){return u(this.__zoom.translate("function"==typeof e?e.apply(this,arguments):e,"function"==typeof n?n.apply(this,arguments):n),a.apply(this,arguments),d)},null,r)},w.translateTo=function(t,e,n,r,i){w.transform(t,function(){var t=a.apply(this,arguments),i=this.__zoom,o=null==r?A(t):"function"==typeof r?r.apply(this,arguments):r;return u(tu.translate(o[0],o[1]).scale(i.k).translate("function"==typeof e?-e.apply(this,arguments):-e,"function"==typeof n?-n.apply(this,arguments):-n),t,d)},r,i)},j.prototype={event:function(t){return t&&(this.sourceEvent=t),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(t,e){return this.mouse&&"mouse"!==t&&(this.mouse[1]=e.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=e.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=e.invert(this.touch1[0])),this.that.__zoom=e,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(t){var e=(0,c.A)(this.that).datum();y.call(t,this.that,new ta(t,{sourceEvent:this.sourceEvent,target:w,type:t,transform:this.that.__zoom,dispatch:y}),e)}},w.wheelDelta=function(t){return arguments.length?(l="function"==typeof t?t:to(+t),w):l},w.filter=function(t){return arguments.length?(r="function"==typeof t?t:to(!!t),w):r},w.touchable=function(t){return arguments.length?(f="function"==typeof t?t:to(!!t),w):f},w.extent=function(t){return arguments.length?(a="function"==typeof t?t:to([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),w):a},w.scaleExtent=function(t){return arguments.length?(p[0]=+t[0],p[1]=+t[1],w):[p[0],p[1]]},w.translateExtent=function(t){return arguments.length?(d[0][0]=+t[0][0],d[1][0]=+t[1][0],d[0][1]=+t[0][1],d[1][1]=+t[1][1],w):[[d[0][0],d[0][1]],[d[1][0],d[1][1]]]},w.constrain=function(t){return arguments.length?(u=t,w):u},w.duration=function(t){return arguments.length?(v=+t,w):v},w.interpolate=function(t){return arguments.length?(m=t,w):m},w.on=function(){var t=y.on.apply(y,arguments);return t===y?w:t},w.clickDistance=function(t){return arguments.length?(_=(t*=1)*t,w):Math.sqrt(_)},w.tapDistance=function(t){return arguments.length?(g=+t,w):g},w}ts.prototype},47402:(t,e,n)=>{"use strict";n.d(e,{o:()=>c});var r=n(94513),i=n(55100),o=n(22697),a=n(91047),s=n(31637),u=n(2923),l=n(33225);let c=(0,u.R)(function(t,e){let n=(0,a.e)(),u=(0,s.Vh)({...t,ref:e}),c=(0,i.H2)({outline:"0",display:"flex",alignItems:"center",justifyContent:"center",...n.tab});return(0,r.jsx)(l.B.button,{...u,className:(0,o.cx)("chakra-tabs__tab",t.className),__css:c})});c.displayName="Tab"},47942:(t,e,n)=>{"use strict";function r(){return[]}function i(t){return null==t?r:function(){return this.querySelectorAll(t)}}n.d(e,{A:()=>i})},53037:(t,e,n)=>{"use strict";n.d(e,{A:()=>s});var r={value:()=>{}};function i(){for(var t,e=0,n=arguments.length,r={};e<n;++e){if(!(t=arguments[e]+"")||t in r||/[\s.]/.test(t))throw Error("illegal type: "+t);r[t]=[]}return new o(r)}function o(t){this._=t}function a(t,e,n){for(var i=0,o=t.length;i<o;++i)if(t[i].name===e){t[i]=r,t=t.slice(0,i).concat(t.slice(i+1));break}return null!=n&&t.push({name:e,value:n}),t}o.prototype=i.prototype={constructor:o,on:function(t,e){var n,r=this._,i=(t+"").trim().split(/^|\s+/).map(function(t){var e="",n=t.indexOf(".");if(n>=0&&(e=t.slice(n+1),t=t.slice(0,n)),t&&!r.hasOwnProperty(t))throw Error("unknown type: "+t);return{type:t,name:e}}),o=-1,s=i.length;if(arguments.length<2){for(;++o<s;)if((n=(t=i[o]).type)&&(n=function(t,e){for(var n,r=0,i=t.length;r<i;++r)if((n=t[r]).name===e)return n.value}(r[n],t.name)))return n;return}if(null!=e&&"function"!=typeof e)throw Error("invalid callback: "+e);for(;++o<s;)if(n=(t=i[o]).type)r[n]=a(r[n],t.name,e);else if(null==e)for(n in r)r[n]=a(r[n],t.name,null);return this},copy:function(){var t={},e=this._;for(var n in e)t[n]=e[n].slice();return new o(t)},call:function(t,e){if((n=arguments.length-2)>0)for(var n,r,i=Array(n),o=0;o<n;++o)i[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(r=this._[t],o=0,n=r.length;o<n;++o)r[o].value.apply(e,i)},apply:function(t,e,n){if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(e,n)}};let s=i},53995:(t,e,n)=>{"use strict";function r(t,e){if(t=function(t){let e;for(;e=t.sourceEvent;)t=e;return t}(t),void 0===e&&(e=t.currentTarget),e){var n=e.ownerSVGElement||e;if(n.createSVGPoint){var r=n.createSVGPoint();return r.x=t.clientX,r.y=t.clientY,[(r=r.matrixTransform(e.getScreenCTM().inverse())).x,r.y]}if(e.getBoundingClientRect){var i=e.getBoundingClientRect();return[t.clientX-i.left-e.clientLeft,t.clientY-i.top-e.clientTop]}}return[t.pageX,t.pageY]}n.d(e,{A:()=>r})},54338:(t,e,n)=>{"use strict";function r(t,e){let n={},r={};for(let[i,o]of Object.entries(t))e.includes(i)?n[i]=o:r[i]=o;return[n,r]}n.d(e,{l:()=>r})},57691:(t,e,n)=>{"use strict";n.d(e,{h:()=>f,n:()=>c});var r=n(94285),i=n(79007);let o=t=>{let e,n=new Set,r=(t,r)=>{let i="function"==typeof t?t(e):t;if(!Object.is(i,e)){let t=e;e=(null!=r?r:"object"!=typeof i||null===i)?i:Object.assign({},e,i),n.forEach(n=>n(e,t))}},i=()=>e,o={setState:r,getState:i,getInitialState:()=>a,subscribe:t=>(n.add(t),()=>n.delete(t)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},a=e=t(r,i,o);return o},a=t=>t?o(t):o,{useDebugValue:s}=r,{useSyncExternalStoreWithSelector:u}=i,l=t=>t;function c(t,e=l,n){let r=u(t.subscribe,t.getState,t.getServerState||t.getInitialState,e,n);return s(r),r}let h=(t,e)=>{let n=a(t),r=(t,r=e)=>c(n,t,r);return Object.assign(r,n),r},f=(t,e)=>t?h(t,e):h},65965:(t,e,n)=>{"use strict";n.d(e,{TV:()=>v,d1:()=>p,vw:()=>f});var r=n(94513),i=n(75387),o=n(29035),a=n(49217),s=n(2923),u=n(56915),l=n(33225);let[c,h]=(0,o.q)({name:"TagStylesContext",errorMessage:"useTagStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Tag />\" "}),f=(0,s.R)((t,e)=>{let n=(0,u.o)("Tag",t),o=(0,i.M)(t),a={display:"inline-flex",verticalAlign:"top",alignItems:"center",maxWidth:"100%",...n.container};return(0,r.jsx)(c,{value:n,children:(0,r.jsx)(l.B.span,{ref:e,...o,__css:a})})});f.displayName="Tag";let p=(0,s.R)((t,e)=>{let n=h();return(0,r.jsx)(l.B.span,{ref:e,noOfLines:1,...t,__css:n.label})});p.displayName="TagLabel",(0,s.R)((t,e)=>(0,r.jsx)(a.I,{ref:e,verticalAlign:"top",marginEnd:"0.5rem",...t})).displayName="TagLeftIcon",(0,s.R)((t,e)=>(0,r.jsx)(a.I,{ref:e,verticalAlign:"top",marginStart:"0.5rem",...t})).displayName="TagRightIcon";let d=t=>(0,r.jsx)(a.I,{verticalAlign:"inherit",viewBox:"0 0 512 512",...t,children:(0,r.jsx)("path",{fill:"currentColor",d:"M289.94 256l95-95A24 24 0 00351 127l-95 95-95-95a24 24 0 00-34 34l95 95-95 95a24 24 0 1034 34l95-95 95 95a24 24 0 0034-34z"})});d.displayName="TagCloseIcon";let v=(0,s.R)((t,e)=>{let{isDisabled:n,children:i,...o}=t,a={display:"flex",alignItems:"center",justifyContent:"center",outline:"0",...h().closeButton};return(0,r.jsx)(l.B.button,{ref:e,"aria-label":"close",...o,type:"button",disabled:n,__css:a,children:i||(0,r.jsx)(d,{})})});v.displayName="TagCloseButton"},67649:(t,e,n)=>{"use strict";n.d(e,{A:()=>i});var r=n(44150);function i(t){var e=t+="",n=e.indexOf(":");return n>=0&&"xmlns"!==(e=t.slice(0,n))&&(t=t.slice(n+1)),r.A.hasOwnProperty(e)?{space:r.A[e],local:t}:t}},70423:(t,e,n)=>{"use strict";n.d(e,{L:()=>i,a:()=>r});let[r,i]=(0,n(29035).q)({name:"CheckboxGroupContext",strict:!1})},70544:(t,e,n)=>{"use strict";n.d(e,{V:()=>d});var r,i=n(94285),o=n(30662),a=n(69792),s=n(88671);function u({color:t,dimensions:e,lineWidth:n}){return i.createElement("path",{stroke:t,strokeWidth:n,d:`M${e[0]/2} 0 V${e[1]} M0 ${e[1]/2} H${e[0]}`})}function l({color:t,radius:e}){return i.createElement("circle",{cx:e,cy:e,r:e,fill:t})}!function(t){t.Lines="lines",t.Dots="dots",t.Cross="cross"}(r||(r={}));let c={[r.Dots]:"#91919a",[r.Lines]:"#eee",[r.Cross]:"#e2e2e2"},h={[r.Dots]:1,[r.Lines]:1,[r.Cross]:6},f=t=>({transform:t.transform,patternId:`pattern-${t.rfId}`});function p({id:t,variant:e=r.Dots,gap:n=20,size:p,lineWidth:d=1,offset:v=2,color:m,style:y,className:_}){let g=(0,i.useRef)(null),{transform:w,patternId:x}=(0,a.Pj)(f,s.x),b=m||c[e],A=p||h[e],k=e===r.Dots,E=e===r.Cross,j=Array.isArray(n)?n:[n,n],S=[j[0]*w[2]||1,j[1]*w[2]||1],N=A*w[2],T=E?[N,N]:S,C=k?[N/v,N/v]:[T[0]/v,T[1]/v];return i.createElement("svg",{className:(0,o.A)(["react-flow__background",_]),style:{...y,position:"absolute",width:"100%",height:"100%",top:0,left:0},ref:g,"data-testid":"rf__background"},i.createElement("pattern",{id:x+t,x:w[0]%S[0],y:w[1]%S[1],width:S[0],height:S[1],patternUnits:"userSpaceOnUse",patternTransform:`translate(-${C[0]},-${C[1]})`},k?i.createElement(l,{color:b,radius:N/v}):i.createElement(u,{dimensions:T,color:b,lineWidth:d})),i.createElement("rect",{x:"0",y:"0",width:"100%",height:"100%",fill:`url(#${x+t})`}))}p.displayName="Background";var d=(0,i.memo)(p)},71409:(t,e,n)=>{"use strict";n.d(e,{LN:()=>D,Ay:()=>X,zr:()=>P});var r=n(39767),i=n(47942),o=n(28276),a=Array.prototype.find;function s(){return this.firstElementChild}var u=Array.prototype.filter;function l(){return Array.from(this.children)}function c(t){return Array(t.length)}function h(t,e){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=e}function f(t,e,n,r,i,o){for(var a,s=0,u=e.length,l=o.length;s<l;++s)(a=e[s])?(a.__data__=o[s],r[s]=a):n[s]=new h(t,o[s]);for(;s<u;++s)(a=e[s])&&(i[s]=a)}function p(t,e,n,r,i,o,a){var s,u,l,c=new Map,f=e.length,p=o.length,d=Array(f);for(s=0;s<f;++s)(u=e[s])&&(d[s]=l=a.call(u,u.__data__,s,e)+"",c.has(l)?i[s]=u:c.set(l,u));for(s=0;s<p;++s)l=a.call(t,o[s],s,o)+"",(u=c.get(l))?(r[s]=u,u.__data__=o[s],c.delete(l)):n[s]=new h(t,o[s]);for(s=0;s<f;++s)(u=e[s])&&c.get(d[s])===u&&(i[s]=u)}function d(t){return t.__data__}function v(t,e){return t<e?-1:t>e?1:t>=e?0:NaN}h.prototype={constructor:h,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,e){return this._parent.insertBefore(t,e)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};var m=n(67649),y=n(40318);function _(t){return t.trim().split(/^|\s+/)}function g(t){return t.classList||new w(t)}function w(t){this._node=t,this._names=_(t.getAttribute("class")||"")}function x(t,e){for(var n=g(t),r=-1,i=e.length;++r<i;)n.add(e[r])}function b(t,e){for(var n=g(t),r=-1,i=e.length;++r<i;)n.remove(e[r])}function A(){this.textContent=""}function k(){this.innerHTML=""}function E(){this.nextSibling&&this.parentNode.appendChild(this)}function j(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}w.prototype={add:function(t){0>this._names.indexOf(t)&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var e=this._names.indexOf(t);e>=0&&(this._names.splice(e,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var S=n(44150);function N(t){var e=(0,m.A)(t);return(e.local?function(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}:function(t){return function(){var e=this.ownerDocument,n=this.namespaceURI;return n===S.g&&e.documentElement.namespaceURI===S.g?e.createElement(t):e.createElementNS(n,t)}})(e)}function T(){return null}function C(){var t=this.parentNode;t&&t.removeChild(this)}function M(){var t=this.cloneNode(!1),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function z(){var t=this.cloneNode(!0),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function I(t){return function(){var e=this.__on;if(e){for(var n,r=0,i=-1,o=e.length;r<o;++r)(n=e[r],t.type&&n.type!==t.type||n.name!==t.name)?e[++i]=n:this.removeEventListener(n.type,n.listener,n.options);++i?e.length=i:delete this.__on}}}function R(t,e,n){return function(){var r,i=this.__on,o=function(t){e.call(this,t,this.__data__)};if(i){for(var a=0,s=i.length;a<s;++a)if((r=i[a]).type===t.type&&r.name===t.name){this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=o,r.options=n),r.value=e;return}}this.addEventListener(t.type,o,n),r={type:t.type,name:t.name,value:e,listener:o,options:n},i?i.push(r):this.__on=[r]}}var B=n(43206);function O(t,e,n){var r=(0,B.A)(t),i=r.CustomEvent;"function"==typeof i?i=new i(e,n):(i=r.document.createEvent("Event"),n?(i.initEvent(e,n.bubbles,n.cancelable),i.detail=n.detail):i.initEvent(e,!1,!1)),t.dispatchEvent(i)}var P=[null];function D(t,e){this._groups=t,this._parents=e}function L(){return new D([[document.documentElement]],P)}D.prototype=L.prototype={constructor:D,select:function(t){"function"!=typeof t&&(t=(0,r.A)(t));for(var e=this._groups,n=e.length,i=Array(n),o=0;o<n;++o)for(var a,s,u=e[o],l=u.length,c=i[o]=Array(l),h=0;h<l;++h)(a=u[h])&&(s=t.call(a,a.__data__,h,u))&&("__data__"in a&&(s.__data__=a.__data__),c[h]=s);return new D(i,this._parents)},selectAll:function(t){if("function"==typeof t){var e;e=t,t=function(){var t;return t=e.apply(this,arguments),null==t?[]:Array.isArray(t)?t:Array.from(t)}}else t=(0,i.A)(t);for(var n=this._groups,r=n.length,o=[],a=[],s=0;s<r;++s)for(var u,l=n[s],c=l.length,h=0;h<c;++h)(u=l[h])&&(o.push(t.call(u,u.__data__,h,l)),a.push(u));return new D(o,a)},selectChild:function(t){var e;return this.select(null==t?s:(e="function"==typeof t?t:(0,o.j)(t),function(){return a.call(this.children,e)}))},selectChildren:function(t){var e;return this.selectAll(null==t?l:(e="function"==typeof t?t:(0,o.j)(t),function(){return u.call(this.children,e)}))},filter:function(t){"function"!=typeof t&&(t=(0,o.A)(t));for(var e=this._groups,n=e.length,r=Array(n),i=0;i<n;++i)for(var a,s=e[i],u=s.length,l=r[i]=[],c=0;c<u;++c)(a=s[c])&&t.call(a,a.__data__,c,s)&&l.push(a);return new D(r,this._parents)},data:function(t,e){if(!arguments.length)return Array.from(this,d);var n=e?p:f,r=this._parents,i=this._groups;"function"!=typeof t&&(x=t,t=function(){return x});for(var o=i.length,a=Array(o),s=Array(o),u=Array(o),l=0;l<o;++l){var c=r[l],h=i[l],v=h.length,m="object"==typeof(w=t.call(c,c&&c.__data__,l,r))&&"length"in w?w:Array.from(w),y=m.length,_=s[l]=Array(y),g=a[l]=Array(y);n(c,h,_,g,u[l]=Array(v),m,e);for(var w,x,b,A,k=0,E=0;k<y;++k)if(b=_[k]){for(k>=E&&(E=k+1);!(A=g[E])&&++E<y;);b._next=A||null}}return(a=new D(a,r))._enter=s,a._exit=u,a},enter:function(){return new D(this._enter||this._groups.map(c),this._parents)},exit:function(){return new D(this._exit||this._groups.map(c),this._parents)},join:function(t,e,n){var r=this.enter(),i=this,o=this.exit();return"function"==typeof t?(r=t(r))&&(r=r.selection()):r=r.append(t+""),null!=e&&(i=e(i))&&(i=i.selection()),null==n?o.remove():n(o),r&&i?r.merge(i).order():i},merge:function(t){for(var e=t.selection?t.selection():t,n=this._groups,r=e._groups,i=n.length,o=r.length,a=Math.min(i,o),s=Array(i),u=0;u<a;++u)for(var l,c=n[u],h=r[u],f=c.length,p=s[u]=Array(f),d=0;d<f;++d)(l=c[d]||h[d])&&(p[d]=l);for(;u<i;++u)s[u]=n[u];return new D(s,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,e=-1,n=t.length;++e<n;)for(var r,i=t[e],o=i.length-1,a=i[o];--o>=0;)(r=i[o])&&(a&&4^r.compareDocumentPosition(a)&&a.parentNode.insertBefore(r,a),a=r);return this},sort:function(t){function e(e,n){return e&&n?t(e.__data__,n.__data__):!e-!n}t||(t=v);for(var n=this._groups,r=n.length,i=Array(r),o=0;o<r;++o){for(var a,s=n[o],u=s.length,l=i[o]=Array(u),c=0;c<u;++c)(a=s[c])&&(l[c]=a);l.sort(e)}return new D(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,e=0,n=t.length;e<n;++e)for(var r=t[e],i=0,o=r.length;i<o;++i){var a=r[i];if(a)return a}return null},size:function(){let t=0;for(let e of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var e=this._groups,n=0,r=e.length;n<r;++n)for(var i,o=e[n],a=0,s=o.length;a<s;++a)(i=o[a])&&t.call(i,i.__data__,a,o);return this},attr:function(t,e){var n=(0,m.A)(t);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((null==e?n.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}}:"function"==typeof e?n.local?function(t,e){return function(){var n=e.apply(this,arguments);null==n?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,n)}}:function(t,e){return function(){var n=e.apply(this,arguments);null==n?this.removeAttribute(t):this.setAttribute(t,n)}}:n.local?function(t,e){return function(){this.setAttributeNS(t.space,t.local,e)}}:function(t,e){return function(){this.setAttribute(t,e)}})(n,e))},style:y.A,property:function(t,e){return arguments.length>1?this.each((null==e?function(t){return function(){delete this[t]}}:"function"==typeof e?function(t,e){return function(){var n=e.apply(this,arguments);null==n?delete this[t]:this[t]=n}}:function(t,e){return function(){this[t]=e}})(t,e)):this.node()[t]},classed:function(t,e){var n=_(t+"");if(arguments.length<2){for(var r=g(this.node()),i=-1,o=n.length;++i<o;)if(!r.contains(n[i]))return!1;return!0}return this.each(("function"==typeof e?function(t,e){return function(){(e.apply(this,arguments)?x:b)(this,t)}}:e?function(t){return function(){x(this,t)}}:function(t){return function(){b(this,t)}})(n,e))},text:function(t){return arguments.length?this.each(null==t?A:("function"==typeof t?function(t){return function(){var e=t.apply(this,arguments);this.textContent=null==e?"":e}}:function(t){return function(){this.textContent=t}})(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?k:("function"==typeof t?function(t){return function(){var e=t.apply(this,arguments);this.innerHTML=null==e?"":e}}:function(t){return function(){this.innerHTML=t}})(t)):this.node().innerHTML},raise:function(){return this.each(E)},lower:function(){return this.each(j)},append:function(t){var e="function"==typeof t?t:N(t);return this.select(function(){return this.appendChild(e.apply(this,arguments))})},insert:function(t,e){var n="function"==typeof t?t:N(t),i=null==e?T:"function"==typeof e?e:(0,r.A)(e);return this.select(function(){return this.insertBefore(n.apply(this,arguments),i.apply(this,arguments)||null)})},remove:function(){return this.each(C)},clone:function(t){return this.select(t?z:M)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,e,n){var r,i,o=(t+"").trim().split(/^|\s+/).map(function(t){var e="",n=t.indexOf(".");return n>=0&&(e=t.slice(n+1),t=t.slice(0,n)),{type:t,name:e}}),a=o.length;if(arguments.length<2){var s=this.node().__on;if(s){for(var u,l=0,c=s.length;l<c;++l)for(r=0,u=s[l];r<a;++r)if((i=o[r]).type===u.type&&i.name===u.name)return u.value}return}for(r=0,s=e?R:I;r<a;++r)this.each(s(o[r],e,n));return this},dispatch:function(t,e){return this.each(("function"==typeof e?function(t,e){return function(){return O(this,t,e.apply(this,arguments))}}:function(t,e){return function(){return O(this,t,e)}})(t,e))},[Symbol.iterator]:function*(){for(var t=this._groups,e=0,n=t.length;e<n;++e)for(var r,i=t[e],o=0,a=i.length;o<a;++o)(r=i[o])&&(yield r)}};let X=L},72671:(t,e,n)=>{"use strict";n.d(e,{K:()=>l});var r=n(94513),i=n(22697),o=n(91047),a=n(31637),s=n(2923),u=n(33225);let l=(0,s.R)(function(t,e){let n=(0,a.Jn)({...t,ref:e}),s=(0,o.e)();return(0,r.jsx)(u.B.div,{outline:"0",...n,className:(0,i.cx)("chakra-tabs__tab-panel",t.className),__css:s.tabpanel})});l.displayName="TabPanel"},79961:(t,e,n)=>{"use strict";n.d(e,{C:()=>l});var r=n(94513),i=n(75387),o=n(22697),a=n(2923),s=n(56915),u=n(33225);let l=(0,a.R)(function(t,e){let n=(0,s.V)("Code",t),{className:a,...l}=(0,i.M)(t);return(0,r.jsx)(u.B.code,{ref:e,className:(0,o.cx)("chakra-code",t.className),...l,__css:{display:"inline-block",...n}})});l.displayName="Code"},83881:(t,e,n)=>{"use strict";n.d(e,{w:()=>c});var r=n(94513),i=n(55100),o=n(22697),a=n(91047),s=n(31637),u=n(2923),l=n(33225);let c=(0,u.R)(function(t,e){let n=(0,s.$c)({...t,ref:e}),u=(0,a.e)(),c=(0,i.H2)({display:"flex",...u.tablist});return(0,r.jsx)(l.B.div,{...n,className:(0,o.cx)("chakra-tabs__tablist",t.className),__css:c})});c.displayName="TabList"},88671:(t,e,n)=>{"use strict";function r(t,e){if(Object.is(t,e))return!0;if("object"!=typeof t||null===t||"object"!=typeof e||null===e)return!1;if(t instanceof Map&&e instanceof Map){if(t.size!==e.size)return!1;for(let[n,r]of t)if(!Object.is(r,e.get(n)))return!1;return!0}if(t instanceof Set&&e instanceof Set){if(t.size!==e.size)return!1;for(let n of t)if(!e.has(n))return!1;return!0}let n=Object.keys(t);if(n.length!==Object.keys(e).length)return!1;for(let r of n)if(!Object.prototype.hasOwnProperty.call(e,r)||!Object.is(t[r],e[r]))return!1;return!0}n.d(e,{x:()=>r})},91047:(t,e,n)=>{"use strict";n.d(e,{e:()=>p,t:()=>d});var r=n(94513),i=n(75387),o=n(29035),a=n(22697),s=n(94285),u=n(31637),l=n(2923),c=n(56915),h=n(33225);let[f,p]=(0,o.q)({name:"TabsStylesContext",errorMessage:"useTabsStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Tabs />\" "}),d=(0,l.R)(function(t,e){let n=(0,c.o)("Tabs",t),{children:o,className:l,...p}=(0,i.M)(t),{htmlProps:d,descendants:v,...m}=(0,u.uc)(p),y=(0,s.useMemo)(()=>m,[m]),{isFitted:_,...g}=d,w={position:"relative",...n.root};return(0,r.jsx)(u.at,{value:v,children:(0,r.jsx)(u.O_,{value:y,children:(0,r.jsx)(f,{value:n,children:(0,r.jsx)(h.B.div,{className:(0,a.cx)("chakra-tabs",l),ref:e,...g,__css:w,children:o})})})})});d.displayName="Tabs"},96268:(t,e,n)=>{"use strict";n.d(e,{G6:()=>h});var r=n(94513),i=n(22697),o=n(6159),a=n(33225),s=n(2923);let u={left:{marginEnd:"-1px",borderEndRadius:0,borderEndColor:"transparent"},right:{marginStart:"-1px",borderStartRadius:0,borderStartColor:"transparent"}},l=(0,a.B)("div",{baseStyle:{flex:"0 0 auto",width:"auto",display:"flex",alignItems:"center",whiteSpace:"nowrap"}}),c=(0,s.R)(function(t,e){let{placement:n="left",...i}=t,a=u[n]??{},s=(0,o.Z)();return(0,r.jsx)(l,{ref:e,...i,__css:{...s.addon,...a}})});c.displayName="InputAddon";let h=(0,s.R)(function(t,e){return(0,r.jsx)(c,{ref:e,placement:"left",...t,className:(0,i.cx)("chakra-input__left-addon",t.className)})});h.displayName="InputLeftAddon",h.id="InputLeftAddon";let f=(0,s.R)(function(t,e){return(0,r.jsx)(c,{ref:e,placement:"right",...t,className:(0,i.cx)("chakra-input__right-addon",t.className)})});f.displayName="InputRightAddon",f.id="InputRightAddon"},99820:(t,e,n)=>{"use strict";n.d(e,{T:()=>l});var r=n(94513),i=n(22697),o=n(91047),a=n(31637),s=n(2923),u=n(33225);let l=(0,s.R)(function(t,e){let n=(0,a.uo)(t),s=(0,o.e)();return(0,r.jsx)(u.B.div,{...n,width:"100%",ref:e,className:(0,i.cx)("chakra-tabs__tab-panels",t.className),__css:s.tabpanels})});l.displayName="TabPanels"}}]);