(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3659],{14691:(e,o,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/experimental/addon-builder",function(){return r(94105)}])},94105:(e,o,r)=>{"use strict";r.r(o),r.d(o,{default:()=>eB});var s=r(94513),n=r(94285),l=r(95845),t=r(22907),i=r(51961),a=r(79156),c=r(78902),d=r(73011),h=r(31678),m=r(41611),x=r(24792),u=r(62690),p=r(71601),g=r(9557),b=r(7680),j=r(52922),f=r(47847),v=r(59365),y=r(85104),C=r(26977),S=r(49451),w=r(52216),E=r(25680),T=r(40443),z=r(63730),k=r(64057),D=r(7627),R=r(19521),A=r(65965),M=r(28245),O=r(68443),W=r(20429),I=r(91169),F=r(87528),U=r(18480),N=r(65104),L=r(59818),P=r(16791),_=r(5095),B=r(97146),H=r(53424),J=r(58686),V=r(12772),q=r(69792),G=r(70544);r(24472);var K=r(83901),$=r(75697),X=r(86293),Y=r(22680),Q=r(84443),Z=r(90020),ee=r(79961),eo=r(71185),er=r(91047),es=r(83881),en=r(47402),el=r(99820),et=r(72671),ei=r(6159),ea=r(96268),ec=r(25964),ed=r(55631),eh=r(58382),em=r(22237),ex=r(61481);let eu={command:[{name:"{command.name}",description:"Command name that was executed",icon:"⚡"},{name:"{command.user}",description:"User who executed the command",icon:"\uD83D\uDC64"},{name:"{command.channel}",description:"Channel where command was executed",icon:"\uD83D\uDCFA"},{name:"{command.server}",description:"Server where command was executed",icon:"\uD83C\uDFE0"},{name:"{command.timestamp}",description:"When the command was executed",icon:"⏰"}],options:[{name:"{option.name}",description:"Value of a specific option",icon:"\uD83D\uDD27"},{name:"{option.user}",description:"User option value",icon:"\uD83D\uDC64"},{name:"{option.channel}",description:"Channel option value",icon:"\uD83D\uDCFA"},{name:"{option.role}",description:"Role option value",icon:"\uD83C\uDFAD"},{name:"{option.string}",description:"String option value",icon:"\uD83D\uDCAC"},{name:"{option.number}",description:"Number option value",icon:"\uD83D\uDD22"},{name:"{option.boolean}",description:"Boolean option value",icon:"✅"}],user:[{name:"{user.id}",description:"User ID",icon:"\uD83C\uDD94"},{name:"{user.username}",description:"Username",icon:"\uD83D\uDC64"},{name:"{user.displayName}",description:"Display Name",icon:"\uD83D\uDCDD"},{name:"{user.tag}",description:"User Tag (username#0000)",icon:"\uD83C\uDFF7️"},{name:"{user.mention}",description:"User Mention (<@id>)",icon:"\uD83D\uDCE2"},{name:"{user.avatar}",description:"Avatar URL",icon:"\uD83D\uDDBC️"},{name:"{user.roles}",description:"User Roles",icon:"\uD83C\uDFAD"},{name:"{user.permissions}",description:"User Permissions",icon:"\uD83D\uDD10"},{name:"{user.joinedAt}",description:"Server Join Date",icon:"\uD83D\uDEAA"}],channel:[{name:"{channel.id}",description:"Channel ID",icon:"\uD83C\uDD94"},{name:"{channel.name}",description:"Channel Name",icon:"\uD83D\uDCFA"},{name:"{channel.mention}",description:"Channel Mention (<#id>)",icon:"\uD83D\uDCE2"},{name:"{channel.type}",description:"Channel Type",icon:"\uD83D\uDCCB"},{name:"{channel.topic}",description:"Channel Topic",icon:"\uD83D\uDCAC"},{name:"{channel.memberCount}",description:"Member Count",icon:"\uD83D\uDC65"}],server:[{name:"{server.id}",description:"Server ID",icon:"\uD83C\uDD94"},{name:"{server.name}",description:"Server Name",icon:"\uD83C\uDFE0"},{name:"{server.icon}",description:"Server Icon URL",icon:"\uD83D\uDDBC️"},{name:"{server.memberCount}",description:"Member Count",icon:"\uD83D\uDC65"},{name:"{server.owner}",description:"Server Owner",icon:"\uD83D\uDC51"},{name:"{server.boostLevel}",description:"Server Boost Level",icon:"\uD83D\uDE80"}]},ep=["ADMINISTRATOR","MANAGE_GUILD","MANAGE_ROLES","MANAGE_CHANNELS","KICK_MEMBERS","BAN_MEMBERS","MANAGE_MESSAGES","EMBED_LINKS","ATTACH_FILES","READ_MESSAGE_HISTORY","MENTION_EVERYONE","USE_EXTERNAL_EMOJIS","CONNECT","SPEAK","MUTE_MEMBERS","DEAFEN_MEMBERS","MOVE_MEMBERS","USE_VAD","CHANGE_NICKNAME","MANAGE_NICKNAMES","MANAGE_WEBHOOKS","MANAGE_EMOJIS","MODERATE_MEMBERS","VIEW_AUDIT_LOG","MANAGE_EVENTS","MANAGE_THREADS","CREATE_PUBLIC_THREADS","CREATE_PRIVATE_THREADS","USE_EXTERNAL_STICKERS","SEND_MESSAGES_IN_THREADS","START_EMBEDDED_ACTIVITIES"],eg=[{value:"string",label:"\uD83D\uDCDD String - Text input"},{value:"integer",label:"\uD83D\uDD22 Integer - Whole number"},{value:"number",label:"\uD83D\uDD22 Number - Decimal number"},{value:"boolean",label:"✅ Boolean - True/False"},{value:"user",label:"\uD83D\uDC64 User - Discord user"},{value:"channel",label:"\uD83D\uDCFA Channel - Discord channel"},{value:"role",label:"\uD83C\uDFAD Role - Discord role"},{value:"mentionable",label:"\uD83D\uDCE2 Mentionable - User or role"},{value:"attachment",label:"\uD83D\uDCCE Attachment - File upload"}],eb=(0,n.memo)(e=>{var o,r,t,h,x,R,A;let{data:M,selected:O,id:W,updateNodeData:I}=e,{currentScheme:F}=(0,V.DP)(),{isOpen:U,onOpen:N,onClose:L}=(0,l.j)(),[P,_]=(0,n.useState)(()=>({guildOnly:!1,adminOnly:!1,allowDMs:!1,cooldown:0,options:[],category:"general",examples:[],permissions:[],ephemeral:!1,deferReply:!1,...M})),[H,J]=(0,n.useState)(!1),G=e=>{_(o=>({...o,...e}))},eb=(e,o)=>{let r=[...P.options||[]];r[e]={...r[e],...o},G({options:r})},ej=e=>{G({options:(P.options||[]).filter((o,r)=>r!==e)})},ef=e=>{let o=[...P.options||[]];o[e].choices||(o[e].choices=[]),o[e].choices.push({name:"",value:""}),G({options:o})},ev=(e,o,r,s)=>{let n=[...P.options||[]];n[e].choices&&(n[e].choices[o][r]=s,G({options:n}))},ey=(e,o)=>{let r=[...P.options||[]];r[e].choices&&(r[e].choices=r[e].choices.filter((e,r)=>r!==o),G({options:r}))},eC=e=>{navigator.clipboard.writeText(e)};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(i.a,{bg:F.colors.surface,border:"2px solid ".concat(O?"#3b82f6":F.colors.border),borderRadius:"md",p:2,minW:"140px",maxW:"180px",boxShadow:"sm",position:"relative",_hover:{boxShadow:"md",transform:"translateY(-1px)"},transition:"all 0.2s",children:[(0,s.jsx)(q.h7,{type:"target",position:q.yX.Top,style:{background:"#3b82f6",border:"2px solid ".concat(F.colors.surface),width:"12px",height:"12px",top:"-6px",left:"50%",transform:"translateX(-50%)"}}),(0,s.jsxs)(a.T,{spacing:1,align:"stretch",children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",children:[(0,s.jsxs)(c.z,{spacing:1,children:[(0,s.jsx)(i.a,{bg:"blue.500",color:"white",borderRadius:"full",p:.5,fontSize:"xs",children:(0,s.jsx)(B.FrA,{})}),(0,s.jsx)(m.E,{fontSize:"xs",fontWeight:"bold",color:F.colors.text,children:"Command"})]}),(0,s.jsx)(d.K,{icon:(0,s.jsx)(B.VSk,{}),size:"xs",variant:"ghost",onClick:N,"aria-label":"Configure command"})]}),(0,s.jsx)(i.a,{children:(0,s.jsxs)(m.E,{fontSize:"xs",color:F.colors.text,noOfLines:1,children:["/",P.commandName||"unnamed"]})}),P.description&&(0,s.jsx)(i.a,{children:(0,s.jsx)(m.E,{fontSize:"xs",color:F.colors.textSecondary,noOfLines:1,children:P.description.length>25?P.description.substring(0,25)+"...":P.description})}),(0,s.jsxs)(c.z,{spacing:1,flexWrap:"wrap",children:[(null!=(R=null==(o=P.options)?void 0:o.length)?R:0)>0&&(0,s.jsxs)(p.E,{size:"xs",colorScheme:"blue",children:[null==(r=P.options)?void 0:r.length," option",(null!=(A=null==(t=P.options)?void 0:t.length)?A:0)!==1?"s":""]}),P.adminOnly&&(0,s.jsx)(p.E,{size:"xs",colorScheme:"red",children:"Admin"}),P.cooldown&&P.cooldown>0&&(0,s.jsxs)(p.E,{size:"xs",colorScheme:"orange",children:[P.cooldown,"s"]})]})]}),(0,s.jsx)(q.h7,{type:"source",position:q.yX.Bottom,style:{background:"#3b82f6",border:"2px solid ".concat(F.colors.surface),width:"12px",height:"12px",bottom:"-6px",left:"50%",transform:"translateX(-50%)"}})]}),(0,s.jsxs)(g.aF,{isOpen:U,onClose:()=>{I&&W&&I(W,P),L()},size:"4xl",children:[(0,s.jsx)(b.m,{bg:"blackAlpha.600"}),(0,s.jsxs)(j.$,{bg:F.colors.background,border:"2px solid",borderColor:"blue.400",maxW:"1200px",children:[(0,s.jsx)(f.r,{color:F.colors.text,children:"⚡ Configure Command"}),(0,s.jsx)(v.s,{}),(0,s.jsx)(y.c,{pb:6,children:(0,s.jsxs)(a.T,{spacing:6,align:"stretch",children:[(0,s.jsxs)(i.a,{children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",mb:2,children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:F.colors.text,children:"Available Variables"}),(0,s.jsxs)(u.$,{size:"sm",variant:"ghost",leftIcon:H?(0,s.jsx)(B._NO,{}):(0,s.jsx)(B.Vap,{}),onClick:()=>J(!H),children:[H?"Hide":"Show"," Variables"]})]}),(0,s.jsxs)(C.F,{status:"info",borderRadius:"md",mb:2,children:[(0,s.jsx)(S._,{}),(0,s.jsx)(w.T,{fontSize:"sm",children:"\uD83D\uDCA1 Use variables in your command responses! Click any variable below to copy it. Variables are replaced with actual values when your command runs."})]}),(0,s.jsx)(K.S,{in:H,animateOpacity:!0,children:(0,s.jsx)(i.a,{bg:F.colors.surface,border:"1px solid",borderColor:F.colors.border,borderRadius:"md",p:4,mt:3,maxH:"400px",overflowY:"auto",children:(0,s.jsx)($.n,{allowMultiple:!0,children:Object.entries(eu).map(e=>{let[o,r]=e;return(0,s.jsxs)(X.A,{border:"none",children:[(0,s.jsxs)(Y.J,{px:0,py:2,children:[(0,s.jsx)(i.a,{flex:"1",textAlign:"left",children:(0,s.jsxs)(m.E,{fontSize:"sm",fontWeight:"bold",color:F.colors.text,textTransform:"capitalize",children:[o," Variables"]})}),(0,s.jsx)(Q.Q,{})]}),(0,s.jsx)(Z.v,{px:0,py:2,children:(0,s.jsx)(a.T,{spacing:2,align:"stretch",children:r.map(e=>(0,s.jsxs)(c.z,{spacing:2,p:2,bg:F.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:F.colors.surface},onClick:()=>eC(e.name),children:[(0,s.jsx)(m.E,{fontSize:"sm",children:e.icon}),(0,s.jsx)(ee.C,{fontSize:"xs",colorScheme:"blue",children:e.name}),(0,s.jsx)(m.E,{fontSize:"xs",color:F.colors.textSecondary,flex:"1",children:e.description}),(0,s.jsx)(d.K,{icon:(0,s.jsx)(B.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable",onClick:o=>{o.stopPropagation(),eC(e.name)}})]},e.name))})})]},o)})})})})]}),(0,s.jsx)(eo.c,{}),(0,s.jsxs)(er.t,{variant:"enclosed",colorScheme:"blue",children:[(0,s.jsxs)(es.w,{children:[(0,s.jsx)(en.o,{children:"Basic Info"}),(0,s.jsx)(en.o,{children:"Options"}),(0,s.jsx)(en.o,{children:"Permissions"}),(0,s.jsx)(en.o,{children:"Advanced"})]}),(0,s.jsxs)(el.T,{children:[(0,s.jsx)(et.K,{children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(E.r,{columns:2,spacing:4,children:[(0,s.jsxs)(T.MJ,{isRequired:!0,children:[(0,s.jsx)(z.l,{color:F.colors.text,children:"Command Name"}),(0,s.jsxs)(ei.M,{children:[(0,s.jsx)(ea.G6,{bg:F.colors.surface,color:F.colors.text,children:"/"}),(0,s.jsx)(k.p,{value:P.commandName||"",onChange:e=>G({commandName:e.target.value}),placeholder:"ping",bg:F.colors.background,color:F.colors.text,borderColor:F.colors.border})]})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:F.colors.text,children:"Category"}),(0,s.jsxs)(ec.l,{value:P.category||"general",onChange:e=>G({category:e.target.value}),bg:F.colors.background,color:F.colors.text,borderColor:F.colors.border,children:[(0,s.jsx)("option",{value:"general",children:"General"}),(0,s.jsx)("option",{value:"moderation",children:"Moderation"}),(0,s.jsx)("option",{value:"fun",children:"Fun"}),(0,s.jsx)("option",{value:"utility",children:"Utility"}),(0,s.jsx)("option",{value:"admin",children:"Admin"}),(0,s.jsx)("option",{value:"info",children:"Info"}),(0,s.jsx)("option",{value:"music",children:"Music"}),(0,s.jsx)("option",{value:"games",children:"Games"})]})]})]}),(0,s.jsxs)(T.MJ,{isRequired:!0,children:[(0,s.jsx)(z.l,{color:F.colors.text,children:"Description"}),(0,s.jsx)(D.T,{value:P.description||"",onChange:e=>G({description:e.target.value}),placeholder:"What does this command do?",bg:F.colors.background,color:F.colors.text,borderColor:F.colors.border,minH:"80px"})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:F.colors.text,children:"Usage Examples"}),(0,s.jsx)(D.T,{value:(null==(h=P.examples)?void 0:h.join("\n"))||"",onChange:e=>G({examples:e.target.value.split("\n").filter(e=>e.trim())}),placeholder:"/ping\n/ping server\n/ping {user.mention}",bg:F.colors.background,color:F.colors.text,borderColor:F.colors.border,minH:"80px"}),(0,s.jsx)(m.E,{fontSize:"xs",color:F.colors.textSecondary,mt:1,children:"One example per line"})]})]})}),(0,s.jsx)(et.K,{children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",children:[(0,s.jsx)(m.E,{fontSize:"lg",fontWeight:"bold",color:F.colors.text,children:"Command Options"}),(0,s.jsx)(u.$,{leftIcon:(0,s.jsx)(B.GGD,{}),onClick:()=>{G({options:[...P.options||[],{name:"",description:"",type:"string",required:!1,choices:[]}]})},colorScheme:"blue",size:"sm",children:"Add Option"})]}),(0,s.jsxs)(C.F,{status:"info",borderRadius:"md",children:[(0,s.jsx)(S._,{}),(0,s.jsx)(w.T,{fontSize:"sm",children:"Options are parameters users can provide with your command. They appear as autocomplete fields in Discord."})]}),(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[null==(x=P.options)?void 0:x.map((e,o)=>{var r;return(0,s.jsxs)(i.a,{p:4,bg:F.colors.surface,borderRadius:"md",border:"1px solid",borderColor:F.colors.border,children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",mb:3,children:[(0,s.jsxs)(m.E,{fontSize:"md",fontWeight:"bold",color:F.colors.text,children:["Option ",o+1]}),(0,s.jsx)(d.K,{icon:(0,s.jsx)(B.IXo,{}),size:"sm",colorScheme:"red",variant:"ghost",onClick:()=>ej(o),"aria-label":"Remove option"})]}),(0,s.jsxs)(a.T,{spacing:3,align:"stretch",children:[(0,s.jsxs)(E.r,{columns:2,spacing:3,children:[(0,s.jsxs)(T.MJ,{isRequired:!0,children:[(0,s.jsx)(z.l,{fontSize:"sm",color:F.colors.text,children:"Option Name"}),(0,s.jsx)(k.p,{value:e.name,onChange:e=>eb(o,{name:e.target.value}),placeholder:"user",bg:F.colors.background,color:F.colors.text,borderColor:F.colors.border,size:"sm"})]}),(0,s.jsxs)(T.MJ,{isRequired:!0,children:[(0,s.jsx)(z.l,{fontSize:"sm",color:F.colors.text,children:"Option Type"}),(0,s.jsx)(ec.l,{value:e.type,onChange:e=>eb(o,{type:e.target.value}),bg:F.colors.background,color:F.colors.text,borderColor:F.colors.border,size:"sm",children:eg.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))})]})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{fontSize:"sm",color:F.colors.text,children:"Description"}),(0,s.jsx)(k.p,{value:e.description,onChange:e=>eb(o,{description:e.target.value}),placeholder:"The user to ping",bg:F.colors.background,color:F.colors.text,borderColor:F.colors.border,size:"sm"})]}),(0,s.jsxs)(c.z,{children:[(0,s.jsx)(ed.d,{isChecked:e.required,onChange:e=>eb(o,{required:e.target.checked}),colorScheme:"blue"}),(0,s.jsx)(m.E,{fontSize:"sm",color:F.colors.text,children:"Required option"})]}),("string"===e.type||"integer"===e.type||"number"===e.type)&&(0,s.jsxs)(i.a,{children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",mb:2,children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:F.colors.text,children:"Predefined Choices (Optional)"}),(0,s.jsx)(u.$,{size:"xs",leftIcon:(0,s.jsx)(B.GGD,{}),onClick:()=>ef(o),colorScheme:"blue",variant:"ghost",children:"Add Choice"})]}),(0,s.jsx)(a.T,{spacing:2,align:"stretch",children:null==(r=e.choices)?void 0:r.map((e,r)=>(0,s.jsxs)(c.z,{spacing:2,children:[(0,s.jsx)(k.p,{value:e.name,onChange:e=>ev(o,r,"name",e.target.value),placeholder:"Choice name",bg:F.colors.background,color:F.colors.text,borderColor:F.colors.border,size:"sm"}),(0,s.jsx)(k.p,{value:e.value,onChange:e=>ev(o,r,"value",e.target.value),placeholder:"Choice value",bg:F.colors.background,color:F.colors.text,borderColor:F.colors.border,size:"sm"}),(0,s.jsx)(d.K,{icon:(0,s.jsx)(B.QLg,{}),size:"sm",colorScheme:"red",variant:"ghost",onClick:()=>ey(o,r),"aria-label":"Remove choice"})]},r))})]})]})]},o)}),(!P.options||0===P.options.length)&&(0,s.jsxs)(C.F,{status:"info",borderRadius:"md",children:[(0,s.jsx)(S._,{}),(0,s.jsx)(w.T,{children:"No options configured. Your command will work without any parameters."})]})]})]})}),(0,s.jsx)(et.K,{children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsx)(m.E,{fontSize:"lg",fontWeight:"bold",color:F.colors.text,children:"Command Permissions"}),(0,s.jsxs)(C.F,{status:"warning",borderRadius:"md",children:[(0,s.jsx)(S._,{}),(0,s.jsx)(w.T,{fontSize:"sm",children:"Be careful with permissions! Overly restrictive permissions can prevent legitimate users from using your command."})]}),(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(c.z,{spacing:4,children:[(0,s.jsx)(ed.d,{isChecked:P.adminOnly,onChange:e=>G({adminOnly:e.target.checked}),colorScheme:"red"}),(0,s.jsxs)(a.T,{align:"start",spacing:0,children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:F.colors.text,children:"Admin Only"}),(0,s.jsx)(m.E,{fontSize:"xs",color:F.colors.textSecondary,children:"Only server administrators can use this command"})]})]}),(0,s.jsxs)(c.z,{spacing:4,children:[(0,s.jsx)(ed.d,{isChecked:P.guildOnly,onChange:e=>G({guildOnly:e.target.checked}),colorScheme:"blue"}),(0,s.jsxs)(a.T,{align:"start",spacing:0,children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:F.colors.text,children:"Server Only"}),(0,s.jsx)(m.E,{fontSize:"xs",color:F.colors.textSecondary,children:"Command can only be used in servers, not DMs"})]})]}),(0,s.jsxs)(c.z,{spacing:4,children:[(0,s.jsx)(ed.d,{isChecked:P.allowDMs,onChange:e=>G({allowDMs:e.target.checked}),colorScheme:"green"}),(0,s.jsxs)(a.T,{align:"start",spacing:0,children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:F.colors.text,children:"Allow DMs"}),(0,s.jsx)(m.E,{fontSize:"xs",color:F.colors.textSecondary,children:"Command can be used in direct messages"})]})]})]}),(0,s.jsx)(eo.c,{}),(0,s.jsxs)(i.a,{children:[(0,s.jsx)(m.E,{fontSize:"md",fontWeight:"bold",color:F.colors.text,mb:3,children:"Required Permissions"}),(0,s.jsx)(m.E,{fontSize:"sm",color:F.colors.textSecondary,mb:3,children:"Select the Discord permissions users need to use this command"}),(0,s.jsx)(eh.$,{value:P.permissions||[],onChange:e=>G({permissions:e}),children:(0,s.jsx)(E.r,{columns:3,spacing:2,children:ep.map(e=>(0,s.jsx)(em.S,{value:e,colorScheme:"blue",size:"sm",children:(0,s.jsx)(m.E,{fontSize:"xs",color:F.colors.text,children:e.replace(/_/g," ").toLowerCase().replace(/\b\w/g,e=>e.toUpperCase())})},e))})})]})]})}),(0,s.jsx)(et.K,{children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsx)(m.E,{fontSize:"lg",fontWeight:"bold",color:F.colors.text,children:"Advanced Settings"}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:F.colors.text,children:"Cooldown (seconds)"}),(0,s.jsxs)(ex.Q7,{value:P.cooldown||0,onChange:e=>G({cooldown:parseInt(e)||0}),min:0,max:3600,children:[(0,s.jsx)(ex.OO,{bg:F.colors.background,color:F.colors.text,borderColor:F.colors.border}),(0,s.jsxs)(ex.lw,{children:[(0,s.jsx)(ex.Q0,{}),(0,s.jsx)(ex.Sh,{})]})]}),(0,s.jsx)(m.E,{fontSize:"xs",color:F.colors.textSecondary,mt:1,children:"How long users must wait between uses (0 = no cooldown)"})]}),(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(c.z,{spacing:4,children:[(0,s.jsx)(ed.d,{isChecked:P.ephemeral,onChange:e=>G({ephemeral:e.target.checked}),colorScheme:"blue"}),(0,s.jsxs)(a.T,{align:"start",spacing:0,children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:F.colors.text,children:"Ephemeral Response"}),(0,s.jsx)(m.E,{fontSize:"xs",color:F.colors.textSecondary,children:"Command response is only visible to the user who ran it"})]})]}),(0,s.jsxs)(c.z,{spacing:4,children:[(0,s.jsx)(ed.d,{isChecked:P.deferReply,onChange:e=>G({deferReply:e.target.checked}),colorScheme:"orange"}),(0,s.jsxs)(a.T,{align:"start",spacing:0,children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:F.colors.text,children:"Defer Reply"}),(0,s.jsx)(m.E,{fontSize:"xs",color:F.colors.textSecondary,children:'Show "thinking..." message while processing (for slow commands)'})]})]})]})]})})]})]}),(0,s.jsx)(u.$,{colorScheme:"blue",onClick:()=>{M.commandName=P.commandName,M.description=P.description,M.options=P.options,M.permissions=P.permissions,M.cooldown=P.cooldown,M.guildOnly=P.guildOnly,M.adminOnly=P.adminOnly,M.allowDMs=P.allowDMs,M.category=P.category,M.examples=P.examples,M.ephemeral=P.ephemeral,M.deferReply=P.deferReply,M.label=P.commandName?"/".concat(P.commandName):"Command",L()},size:"lg",width:"full",children:"Save Configuration"})]})})]})]})]})});eb.displayName="CommandNode";let ej={event:[{name:"{event.type}",description:"Type of event that triggered",icon:"\uD83D\uDCE1"},{name:"{event.timestamp}",description:"When the event occurred",icon:"⏰"},{name:"{event.guild}",description:"Server where event occurred",icon:"\uD83C\uDFE0"},{name:"{event.channel}",description:"Channel where event occurred",icon:"\uD83D\uDCFA"},{name:"{event.user}",description:"User who triggered the event",icon:"\uD83D\uDC64"}],message:[{name:"{message.id}",description:"Message ID",icon:"\uD83C\uDD94"},{name:"{message.content}",description:"Message content",icon:"\uD83D\uDCAC"},{name:"{message.author}",description:"Message author",icon:"\uD83D\uDC64"},{name:"{message.channel}",description:"Message channel",icon:"\uD83D\uDCFA"},{name:"{message.createdAt}",description:"Message creation time",icon:"\uD83D\uDCC5"},{name:"{message.editedAt}",description:"Message edit time",icon:"✏️"},{name:"{message.attachments}",description:"Message attachments",icon:"\uD83D\uDCCE"},{name:"{message.embeds}",description:"Message embeds",icon:"\uD83D\uDCCB"},{name:"{message.reactions}",description:"Message reactions",icon:"\uD83D\uDC4D"},{name:"{message.mentions}",description:"Message mentions",icon:"\uD83D\uDCE2"}],member:[{name:"{member.id}",description:"Member ID",icon:"\uD83C\uDD94"},{name:"{member.username}",description:"Member username",icon:"\uD83D\uDC64"},{name:"{member.displayName}",description:"Member display name",icon:"\uD83D\uDCDD"},{name:"{member.tag}",description:"Member tag (username#0000)",icon:"\uD83C\uDFF7️"},{name:"{member.mention}",description:"Member mention (<@id>)",icon:"\uD83D\uDCE2"},{name:"{member.avatar}",description:"Member avatar URL",icon:"\uD83D\uDDBC️"},{name:"{member.joinedAt}",description:"Server join date",icon:"\uD83D\uDEAA"},{name:"{member.roles}",description:"Member roles",icon:"\uD83C\uDFAD"},{name:"{member.permissions}",description:"Member permissions",icon:"\uD83D\uDD10"},{name:"{member.isBot}",description:"Is member a bot",icon:"\uD83E\uDD16"}],channel:[{name:"{channel.id}",description:"Channel ID",icon:"\uD83C\uDD94"},{name:"{channel.name}",description:"Channel name",icon:"\uD83D\uDCFA"},{name:"{channel.mention}",description:"Channel mention (<#id>)",icon:"\uD83D\uDCE2"},{name:"{channel.type}",description:"Channel type",icon:"\uD83D\uDCCB"},{name:"{channel.topic}",description:"Channel topic",icon:"\uD83D\uDCAC"},{name:"{channel.memberCount}",description:"Member count",icon:"\uD83D\uDC65"},{name:"{channel.position}",description:"Channel position",icon:"\uD83D\uDCCD"},{name:"{channel.nsfw}",description:"Is NSFW channel",icon:"\uD83D\uDD1E"}],server:[{name:"{server.id}",description:"Server ID",icon:"\uD83C\uDD94"},{name:"{server.name}",description:"Server name",icon:"\uD83C\uDFE0"},{name:"{server.icon}",description:"Server icon URL",icon:"\uD83D\uDDBC️"},{name:"{server.memberCount}",description:"Total member count",icon:"\uD83D\uDC65"},{name:"{server.owner}",description:"Server owner",icon:"\uD83D\uDC51"},{name:"{server.boostLevel}",description:"Server boost level",icon:"\uD83D\uDE80"},{name:"{server.boostCount}",description:"Server boost count",icon:"\uD83D\uDC8E"},{name:"{server.createdAt}",description:"Server creation date",icon:"\uD83D\uDCC5"}],reaction:[{name:"{reaction.emoji}",description:"Reaction emoji",icon:"\uD83D\uDE00"},{name:"{reaction.count}",description:"Reaction count",icon:"\uD83D\uDD22"},{name:"{reaction.users}",description:"Users who reacted",icon:"\uD83D\uDC65"},{name:"{reaction.me}",description:"Bot reacted",icon:"\uD83E\uDD16"}],voice:[{name:"{voice.channelId}",description:"Voice channel ID",icon:"\uD83D\uDD0A"},{name:"{voice.channelName}",description:"Voice channel name",icon:"\uD83D\uDD0A"},{name:"{voice.memberCount}",description:"Voice channel member count",icon:"\uD83D\uDC65"},{name:"{voice.muted}",description:"Is member muted",icon:"\uD83D\uDD07"},{name:"{voice.deafened}",description:"Is member deafened",icon:"\uD83D\uDD07"},{name:"{voice.streaming}",description:"Is member streaming",icon:"\uD83D\uDCFA"},{name:"{voice.camera}",description:"Is member using camera",icon:"\uD83D\uDCF9"}],role:[{name:"{role.id}",description:"Role ID",icon:"\uD83C\uDD94"},{name:"{role.name}",description:"Role name",icon:"\uD83C\uDFAD"},{name:"{role.mention}",description:"Role mention (<@&id>)",icon:"\uD83D\uDCE2"},{name:"{role.color}",description:"Role color",icon:"\uD83C\uDFA8"},{name:"{role.position}",description:"Role position",icon:"\uD83D\uDCCD"},{name:"{role.permissions}",description:"Role permissions",icon:"\uD83D\uDD10"},{name:"{role.mentionable}",description:"Is role mentionable",icon:"\uD83D\uDCE2"},{name:"{role.hoisted}",description:"Is role hoisted",icon:"\uD83D\uDCCC"}]},ef=[{value:"messageCreate",label:"\uD83D\uDCAC Message Created",category:"Messages",description:"When a new message is sent"},{value:"messageUpdate",label:"✏️ Message Edited",category:"Messages",description:"When a message is edited"},{value:"messageDelete",label:"\uD83D\uDDD1️ Message Deleted",category:"Messages",description:"When a message is deleted"},{value:"messageReactionAdd",label:"\uD83D\uDC4D Reaction Added",category:"Messages",description:"When a reaction is added to a message"},{value:"messageReactionRemove",label:"\uD83D\uDC4E Reaction Removed",category:"Messages",description:"When a reaction is removed from a message"},{value:"messageReactionRemoveAll",label:"\uD83E\uDDF9 All Reactions Removed",category:"Messages",description:"When all reactions are removed from a message"},{value:"guildMemberAdd",label:"\uD83D\uDEAA Member Joined",category:"Members",description:"When a new member joins the server"},{value:"guildMemberRemove",label:"\uD83D\uDC4B Member Left",category:"Members",description:"When a member leaves the server"},{value:"guildMemberUpdate",label:"\uD83D\uDC64 Member Updated",category:"Members",description:"When member info changes (roles, nickname, etc.)"},{value:"userUpdate",label:"\uD83D\uDCDD User Updated",category:"Members",description:"When user profile changes (avatar, username, etc.)"},{value:"presenceUpdate",label:"\uD83D\uDFE2 Presence Updated",category:"Members",description:"When member status/activity changes"},{value:"guildBanAdd",label:"\uD83D\uDD28 Member Banned",category:"Moderation",description:"When a member is banned"},{value:"guildBanRemove",label:"\uD83D\uDD13 Member Unbanned",category:"Moderation",description:"When a member is unbanned"},{value:"messageDeleteBulk",label:"\uD83E\uDDF9 Bulk Message Delete",category:"Moderation",description:"When multiple messages are deleted at once"},{value:"voiceStateUpdate",label:"\uD83D\uDD0A Voice State Changed",category:"Voice",description:"When member joins/leaves/mutes in voice"},{value:"channelCreate",label:"\uD83D\uDCFA Channel Created",category:"Channels",description:"When a new channel is created"},{value:"channelDelete",label:"\uD83D\uDDD1️ Channel Deleted",category:"Channels",description:"When a channel is deleted"},{value:"channelUpdate",label:"⚙️ Channel Updated",category:"Channels",description:"When channel settings change"},{value:"channelPinsUpdate",label:"\uD83D\uDCCC Channel Pins Updated",category:"Channels",description:"When pinned messages change"},{value:"roleCreate",label:"\uD83C\uDFAD Role Created",category:"Roles",description:"When a new role is created"},{value:"roleDelete",label:"\uD83D\uDDD1️ Role Deleted",category:"Roles",description:"When a role is deleted"},{value:"roleUpdate",label:"⚙️ Role Updated",category:"Roles",description:"When role settings change"},{value:"threadCreate",label:"\uD83E\uDDF5 Thread Created",category:"Threads",description:"When a thread is created"},{value:"threadDelete",label:"\uD83D\uDDD1️ Thread Deleted",category:"Threads",description:"When a thread is deleted"},{value:"threadUpdate",label:"⚙️ Thread Updated",category:"Threads",description:"When thread settings change"},{value:"threadMemberUpdate",label:"\uD83D\uDC64 Thread Member Update",category:"Threads",description:"When someone joins/leaves a thread"},{value:"interactionCreate",label:"\uD83C\uDF9B️ Interaction Created",category:"Interactions",description:"When buttons/selects are used"},{value:"applicationCommandPermissionsUpdate",label:"\uD83D\uDD10 Command Permissions Updated",category:"Interactions",description:"When command permissions change"},{value:"guildUpdate",label:"\uD83C\uDFE0 Server Updated",category:"Server",description:"When server settings change"},{value:"guildUnavailable",label:"⚠️ Server Unavailable",category:"Server",description:"When server becomes unavailable"},{value:"guildIntegrationsUpdate",label:"\uD83D\uDD17 Integrations Updated",category:"Server",description:"When server integrations change"},{value:"inviteCreate",label:"\uD83D\uDD17 Invite Created",category:"Server",description:"When an invite is created"},{value:"inviteDelete",label:"\uD83D\uDDD1️ Invite Deleted",category:"Server",description:"When an invite is deleted"},{value:"emojiCreate",label:"\uD83D\uDE00 Emoji Created",category:"Server",description:"When a custom emoji is added"},{value:"emojiDelete",label:"\uD83D\uDDD1️ Emoji Deleted",category:"Server",description:"When a custom emoji is removed"},{value:"emojiUpdate",label:"⚙️ Emoji Updated",category:"Server",description:"When a custom emoji is modified"},{value:"stickerCreate",label:"\uD83C\uDFF7️ Sticker Created",category:"Server",description:"When a custom sticker is added"},{value:"stickerDelete",label:"\uD83D\uDDD1️ Sticker Deleted",category:"Server",description:"When a custom sticker is removed"},{value:"stickerUpdate",label:"⚙️ Sticker Updated",category:"Server",description:"When a custom sticker is modified"}],ev=[{value:"channel",label:"\uD83D\uDCFA Channel Filter",description:"Filter by specific channels"},{value:"role",label:"\uD83C\uDFAD Role Filter",description:"Filter by user roles"},{value:"user",label:"\uD83D\uDC64 User Filter",description:"Filter by specific users"},{value:"regex",label:"\uD83D\uDD0D Regex Pattern",description:"Filter using regular expressions"},{value:"cooldown",label:"⏰ Cooldown",description:"Rate limit event triggers"},{value:"permission",label:"\uD83D\uDD10 Permission",description:"Filter by user permissions"},{value:"content",label:"\uD83D\uDCAC Content Filter",description:"Filter by message content"},{value:"custom",label:"⚙️ Custom",description:"Custom filter condition"}],ey=(0,n.memo)(e=>{var o,r,t,h,x,R,A,M;let{data:O,selected:W,id:I,updateNodeData:F}=e,{currentScheme:U}=(0,V.DP)(),{isOpen:N,onOpen:L,onClose:P}=(0,l.j)(),[_,H]=(0,n.useState)(()=>({ignoreBot:!0,ignoreSystem:!0,rateLimited:!1,rateLimit:1e3,priority:1,async:!1,retryOnError:!1,maxRetries:3,filters:[],channelRestrictions:[],roleRestrictions:[],...O})),[J,G]=(0,n.useState)(!1),ei=e=>{H(o=>({...o,...e}))},ea=e=>{let o=ef.find(o=>o.value===e);return o?o.label.split(" ").slice(1).join(" "):e},eh=(e,o)=>{let r=[..._.filters||[]];r[e]={...r[e],...o},ei({filters:r})},em=e=>{ei({filters:(_.filters||[]).filter((o,r)=>r!==e)})},eu=e=>{navigator.clipboard.writeText(e)};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(i.a,{bg:U.colors.surface,border:"2px solid ".concat(W?"#10b981":U.colors.border),borderRadius:"md",p:2,minW:"140px",maxW:"180px",boxShadow:"sm",position:"relative",_hover:{boxShadow:"md",transform:"translateY(-1px)"},transition:"all 0.2s",children:[(0,s.jsx)(q.h7,{type:"target",position:q.yX.Top,style:{background:"#10b981",border:"2px solid ".concat(U.colors.surface),width:"12px",height:"12px",top:"-6px",left:"50%",transform:"translateX(-50%)"}}),(0,s.jsxs)(a.T,{spacing:1,align:"stretch",children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",children:[(0,s.jsxs)(c.z,{spacing:1,children:[(0,s.jsx)(i.a,{bg:"green.500",color:"white",borderRadius:"full",p:.5,fontSize:"xs",children:(0,s.jsx)(B.DQs,{})}),(0,s.jsx)(m.E,{fontSize:"xs",fontWeight:"bold",color:U.colors.text,children:"Event"})]}),(0,s.jsx)(d.K,{icon:(0,s.jsx)(B.VSk,{}),size:"xs",variant:"ghost",onClick:L,"aria-label":"Configure event"})]}),(0,s.jsx)(i.a,{children:(0,s.jsxs)(c.z,{spacing:1,children:[_.eventType&&(0,s.jsx)(m.E,{fontSize:"xs",children:(e=>{let o=ef.find(o=>o.value===e);return o?o.label.split(" ")[0]:"\uD83D\uDCE1"})(_.eventType)}),(0,s.jsx)(m.E,{fontSize:"xs",color:U.colors.text,noOfLines:1,children:_.eventType?ea(_.eventType):"Select Event"})]})}),_.description&&(0,s.jsx)(i.a,{children:(0,s.jsx)(m.E,{fontSize:"xs",color:U.colors.textSecondary,noOfLines:1,children:_.description.length>25?_.description.substring(0,25)+"...":_.description})}),(0,s.jsxs)(c.z,{spacing:1,flexWrap:"wrap",children:[(null!=(A=null==(o=_.filters)?void 0:o.length)?A:0)>0&&(0,s.jsxs)(p.E,{size:"xs",colorScheme:"green",children:[null==(r=_.filters)?void 0:r.length," filter",(null!=(M=null==(t=_.filters)?void 0:t.length)?M:0)!==1?"s":""]}),_.ignoreBot&&(0,s.jsx)(p.E,{size:"xs",colorScheme:"orange",children:"No Bots"}),_.rateLimited&&(0,s.jsx)(p.E,{size:"xs",colorScheme:"yellow",children:"Rate Limited"})]})]}),(0,s.jsx)(q.h7,{type:"source",position:q.yX.Bottom,style:{background:"#10b981",border:"2px solid ".concat(U.colors.surface),width:"12px",height:"12px",bottom:"-6px",left:"50%",transform:"translateX(-50%)"}})]}),(0,s.jsxs)(g.aF,{isOpen:N,onClose:()=>{F&&I&&F(I,_),P()},size:"4xl",children:[(0,s.jsx)(b.m,{bg:"blackAlpha.600"}),(0,s.jsxs)(j.$,{bg:U.colors.background,border:"2px solid",borderColor:"green.400",maxW:"1200px",children:[(0,s.jsx)(f.r,{color:U.colors.text,children:"\uD83D\uDCE1 Configure Event"}),(0,s.jsx)(v.s,{}),(0,s.jsx)(y.c,{pb:6,children:(0,s.jsxs)(a.T,{spacing:6,align:"stretch",children:[(0,s.jsxs)(i.a,{children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",mb:2,children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:U.colors.text,children:"Available Variables"}),(0,s.jsxs)(u.$,{size:"sm",variant:"ghost",leftIcon:J?(0,s.jsx)(B._NO,{}):(0,s.jsx)(B.Vap,{}),onClick:()=>G(!J),children:[J?"Hide":"Show"," Variables"]})]}),(0,s.jsxs)(C.F,{status:"info",borderRadius:"md",mb:2,children:[(0,s.jsx)(S._,{}),(0,s.jsx)(w.T,{fontSize:"sm",children:"\uD83D\uDCA1 Use variables in your event responses! Click any variable below to copy it. Variables are replaced with actual values when your event triggers."})]}),(0,s.jsx)(K.S,{in:J,animateOpacity:!0,children:(0,s.jsx)(i.a,{bg:U.colors.surface,border:"1px solid",borderColor:U.colors.border,borderRadius:"md",p:4,mt:3,maxH:"400px",overflowY:"auto",children:(0,s.jsx)($.n,{allowMultiple:!0,children:Object.entries(ej).map(e=>{let[o,r]=e;return(0,s.jsxs)(X.A,{border:"none",children:[(0,s.jsxs)(Y.J,{px:0,py:2,children:[(0,s.jsx)(i.a,{flex:"1",textAlign:"left",children:(0,s.jsxs)(m.E,{fontSize:"sm",fontWeight:"bold",color:U.colors.text,textTransform:"capitalize",children:[o," Variables"]})}),(0,s.jsx)(Q.Q,{})]}),(0,s.jsx)(Z.v,{px:0,py:2,children:(0,s.jsx)(a.T,{spacing:2,align:"stretch",children:r.map(e=>(0,s.jsxs)(c.z,{spacing:2,p:2,bg:U.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:U.colors.surface},onClick:()=>eu(e.name),children:[(0,s.jsx)(m.E,{fontSize:"sm",children:e.icon}),(0,s.jsx)(ee.C,{fontSize:"xs",colorScheme:"green",children:e.name}),(0,s.jsx)(m.E,{fontSize:"xs",color:U.colors.textSecondary,flex:"1",children:e.description}),(0,s.jsx)(d.K,{icon:(0,s.jsx)(B.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable",onClick:o=>{o.stopPropagation(),eu(e.name)}})]},e.name))})})]},o)})})})})]}),(0,s.jsx)(eo.c,{}),(0,s.jsxs)(er.t,{variant:"enclosed",colorScheme:"green",children:[(0,s.jsxs)(es.w,{children:[(0,s.jsx)(en.o,{children:"Event Type"}),(0,s.jsx)(en.o,{children:"Filters"}),(0,s.jsx)(en.o,{children:"Settings"}),(0,s.jsx)(en.o,{children:"Advanced"})]}),(0,s.jsxs)(el.T,{children:[(0,s.jsx)(et.K,{children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(T.MJ,{isRequired:!0,children:[(0,s.jsx)(z.l,{color:U.colors.text,children:"Event Type"}),(0,s.jsx)(ec.l,{value:_.eventType||"",onChange:e=>ei({eventType:e.target.value}),placeholder:"Select an event type",bg:U.colors.background,color:U.colors.text,borderColor:U.colors.border,children:Object.entries(ef.reduce((e,o)=>(e[o.category]||(e[o.category]=[]),e[o.category].push(o),e),{})).map(e=>{let[o,r]=e;return(0,s.jsx)("optgroup",{label:o,children:r.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))},o)})})]}),_.eventType&&(0,s.jsxs)(C.F,{status:"info",borderRadius:"md",children:[(0,s.jsx)(S._,{}),(0,s.jsxs)(i.a,{children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",mb:1,children:null==(h=ef.find(e=>e.value===_.eventType))?void 0:h.label}),(0,s.jsx)(m.E,{fontSize:"sm",children:null==(x=ef.find(e=>e.value===_.eventType))?void 0:x.description})]})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:U.colors.text,children:"Description"}),(0,s.jsx)(D.T,{value:_.description||"",onChange:e=>ei({description:e.target.value}),placeholder:"Describe when this event should trigger",bg:U.colors.background,color:U.colors.text,borderColor:U.colors.border,minH:"80px"})]})]})}),(0,s.jsx)(et.K,{children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",children:[(0,s.jsx)(m.E,{fontSize:"lg",fontWeight:"bold",color:U.colors.text,children:"Event Filters"}),(0,s.jsx)(u.$,{leftIcon:(0,s.jsx)(B.GGD,{}),onClick:()=>{ei({filters:[..._.filters||[],{type:"channel",value:"",operator:"equals"}]})},colorScheme:"green",size:"sm",children:"Add Filter"})]}),(0,s.jsxs)(C.F,{status:"info",borderRadius:"md",children:[(0,s.jsx)(S._,{}),(0,s.jsx)(w.T,{fontSize:"sm",children:"Filters determine when this event should trigger. Only events that pass all filters will execute the connected actions."})]}),(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[null==(R=_.filters)?void 0:R.map((e,o)=>{var r;return(0,s.jsxs)(i.a,{p:4,bg:U.colors.surface,borderRadius:"md",border:"1px solid",borderColor:U.colors.border,children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",mb:3,children:[(0,s.jsxs)(m.E,{fontSize:"md",fontWeight:"bold",color:U.colors.text,children:["Filter ",o+1]}),(0,s.jsx)(d.K,{icon:(0,s.jsx)(B.IXo,{}),size:"sm",colorScheme:"red",variant:"ghost",onClick:()=>em(o),"aria-label":"Remove filter"})]}),(0,s.jsxs)(a.T,{spacing:3,align:"stretch",children:[(0,s.jsxs)(E.r,{columns:2,spacing:3,children:[(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{fontSize:"sm",color:U.colors.text,children:"Filter Type"}),(0,s.jsx)(ec.l,{value:e.type,onChange:e=>eh(o,{type:e.target.value}),bg:U.colors.background,color:U.colors.text,borderColor:U.colors.border,size:"sm",children:ev.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{fontSize:"sm",color:U.colors.text,children:"Operator"}),(0,s.jsxs)(ec.l,{value:e.operator||"equals",onChange:e=>eh(o,{operator:e.target.value}),bg:U.colors.background,color:U.colors.text,borderColor:U.colors.border,size:"sm",children:[(0,s.jsx)("option",{value:"equals",children:"Equals"}),(0,s.jsx)("option",{value:"contains",children:"Contains"}),(0,s.jsx)("option",{value:"startsWith",children:"Starts With"}),(0,s.jsx)("option",{value:"endsWith",children:"Ends With"}),(0,s.jsx)("option",{value:"regex",children:"Regex"})]})]})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{fontSize:"sm",color:U.colors.text,children:"Filter Value"}),(0,s.jsx)(k.p,{value:e.value,onChange:e=>eh(o,{value:e.target.value}),placeholder:"channel"===e.type?"general or {channel.name}":"role"===e.type?"Member or {role.name}":"user"===e.type?"username or {user.id}":"regex"===e.type?"^Hello.*":"content"===e.type?"hello world":"Filter value",bg:U.colors.background,color:U.colors.text,borderColor:U.colors.border,size:"sm"})]}),(0,s.jsx)(m.E,{fontSize:"xs",color:U.colors.textSecondary,children:null==(r=ev.find(o=>o.value===e.type))?void 0:r.description})]})]},o)}),(!_.filters||0===_.filters.length)&&(0,s.jsxs)(C.F,{status:"info",borderRadius:"md",children:[(0,s.jsx)(S._,{}),(0,s.jsx)(w.T,{children:"No filters configured. This event will trigger for all occurrences of the selected event type."})]})]})]})}),(0,s.jsx)(et.K,{children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsx)(m.E,{fontSize:"lg",fontWeight:"bold",color:U.colors.text,children:"Event Settings"}),(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(c.z,{spacing:4,children:[(0,s.jsx)(ed.d,{isChecked:_.ignoreBot,onChange:e=>ei({ignoreBot:e.target.checked}),colorScheme:"green"}),(0,s.jsxs)(a.T,{align:"start",spacing:0,children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:U.colors.text,children:"Ignore Bot Messages"}),(0,s.jsx)(m.E,{fontSize:"xs",color:U.colors.textSecondary,children:"Don't trigger on messages from bots (recommended)"})]})]}),(0,s.jsxs)(c.z,{spacing:4,children:[(0,s.jsx)(ed.d,{isChecked:_.ignoreSystem,onChange:e=>ei({ignoreSystem:e.target.checked}),colorScheme:"green"}),(0,s.jsxs)(a.T,{align:"start",spacing:0,children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:U.colors.text,children:"Ignore System Messages"}),(0,s.jsx)(m.E,{fontSize:"xs",color:U.colors.textSecondary,children:"Don't trigger on Discord system messages"})]})]}),(0,s.jsxs)(c.z,{spacing:4,children:[(0,s.jsx)(ed.d,{isChecked:_.rateLimited,onChange:e=>ei({rateLimited:e.target.checked}),colorScheme:"orange"}),(0,s.jsxs)(a.T,{align:"start",spacing:0,children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:U.colors.text,children:"Rate Limited"}),(0,s.jsx)(m.E,{fontSize:"xs",color:U.colors.textSecondary,children:"Limit how often this event can trigger"})]})]}),_.rateLimited&&(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:U.colors.text,children:"Rate Limit (milliseconds)"}),(0,s.jsxs)(ex.Q7,{value:_.rateLimit||1e3,onChange:e=>ei({rateLimit:parseInt(e)||1e3}),min:100,max:6e4,children:[(0,s.jsx)(ex.OO,{bg:U.colors.background,color:U.colors.text,borderColor:U.colors.border}),(0,s.jsxs)(ex.lw,{children:[(0,s.jsx)(ex.Q0,{}),(0,s.jsx)(ex.Sh,{})]})]}),(0,s.jsx)(m.E,{fontSize:"xs",color:U.colors.textSecondary,mt:1,children:"Minimum time between triggers (1000ms = 1 second)"})]})]})]})}),(0,s.jsx)(et.K,{children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsx)(m.E,{fontSize:"lg",fontWeight:"bold",color:U.colors.text,children:"Advanced Settings"}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:U.colors.text,children:"Event Priority"}),(0,s.jsxs)(ex.Q7,{value:_.priority||1,onChange:e=>ei({priority:parseInt(e)||1}),min:1,max:10,children:[(0,s.jsx)(ex.OO,{bg:U.colors.background,color:U.colors.text,borderColor:U.colors.border}),(0,s.jsxs)(ex.lw,{children:[(0,s.jsx)(ex.Q0,{}),(0,s.jsx)(ex.Sh,{})]})]}),(0,s.jsx)(m.E,{fontSize:"xs",color:U.colors.textSecondary,mt:1,children:"Higher priority events execute first (1 = highest, 10 = lowest)"})]}),(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(c.z,{spacing:4,children:[(0,s.jsx)(ed.d,{isChecked:_.async,onChange:e=>ei({async:e.target.checked}),colorScheme:"green"}),(0,s.jsxs)(a.T,{align:"start",spacing:0,children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:U.colors.text,children:"Async Processing"}),(0,s.jsx)(m.E,{fontSize:"xs",color:U.colors.textSecondary,children:"Don't wait for this event to complete before processing others"})]})]}),(0,s.jsxs)(c.z,{spacing:4,children:[(0,s.jsx)(ed.d,{isChecked:_.retryOnError,onChange:e=>ei({retryOnError:e.target.checked}),colorScheme:"red"}),(0,s.jsxs)(a.T,{align:"start",spacing:0,children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:U.colors.text,children:"Retry on Error"}),(0,s.jsx)(m.E,{fontSize:"xs",color:U.colors.textSecondary,children:"Automatically retry if event processing fails"})]})]}),_.retryOnError&&(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:U.colors.text,children:"Max Retries"}),(0,s.jsxs)(ex.Q7,{value:_.maxRetries||3,onChange:e=>ei({maxRetries:parseInt(e)||3}),min:1,max:10,children:[(0,s.jsx)(ex.OO,{bg:U.colors.background,color:U.colors.text,borderColor:U.colors.border}),(0,s.jsxs)(ex.lw,{children:[(0,s.jsx)(ex.Q0,{}),(0,s.jsx)(ex.Sh,{})]})]}),(0,s.jsx)(m.E,{fontSize:"xs",color:U.colors.textSecondary,mt:1,children:"Maximum number of retry attempts"})]})]})]})})]})]}),(0,s.jsx)(u.$,{colorScheme:"green",onClick:()=>{O.eventType=_.eventType,O.description=_.description,O.filters=_.filters,O.ignoreBot=_.ignoreBot,O.ignoreSystem=_.ignoreSystem,O.rateLimited=_.rateLimited,O.rateLimit=_.rateLimit,O.priority=_.priority,O.async=_.async,O.retryOnError=_.retryOnError,O.maxRetries=_.maxRetries,O.channelRestrictions=_.channelRestrictions,O.roleRestrictions=_.roleRestrictions,O.label=_.eventType?ea(_.eventType):"Event",P()},size:"lg",width:"full",children:"Save Configuration"})]})})]})]})]})});ey.displayName="EventNode";let eC=[{value:"sendMessage",label:"\uD83D\uDCAC Send Message",category:"Message"},{value:"sendEmbed",label:"\uD83D\uDCCB Send Embed",category:"Message"},{value:"editMessage",label:"✏️ Edit Message",category:"Message"},{value:"deleteMessage",label:"\uD83D\uDDD1️ Delete Message",category:"Message"},{value:"addReaction",label:"\uD83D\uDC4D Add Reaction",category:"Message"},{value:"removeReaction",label:"\uD83D\uDC4E Remove Reaction",category:"Message"},{value:"addRole",label:"\uD83C\uDFAD Add Role",category:"Roles"},{value:"removeRole",label:"\uD83C\uDFAD Remove Role",category:"Roles"},{value:"kickUser",label:"\uD83D\uDC62 Kick User",category:"Moderation"},{value:"banUser",label:"\uD83D\uDD28 Ban User",category:"Moderation"},{value:"timeoutUser",label:"⏰ Timeout User",category:"Moderation"},{value:"unbanUser",label:"\uD83D\uDD13 Unban User",category:"Moderation"},{value:"createChannel",label:"\uD83D\uDCFA Create Channel",category:"Channel"},{value:"deleteChannel",label:"\uD83D\uDDD1️ Delete Channel",category:"Channel"},{value:"lockChannel",label:"\uD83D\uDD12 Lock Channel",category:"Channel"},{value:"unlockChannel",label:"\uD83D\uDD13 Unlock Channel",category:"Channel"},{value:"sendDM",label:"\uD83D\uDCEC Send DM",category:"Message"},{value:"createThread",label:"\uD83E\uDDF5 Create Thread",category:"Channel"},{value:"pinMessage",label:"\uD83D\uDCCC Pin Message",category:"Message"},{value:"unpinMessage",label:"\uD83D\uDCCC Unpin Message",category:"Message"}],eS={user:[{name:"{user.id}",description:"User ID",icon:"\uD83C\uDD94"},{name:"{user.username}",description:"Username",icon:"\uD83D\uDC64"},{name:"{user.displayName}",description:"Display Name",icon:"\uD83D\uDCDD"},{name:"{user.tag}",description:"User Tag (username#0000)",icon:"\uD83C\uDFF7️"},{name:"{user.mention}",description:"User Mention (<@id>)",icon:"\uD83D\uDCE2"},{name:"{user.avatar}",description:"Avatar URL",icon:"\uD83D\uDDBC️"},{name:"{user.createdAt}",description:"Account Creation Date",icon:"\uD83D\uDCC5"},{name:"{user.joinedAt}",description:"Server Join Date",icon:"\uD83D\uDEAA"},{name:"{user.roles}",description:"User Roles",icon:"\uD83C\uDFAD"},{name:"{user.permissions}",description:"User Permissions",icon:"\uD83D\uDD10"}],channel:[{name:"{channel.id}",description:"Channel ID",icon:"\uD83C\uDD94"},{name:"{channel.name}",description:"Channel Name",icon:"\uD83D\uDCFA"},{name:"{channel.mention}",description:"Channel Mention (<#id>)",icon:"\uD83D\uDCE2"},{name:"{channel.type}",description:"Channel Type",icon:"\uD83D\uDCCB"},{name:"{channel.topic}",description:"Channel Topic",icon:"\uD83D\uDCAC"},{name:"{channel.memberCount}",description:"Member Count",icon:"\uD83D\uDC65"},{name:"{channel.createdAt}",description:"Channel Creation Date",icon:"\uD83D\uDCC5"}],server:[{name:"{server.id}",description:"Server ID",icon:"\uD83C\uDD94"},{name:"{server.name}",description:"Server Name",icon:"\uD83C\uDFE0"},{name:"{server.icon}",description:"Server Icon URL",icon:"\uD83D\uDDBC️"},{name:"{server.memberCount}",description:"Member Count",icon:"\uD83D\uDC65"},{name:"{server.createdAt}",description:"Server Creation Date",icon:"\uD83D\uDCC5"},{name:"{server.owner}",description:"Server Owner",icon:"\uD83D\uDC51"},{name:"{server.boostLevel}",description:"Server Boost Level",icon:"\uD83D\uDE80"},{name:"{server.boostCount}",description:"Server Boost Count",icon:"\uD83D\uDC8E"}],message:[{name:"{message.id}",description:"Message ID",icon:"\uD83C\uDD94"},{name:"{message.content}",description:"Message Content",icon:"\uD83D\uDCAC"},{name:"{message.author}",description:"Message Author",icon:"\uD83D\uDC64"},{name:"{message.channel}",description:"Message Channel",icon:"\uD83D\uDCFA"},{name:"{message.createdAt}",description:"Message Creation Date",icon:"\uD83D\uDCC5"},{name:"{message.editedAt}",description:"Message Edit Date",icon:"✏️"},{name:"{message.reactions}",description:"Message Reactions",icon:"\uD83D\uDC4D"},{name:"{message.attachments}",description:"Message Attachments",icon:"\uD83D\uDCCE"}],api:[{name:"{response.data}",description:"API Response Data",icon:"\uD83D\uDCCA"},{name:"{response.status}",description:"HTTP Status Code",icon:"\uD83D\uDD22"},{name:"{response.headers}",description:"Response Headers",icon:"\uD83D\uDCCB"},{name:"{response.message}",description:"Response Message",icon:"\uD83D\uDCAC"},{name:"{response.error}",description:"Error Message",icon:"❌"}],random:[{name:"{random.number}",description:"Random Number (1-100)",icon:"\uD83C\uDFB2"},{name:"{random.uuid}",description:"Random UUID",icon:"\uD83C\uDD94"},{name:"{random.choice}",description:"Random Choice from Array",icon:"\uD83C\uDFAF"},{name:"{random.color}",description:"Random Hex Color",icon:"\uD83C\uDFA8"}],date:[{name:"{date.now}",description:"Current Date/Time",icon:"⏰"},{name:"{date.today}",description:"Today's Date",icon:"\uD83D\uDCC5"},{name:"{date.timestamp}",description:"Unix Timestamp",icon:"\uD83D\uDD50"},{name:"{date.iso}",description:"ISO Date String",icon:"\uD83D\uDCDD"}]},ew=(0,n.memo)(e=>{var o,r,t,h,x,R,A,M,O,W,I,F,U,N,L,P,_,H,J,G;let{data:ei,selected:ea,id:eh,updateNodeData:em}=e,{currentScheme:eu}=(0,V.DP)(),{isOpen:ep,onOpen:eg,onClose:eb}=(0,l.j)(),[ej,ef]=(0,n.useState)(()=>({embed:{fields:[],author:{name:""},footer:{text:""}},...ei})),[ev,ey]=(0,n.useState)(!1),[ew,eE]=(0,n.useState)(!1),[eT,ez]=(0,n.useState)(null),[ek,eD]=(0,n.useState)(!1),eR=e=>{ef(o=>({...o,...e}))},eA=e=>{var o;return(null==(o=eC.find(o=>o.value===e))?void 0:o.label)||e},eM=e=>{navigator.clipboard.writeText(e)},eO=async()=>{if(!eT&&!ek){eD(!0);try{let e=await fetch("/api/admin/experimental/addon-builder/guild-data");if(e.ok){let o=await e.json();ez({channels:o.channels,roles:o.roles,members:o.members})}}catch(e){}finally{eD(!1)}}};(0,n.useEffect)(()=>{ep&&eO()},[ep]);let eW=(e,o,r)=>{var s;let n=[...(null==(s=ej.embed)?void 0:s.fields)||[]];n[e]={...n[e],[o]:r},eR({embed:{...ej.embed,fields:n}})},eI=e=>{var o;let r=((null==(o=ej.embed)?void 0:o.fields)||[]).filter((o,r)=>r!==e);eR({embed:{...ej.embed,fields:r}})};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(i.a,{bg:eu.colors.surface,border:"2px solid ".concat(ea?"#a855f7":eu.colors.border),borderRadius:"md",p:2,minW:"140px",maxW:"180px",boxShadow:"sm",position:"relative",_hover:{boxShadow:"md",transform:"translateY(-1px)"},transition:"all 0.2s",children:[(0,s.jsx)(q.h7,{type:"target",position:q.yX.Top,style:{background:"#a855f7",border:"2px solid ".concat(eu.colors.surface),width:"12px",height:"12px",top:"-6px",left:"50%",transform:"translateX(-50%)"}}),(0,s.jsxs)(a.T,{spacing:1,align:"stretch",children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",children:[(0,s.jsxs)(c.z,{spacing:1,children:[(0,s.jsx)(i.a,{bg:"purple.500",color:"white",borderRadius:"full",p:.5,fontSize:"xs",children:(0,s.jsx)(B.x_j,{})}),(0,s.jsx)(m.E,{fontSize:"xs",fontWeight:"bold",color:eu.colors.text,children:"Action"})]}),(0,s.jsx)(d.K,{icon:(0,s.jsx)(B.VSk,{}),size:"xs",variant:"ghost",onClick:eg,"aria-label":"Configure action"})]}),(0,s.jsx)(i.a,{children:(0,s.jsxs)(c.z,{spacing:1,children:[ej.actionType&&(0,s.jsx)(m.E,{fontSize:"xs",children:(e=>{let o=eC.find(o=>o.value===e);return(null==o?void 0:o.label.split(" ")[0])||"\uD83C\uDFAF"})(ej.actionType)}),(0,s.jsx)(m.E,{fontSize:"xs",color:eu.colors.text,noOfLines:1,children:ej.actionType?eA(ej.actionType).split(" ").slice(1).join(" "):"Select Action"})]})}),ej.message&&(0,s.jsx)(i.a,{children:(0,s.jsx)(m.E,{fontSize:"xs",color:eu.colors.textSecondary,noOfLines:1,children:ej.message.length>20?ej.message.substring(0,20)+"...":ej.message})}),(0,s.jsxs)(c.z,{spacing:1,flexWrap:"wrap",children:[ej.channel&&(0,s.jsxs)(p.E,{size:"xs",colorScheme:"purple",children:["#",ej.channel]}),ej.role&&(0,s.jsxs)(p.E,{size:"xs",colorScheme:"purple",children:["@",ej.role]}),(null==(o=ej.embed)?void 0:o.title)&&(0,s.jsx)(p.E,{size:"xs",colorScheme:"blue",children:"\uD83D\uDCCB Embed"})]})]})]}),(0,s.jsxs)(g.aF,{isOpen:ep,onClose:()=>{em&&eh&&em(eh,ej),eb()},size:"4xl",children:[(0,s.jsx)(b.m,{bg:"blackAlpha.600"}),(0,s.jsxs)(j.$,{bg:eu.colors.background,border:"2px solid",borderColor:"purple.400",maxW:"1200px",children:[(0,s.jsx)(f.r,{color:eu.colors.text,children:"\uD83C\uDFAF Configure Action"}),(0,s.jsx)(v.s,{}),(0,s.jsx)(y.c,{pb:6,children:(0,s.jsxs)(a.T,{spacing:6,align:"stretch",children:[(0,s.jsxs)(i.a,{children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",mb:2,children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:eu.colors.text,children:"Available Variables"}),(0,s.jsxs)(u.$,{size:"sm",variant:"ghost",leftIcon:ev?(0,s.jsx)(B._NO,{}):(0,s.jsx)(B.Vap,{}),onClick:()=>ey(!ev),children:[ev?"Hide":"Show"," Variables"]})]}),(0,s.jsxs)(C.F,{status:"info",borderRadius:"md",mb:2,children:[(0,s.jsx)(S._,{}),(0,s.jsx)(w.T,{fontSize:"sm",children:"\uD83D\uDCA1 Use variables in your actions! Click any variable below to copy it. Variables are replaced with actual values when your addon runs."})]}),(0,s.jsx)(K.S,{in:ev,animateOpacity:!0,children:(0,s.jsx)(i.a,{bg:eu.colors.surface,border:"1px solid",borderColor:eu.colors.border,borderRadius:"md",p:4,mt:3,maxH:"400px",overflowY:"auto",children:(0,s.jsx)($.n,{allowMultiple:!0,children:Object.entries(eS).map(e=>{let[o,r]=e;return(0,s.jsxs)(X.A,{border:"none",children:[(0,s.jsxs)(Y.J,{px:0,py:2,children:[(0,s.jsx)(i.a,{flex:"1",textAlign:"left",children:(0,s.jsxs)(m.E,{fontSize:"sm",fontWeight:"bold",color:eu.colors.text,textTransform:"capitalize",children:[o," Variables"]})}),(0,s.jsx)(Q.Q,{})]}),(0,s.jsx)(Z.v,{px:0,py:2,children:(0,s.jsx)(a.T,{spacing:2,align:"stretch",children:r.map(e=>(0,s.jsxs)(c.z,{spacing:2,p:2,bg:eu.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:eu.colors.surface},onClick:()=>eM(e.name),children:[(0,s.jsx)(m.E,{fontSize:"sm",children:e.icon}),(0,s.jsx)(ee.C,{fontSize:"xs",colorScheme:"blue",children:e.name}),(0,s.jsx)(m.E,{fontSize:"xs",color:eu.colors.textSecondary,flex:"1",children:e.description}),(0,s.jsx)(d.K,{icon:(0,s.jsx)(B.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable",onClick:o=>{o.stopPropagation(),eM(e.name)}})]},e.name))})})]},o)})})})})]}),(0,s.jsx)(eo.c,{}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:eu.colors.text,children:"Action Type"}),(0,s.jsx)(ec.l,{value:ej.actionType||"",onChange:e=>eR({actionType:e.target.value}),placeholder:"Select an action type",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border,children:Object.entries(eC.reduce((e,o)=>(e[o.category]||(e[o.category]=[]),e[o.category].push(o),e),{})).map(e=>{let[o,r]=e;return(0,s.jsx)("optgroup",{label:o,children:r.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))},o)})})]}),ej.actionType&&(0,s.jsx)(s.Fragment,{children:"sendEmbed"===ej.actionType?(0,s.jsxs)(er.t,{variant:"enclosed",colorScheme:"purple",children:[(0,s.jsxs)(es.w,{children:[(0,s.jsx)(en.o,{children:"Embed Builder"}),(0,s.jsx)(en.o,{children:"Preview"})]}),(0,s.jsxs)(el.T,{children:[(0,s.jsx)(et.K,{children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:eu.colors.text,children:"Channel"}),(0,s.jsxs)(a.T,{spacing:2,align:"stretch",children:[(0,s.jsx)(ec.l,{value:ej.channel||"",onChange:e=>eR({channel:e.target.value}),placeholder:ek?"Loading channels...":"Select a channel",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border,isDisabled:ek,children:null==eT?void 0:eT.channels.filter(e=>"text"===e.type).map(e=>(0,s.jsxs)("option",{value:e.name,children:["#",e.name]},e.id))}),(0,s.jsx)(k.p,{value:ej.channel||"",onChange:e=>eR({channel:e.target.value}),placeholder:"Or type: general or {channel.name}",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border,size:"sm"})]})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:eu.colors.text,children:"Message Content (appears above embed)"}),(0,s.jsx)(D.T,{value:ej.message||"",onChange:e=>eR({message:e.target.value}),placeholder:"Hello {user.username}! This text appears above the embed...",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border,minH:"100px"})]}),(0,s.jsxs)(E.r,{columns:2,spacing:4,children:[(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:eu.colors.text,children:"Embed Title"}),(0,s.jsx)(k.p,{value:(null==(r=ej.embed)?void 0:r.title)||"",onChange:e=>eR({embed:{...ej.embed,title:e.target.value}}),placeholder:"Welcome to {server.name}!",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:eu.colors.text,children:"Embed Color"}),(0,s.jsxs)(a.T,{spacing:2,align:"stretch",children:[(0,s.jsxs)(c.z,{spacing:2,children:[(0,s.jsx)(k.p,{type:"color",value:(null==(t=ej.embed)?void 0:t.color)||"#5865F2",onChange:e=>eR({embed:{...ej.embed,color:e.target.value}}),w:"60px",h:"40px",p:1,bg:eu.colors.background,borderColor:eu.colors.border}),(0,s.jsx)(k.p,{value:(null==(h=ej.embed)?void 0:h.color)||"",onChange:e=>eR({embed:{...ej.embed,color:e.target.value}}),placeholder:"#5865F2",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border,flex:"1"})]}),(0,s.jsx)(c.z,{spacing:1,flexWrap:"wrap",children:["#5865F2","#57F287","#FEE75C","#EB459E","#ED4245","#FF6B35","#00ADB5","#9B59B6"].map(e=>(0,s.jsx)(u.$,{size:"xs",bg:e,w:"30px",h:"20px",minW:"30px",p:0,onClick:()=>eR({embed:{...ej.embed,color:e}}),_hover:{transform:"scale(1.1)"},border:"1px solid",borderColor:eu.colors.border},e))})]})]})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:eu.colors.text,children:"Embed Description"}),(0,s.jsx)(D.T,{value:(null==(x=ej.embed)?void 0:x.description)||"",onChange:e=>eR({embed:{...ej.embed,description:e.target.value}}),placeholder:"This is the description that appears inside the embed...",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border,minH:"100px"})]}),(0,s.jsxs)(E.r,{columns:2,spacing:4,children:[(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:eu.colors.text,children:"Thumbnail URL"}),(0,s.jsx)(k.p,{value:(null==(R=ej.embed)?void 0:R.thumbnail)||"",onChange:e=>eR({embed:{...ej.embed,thumbnail:e.target.value}}),placeholder:"https://example.com/image.png",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:eu.colors.text,children:"Image URL"}),(0,s.jsx)(k.p,{value:(null==(A=ej.embed)?void 0:A.image)||"",onChange:e=>eR({embed:{...ej.embed,image:e.target.value}}),placeholder:"https://example.com/image.png",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border})]})]}),(0,s.jsxs)(i.a,{children:[(0,s.jsx)(z.l,{color:eu.colors.text,children:"Author"}),(0,s.jsxs)(a.T,{spacing:2,align:"stretch",children:[(0,s.jsx)(k.p,{value:(null==(O=ej.embed)||null==(M=O.author)?void 0:M.name)||"",onChange:e=>{var o;return eR({embed:{...ej.embed,author:{...null==(o=ej.embed)?void 0:o.author,name:e.target.value}}})},placeholder:"Author name",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border}),(0,s.jsxs)(E.r,{columns:2,spacing:2,children:[(0,s.jsx)(k.p,{value:(null==(I=ej.embed)||null==(W=I.author)?void 0:W.url)||"",onChange:e=>{var o;return eR({embed:{...ej.embed,author:{...null==(o=ej.embed)?void 0:o.author,url:e.target.value}}})},placeholder:"Author URL",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border}),(0,s.jsx)(k.p,{value:(null==(U=ej.embed)||null==(F=U.author)?void 0:F.iconUrl)||"",onChange:e=>{var o;return eR({embed:{...ej.embed,author:{...null==(o=ej.embed)?void 0:o.author,iconUrl:e.target.value}}})},placeholder:"Author icon URL",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border})]})]})]}),(0,s.jsxs)(i.a,{children:[(0,s.jsx)(z.l,{color:eu.colors.text,children:"Footer"}),(0,s.jsxs)(a.T,{spacing:2,align:"stretch",children:[(0,s.jsx)(k.p,{value:(null==(L=ej.embed)||null==(N=L.footer)?void 0:N.text)||"",onChange:e=>{var o;return eR({embed:{...ej.embed,footer:{...null==(o=ej.embed)?void 0:o.footer,text:e.target.value}}})},placeholder:"Footer text",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border}),(0,s.jsx)(k.p,{value:(null==(_=ej.embed)||null==(P=_.footer)?void 0:P.iconUrl)||"",onChange:e=>{var o;return eR({embed:{...ej.embed,footer:{...null==(o=ej.embed)?void 0:o.footer,iconUrl:e.target.value}}})},placeholder:"Footer icon URL",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border})]})]}),(0,s.jsxs)(i.a,{children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",mb:2,children:[(0,s.jsx)(z.l,{color:eu.colors.text,mb:0,children:"Embed Fields"}),(0,s.jsx)(u.$,{size:"sm",leftIcon:(0,s.jsx)(B.GGD,{}),onClick:()=>{var e;let o=(null==(e=ej.embed)?void 0:e.fields)||[];eR({embed:{...ej.embed,fields:[...o,{name:"",value:"",inline:!1}]}})},colorScheme:"blue",children:"Add Field"})]}),(0,s.jsx)(a.T,{spacing:3,align:"stretch",children:null==(J=ej.embed)||null==(H=J.fields)?void 0:H.map((e,o)=>(0,s.jsxs)(i.a,{p:3,bg:eu.colors.surface,borderRadius:"md",border:"1px solid",borderColor:eu.colors.border,children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",mb:2,children:[(0,s.jsxs)(m.E,{fontSize:"sm",fontWeight:"bold",color:eu.colors.text,children:["Field ",o+1]}),(0,s.jsx)(d.K,{icon:(0,s.jsx)(B.IXo,{}),size:"xs",colorScheme:"red",variant:"ghost",onClick:()=>eI(o),"aria-label":"Remove field"})]}),(0,s.jsxs)(a.T,{spacing:2,align:"stretch",children:[(0,s.jsx)(k.p,{value:e.name,onChange:e=>eW(o,"name",e.target.value),placeholder:"Field name",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border}),(0,s.jsx)(D.T,{value:e.value,onChange:e=>eW(o,"value",e.target.value),placeholder:"Field value",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border,minH:"80px"}),(0,s.jsxs)(c.z,{children:[(0,s.jsx)(ed.d,{isChecked:e.inline||!1,onChange:e=>eW(o,"inline",e.target.checked),colorScheme:"purple"}),(0,s.jsx)(m.E,{fontSize:"sm",color:eu.colors.text,children:"Display inline"})]})]})]},o))})]}),(0,s.jsxs)(c.z,{children:[(0,s.jsx)(ed.d,{isChecked:(null==(G=ej.embed)?void 0:G.timestamp)||!1,onChange:e=>eR({embed:{...ej.embed,timestamp:e.target.checked}}),colorScheme:"purple"}),(0,s.jsx)(m.E,{fontSize:"sm",color:eu.colors.text,children:"Show current timestamp"})]})]})}),(0,s.jsx)(et.K,{children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsx)(m.E,{fontSize:"lg",fontWeight:"bold",color:eu.colors.text,children:"Embed Preview"}),(0,s.jsxs)(C.F,{status:"info",borderRadius:"md",children:[(0,s.jsx)(S._,{}),(0,s.jsx)(w.T,{fontSize:"sm",children:"This shows your message content (above) and embed (below) as they will appear in Discord. Variables will be replaced with actual values when sent."})]}),(()=>{var e,o;let r=ej.embed||{};return(0,s.jsxs)(a.T,{spacing:3,align:"stretch",maxW:"500px",children:[ej.message&&(0,s.jsxs)(i.a,{bg:eu.colors.surface,border:"1px solid",borderColor:eu.colors.border,borderRadius:"md",p:3,children:[(0,s.jsx)(m.E,{fontSize:"sm",color:eu.colors.text,fontWeight:"medium",children:"\uD83D\uDCE9 Message Content:"}),(0,s.jsx)(m.E,{fontSize:"sm",color:eu.colors.text,mt:1,children:ej.message})]}),(0,s.jsxs)(i.a,{bg:eu.colors.surface,border:"1px solid",borderColor:eu.colors.border,borderRadius:"md",p:4,borderLeft:"4px solid ".concat(r.color||"#5865F2"),children:[(null==(e=r.author)?void 0:e.name)&&(0,s.jsxs)(c.z,{spacing:2,mb:2,children:[r.author.iconUrl&&(0,s.jsx)(i.a,{w:6,h:6,borderRadius:"full",bg:"gray.300",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"xs",children:"\uD83D\uDC64"}),(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:eu.colors.text,children:r.author.name})]}),r.title&&(0,s.jsx)(m.E,{fontSize:"md",fontWeight:"bold",color:eu.colors.text,mb:2,children:r.title}),r.description&&(0,s.jsx)(m.E,{fontSize:"sm",color:eu.colors.textSecondary,mb:3,children:r.description}),r.fields&&r.fields.length>0&&(0,s.jsx)(a.T,{spacing:2,align:"stretch",mb:3,children:r.fields.map((e,o)=>(0,s.jsxs)(i.a,{children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:eu.colors.text,children:e.name}),(0,s.jsx)(m.E,{fontSize:"sm",color:eu.colors.textSecondary,children:e.value})]},o))}),(null==(o=r.footer)?void 0:o.text)&&(0,s.jsxs)(c.z,{spacing:2,mt:3,pt:2,borderTop:"1px solid",borderColor:eu.colors.border,children:[r.footer.iconUrl&&(0,s.jsx)(i.a,{w:4,h:4,borderRadius:"full",bg:"gray.300",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"xs",children:"ℹ️"}),(0,s.jsx)(m.E,{fontSize:"xs",color:eu.colors.textSecondary,children:r.footer.text})]}),r.timestamp&&(0,s.jsxs)(m.E,{fontSize:"xs",color:eu.colors.textSecondary,mt:2,children:["\uD83D\uDD52 ",new Date().toLocaleString()]})]})]})})()]})})]})]}):(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[("sendMessage"===ej.actionType||"sendDM"===ej.actionType)&&(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:eu.colors.text,children:"Message Content"}),(0,s.jsx)(D.T,{value:ej.message||"",onChange:e=>eR({message:e.target.value}),placeholder:"Hello {user.username}! Welcome to {server.name}!",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border,minH:"100px"})]}),"sendDM"!==ej.actionType&&(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:eu.colors.text,children:"Channel"}),(0,s.jsxs)(a.T,{spacing:2,align:"stretch",children:[(0,s.jsx)(ec.l,{value:ej.channel||"",onChange:e=>eR({channel:e.target.value}),placeholder:ek?"Loading channels...":"Select a channel",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border,isDisabled:ek,children:null==eT?void 0:eT.channels.filter(e=>"text"===e.type).map(e=>(0,s.jsxs)("option",{value:e.name,children:["#",e.name]},e.id))}),(0,s.jsx)(k.p,{value:ej.channel||"",onChange:e=>eR({channel:e.target.value}),placeholder:"Or type: general or {channel.name}",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border,size:"sm"})]})]})]}),("addRole"===ej.actionType||"removeRole"===ej.actionType)&&(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:eu.colors.text,children:"Role Name"}),(0,s.jsxs)(a.T,{spacing:2,align:"stretch",children:[(0,s.jsx)(ec.l,{value:ej.role||"",onChange:e=>eR({role:e.target.value}),placeholder:ek?"Loading roles...":"Select a role",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border,isDisabled:ek,children:null==eT?void 0:eT.roles.map(e=>(0,s.jsxs)("option",{value:e.name,children:["@",e.name]},e.id))}),(0,s.jsx)(k.p,{value:ej.role||"",onChange:e=>eR({role:e.target.value}),placeholder:"Or type: Member or {user.role}",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border,size:"sm"})]})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:eu.colors.text,children:"Reason"}),(0,s.jsx)(k.p,{value:ej.reason||"",onChange:e=>eR({reason:e.target.value}),placeholder:"Role updated by bot",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border})]})]}),("kickUser"===ej.actionType||"banUser"===ej.actionType)&&(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:eu.colors.text,children:"User"}),(0,s.jsxs)(a.T,{spacing:2,align:"stretch",children:[(0,s.jsx)(ec.l,{value:ej.user||"",onChange:e=>eR({user:e.target.value}),placeholder:ek?"Loading members...":"Select a user",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border,isDisabled:ek,children:null==eT?void 0:eT.members.map(e=>(0,s.jsxs)("option",{value:e.username,children:[e.displayName," (@",e.username,")"]},e.id))}),(0,s.jsx)(k.p,{value:ej.user||"",onChange:e=>eR({user:e.target.value}),placeholder:"Or type: username or {user.id}",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border,size:"sm"})]})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:eu.colors.text,children:"Reason"}),(0,s.jsx)(k.p,{value:ej.reason||"",onChange:e=>eR({reason:e.target.value}),placeholder:"Violation of server rules",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border})]}),"banUser"===ej.actionType&&(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:eu.colors.text,children:"Delete Message History"}),(0,s.jsx)(ed.d,{isChecked:ej.deleteMessages||!1,onChange:e=>eR({deleteMessages:e.target.checked}),colorScheme:"purple"})]})]}),"timeoutUser"===ej.actionType&&(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:eu.colors.text,children:"User"}),(0,s.jsxs)(a.T,{spacing:2,align:"stretch",children:[(0,s.jsx)(ec.l,{value:ej.user||"",onChange:e=>eR({user:e.target.value}),placeholder:ek?"Loading members...":"Select a user",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border,isDisabled:ek,children:null==eT?void 0:eT.members.map(e=>(0,s.jsxs)("option",{value:e.username,children:[e.displayName," (@",e.username,")"]},e.id))}),(0,s.jsx)(k.p,{value:ej.user||"",onChange:e=>eR({user:e.target.value}),placeholder:"Or type: username or {user.id}",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border,size:"sm"})]})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:eu.colors.text,children:"Duration (minutes)"}),(0,s.jsxs)(ex.Q7,{value:ej.duration||10,onChange:e=>eR({duration:parseInt(e)||10}),min:1,max:40320,children:[(0,s.jsx)(ex.OO,{bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border}),(0,s.jsxs)(ex.lw,{children:[(0,s.jsx)(ex.Q0,{}),(0,s.jsx)(ex.Sh,{})]})]})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:eu.colors.text,children:"Reason"}),(0,s.jsx)(k.p,{value:ej.reason||"",onChange:e=>eR({reason:e.target.value}),placeholder:"Timeout for spam",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border})]})]}),"addReaction"===ej.actionType&&(0,s.jsx)(a.T,{spacing:4,align:"stretch",children:(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:eu.colors.text,children:"Reaction (emoji)"}),(0,s.jsx)(k.p,{value:ej.reaction||"",onChange:e=>eR({reaction:e.target.value}),placeholder:"\uD83D\uDC4D or :thumbsup:",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border})]})}),"createChannel"===ej.actionType&&(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:eu.colors.text,children:"Channel Name"}),(0,s.jsx)(k.p,{value:ej.channelName||"",onChange:e=>eR({channelName:e.target.value}),placeholder:"new-channel",bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:eu.colors.text,children:"Channel Type"}),(0,s.jsxs)(ec.l,{value:ej.channelType||"text",onChange:e=>eR({channelType:e.target.value}),bg:eu.colors.background,color:eu.colors.text,borderColor:eu.colors.border,children:[(0,s.jsx)("option",{value:"text",children:"Text Channel"}),(0,s.jsx)("option",{value:"voice",children:"Voice Channel"}),(0,s.jsx)("option",{value:"category",children:"Category"})]})]})]})]})}),(0,s.jsx)(u.$,{colorScheme:"purple",onClick:()=>{ei.actionType=ej.actionType,ei.message=ej.message,ei.channel=ej.channel,ei.role=ej.role,ei.user=ej.user,ei.embed=ej.embed,ei.reason=ej.reason,ei.duration=ej.duration,ei.deleteMessages=ej.deleteMessages,ei.reaction=ej.reaction,ei.channelName=ej.channelName,ei.channelType=ej.channelType,ei.label=ej.actionType?eA(ej.actionType):"Action",eb()},size:"lg",width:"full",children:"Save Configuration"})]})})]})]})]})});ew.displayName="ActionNode";let eE={user:[{name:"{user.id}",description:"User ID",icon:"\uD83C\uDD94"},{name:"{user.username}",description:"Username",icon:"\uD83D\uDC64"},{name:"{user.displayName}",description:"Display Name",icon:"\uD83D\uDCDD"},{name:"{user.roles}",description:"User Roles (array)",icon:"\uD83C\uDFAD"},{name:"{user.permissions}",description:"User Permissions (array)",icon:"\uD83D\uDD10"},{name:"{user.isBot}",description:"Is Bot (true/false)",icon:"\uD83E\uDD16"},{name:"{user.createdAt}",description:"Account Creation Date",icon:"\uD83D\uDCC5"},{name:"{user.joinedAt}",description:"Server Join Date",icon:"\uD83D\uDEAA"}],channel:[{name:"{channel.id}",description:"Channel ID",icon:"\uD83C\uDD94"},{name:"{channel.name}",description:"Channel Name",icon:"\uD83D\uDCFA"},{name:"{channel.type}",description:"Channel Type",icon:"\uD83D\uDCCB"},{name:"{channel.nsfw}",description:"Is NSFW (true/false)",icon:"\uD83D\uDD1E"},{name:"{channel.memberCount}",description:"Member Count (number)",icon:"\uD83D\uDC65"}],server:[{name:"{server.id}",description:"Server ID",icon:"\uD83C\uDD94"},{name:"{server.name}",description:"Server Name",icon:"\uD83C\uDFE0"},{name:"{server.memberCount}",description:"Total Members (number)",icon:"\uD83D\uDC65"},{name:"{server.boostLevel}",description:"Boost Level (number)",icon:"\uD83D\uDE80"},{name:"{server.owner}",description:"Server Owner ID",icon:"\uD83D\uDC51"}],message:[{name:"{message.content}",description:"Message Content",icon:"\uD83D\uDCAC"},{name:"{message.length}",description:"Message Length (number)",icon:"\uD83D\uDCCF"},{name:"{message.mentions}",description:"Message Mentions (array)",icon:"\uD83D\uDCE2"},{name:"{message.attachments}",description:"Attachments Count (number)",icon:"\uD83D\uDCCE"},{name:"{message.embeds}",description:"Embeds Count (number)",icon:"\uD83D\uDCCB"}],api:[{name:"{response.status}",description:"HTTP Status Code (number)",icon:"\uD83D\uDD22"},{name:"{response.data}",description:"Response Data",icon:"\uD83D\uDCCA"},{name:"{response.error}",description:"Error Message",icon:"❌"},{name:"{response.length}",description:"Response Array Length",icon:"\uD83D\uDCCF"}],time:[{name:"{time.hour}",description:"Current Hour (0-23)",icon:"\uD83D\uDD50"},{name:"{time.day}",description:"Day of Week (0-6)",icon:"\uD83D\uDCC5"},{name:"{time.date}",description:"Current Date",icon:"\uD83D\uDCC6"},{name:"{time.timestamp}",description:"Unix Timestamp",icon:"⏰"}],random:[{name:"{random.number}",description:"Random Number (1-100)",icon:"\uD83C\uDFB2"},{name:"{random.boolean}",description:"Random True/False",icon:"\uD83C\uDFAF"}]},eT=[{value:"userHasRole",label:"\uD83C\uDFAD User Has Role",category:"User",description:"Check if user has a specific role"},{value:"userIsAdmin",label:"\uD83D\uDC51 User Is Admin",category:"User",description:"Check if user is server admin"},{value:"userHasPermission",label:"\uD83D\uDD10 User Has Permission",category:"User",description:"Check if user has specific permission"},{value:"userIsBot",label:"\uD83E\uDD16 User Is Bot",category:"User",description:"Check if user is a bot"},{value:"userJoinedRecently",label:"\uD83D\uDEAA User Joined Recently",category:"User",description:"Check if user joined within timeframe"},{value:"messageContains",label:"\uD83D\uDCAC Message Contains",category:"Message",description:"Check if message contains text"},{value:"messageLength",label:"\uD83D\uDCCF Message Length",category:"Message",description:"Check message character count"},{value:"messageHasMentions",label:"\uD83D\uDCE2 Message Has Mentions",category:"Message",description:"Check if message mentions users/roles"},{value:"messageHasAttachments",label:"\uD83D\uDCCE Message Has Attachments",category:"Message",description:"Check if message has files"},{value:"messageHasEmbeds",label:"\uD83D\uDCCB Message Has Embeds",category:"Message",description:"Check if message has embeds"},{value:"channelType",label:"\uD83D\uDCFA Channel Type",category:"Channel",description:"Check channel type (text, voice, etc.)"},{value:"channelIsNSFW",label:"\uD83D\uDD1E Channel Is NSFW",category:"Channel",description:"Check if channel is NSFW"},{value:"channelMemberCount",label:"\uD83D\uDC65 Channel Member Count",category:"Channel",description:"Check voice channel member count"},{value:"serverMemberCount",label:"\uD83D\uDC65 Server Member Count",category:"Server",description:"Check total server members"},{value:"serverBoostLevel",label:"\uD83D\uDE80 Server Boost Level",category:"Server",description:"Check server boost level"},{value:"serverName",label:"\uD83C\uDFE0 Server Name",category:"Server",description:"Check server name"},{value:"timeOfDay",label:"\uD83D\uDD50 Time of Day",category:"Time",description:"Check current hour of day"},{value:"dayOfWeek",label:"\uD83D\uDCC5 Day of Week",category:"Time",description:"Check day of the week"},{value:"apiResponseStatus",label:"\uD83D\uDD22 API Response Status",category:"API",description:"Check HTTP status code"},{value:"apiResponseData",label:"\uD83D\uDCCA API Response Data",category:"API",description:"Check API response content"},{value:"customVariable",label:"⚙️ Custom Variable",category:"Custom",description:"Check custom variable value"},{value:"randomChance",label:"\uD83C\uDFB2 Random Chance",category:"Custom",description:"Random percentage chance"}],ez=[{value:"equals",label:"= Equals",description:"Exact match"},{value:"notEquals",label:"≠ Not Equals",description:"Does not match"},{value:"contains",label:"\uD83D\uDD0D Contains",description:"Contains substring"},{value:"notContains",label:"\uD83D\uDEAB Not Contains",description:"Does not contain substring"},{value:"startsWith",label:"▶️ Starts With",description:"Begins with text"},{value:"endsWith",label:"◀️ Ends With",description:"Ends with text"},{value:"greaterThan",label:"> Greater Than",description:"Numeric greater than"},{value:"lessThan",label:"< Less Than",description:"Numeric less than"},{value:"greaterEqual",label:"≥ Greater or Equal",description:"Numeric greater than or equal"},{value:"lessEqual",label:"≤ Less or Equal",description:"Numeric less than or equal"},{value:"regex",label:"\uD83D\uDD0D Regex Match",description:"Regular expression pattern"},{value:"inArray",label:"\uD83D\uDCCB In Array",description:"Value exists in array"},{value:"hasLength",label:"\uD83D\uDCCF Has Length",description:"Array/string has specific length"}],ek=(0,n.memo)(e=>{var o,r;let{data:t,selected:h,id:x,updateNodeData:R}=e,{currentScheme:A}=(0,V.DP)(),{isOpen:M,onOpen:O,onClose:W}=(0,l.j)(),[I,F]=(0,n.useState)(()=>({operator:"equals",logicalOperator:"AND",caseSensitive:!1,conditions:[],...t})),[U,N]=(0,n.useState)(!1),L=e=>{F(o=>({...o,...e}))},P=e=>{let o=eT.find(o=>o.value===e);return o?o.label.split(" ").slice(1).join(" "):e},_=e=>{navigator.clipboard.writeText(e)};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(i.a,{bg:A.colors.surface,border:"2px solid ".concat(h?"#f59e0b":A.colors.border),borderRadius:"md",p:2,minW:"140px",maxW:"180px",boxShadow:"sm",position:"relative",_hover:{boxShadow:"md",transform:"translateY(-1px)"},transition:"all 0.2s",children:[(0,s.jsx)(q.h7,{type:"target",position:q.yX.Top,style:{background:"#f59e0b",border:"2px solid ".concat(A.colors.surface),width:"12px",height:"12px",top:"-6px",left:"50%",transform:"translateX(-50%)"}}),(0,s.jsxs)(a.T,{spacing:1,align:"stretch",children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",children:[(0,s.jsxs)(c.z,{spacing:1,children:[(0,s.jsx)(i.a,{bg:"orange.500",color:"white",borderRadius:"full",p:.5,fontSize:"xs",children:(0,s.jsx)(B.lrG,{})}),(0,s.jsx)(m.E,{fontSize:"xs",fontWeight:"bold",color:A.colors.text,children:"Condition"})]}),(0,s.jsx)(d.K,{icon:(0,s.jsx)(B.VSk,{}),size:"xs",variant:"ghost",onClick:O,"aria-label":"Configure condition"})]}),(0,s.jsx)(i.a,{children:(0,s.jsxs)(c.z,{spacing:1,children:[I.conditionType&&(0,s.jsx)(m.E,{fontSize:"xs",children:(e=>{let o=eT.find(o=>o.value===e);return o?o.label.split(" ")[0]:"❓"})(I.conditionType)}),(0,s.jsx)(m.E,{fontSize:"xs",color:A.colors.text,noOfLines:1,children:I.conditionType?P(I.conditionType):"Select Condition"})]})}),I.operator&&I.value&&(0,s.jsx)(i.a,{children:(0,s.jsxs)(m.E,{fontSize:"xs",color:A.colors.textSecondary,noOfLines:1,children:[(e=>{var o;return(null==(o=ez.find(o=>o.value===e))?void 0:o.label)||e})(I.operator).split(" ").slice(1).join(" "),' "',I.value.length>15?I.value.substring(0,15)+"...":I.value,'"']})}),(0,s.jsxs)(c.z,{spacing:1,justify:"space-between",children:[(0,s.jsx)(p.E,{size:"xs",colorScheme:"green",children:"TRUE"}),(0,s.jsx)(p.E,{size:"xs",colorScheme:"red",children:"FALSE"})]})]}),(0,s.jsx)(q.h7,{type:"source",position:q.yX.Bottom,id:"true",style:{background:"#38a169",border:"2px solid ".concat(A.colors.surface),width:"12px",height:"12px",bottom:"-6px",left:"25%",transform:"translateX(-50%)"}}),(0,s.jsx)(q.h7,{type:"source",position:q.yX.Bottom,id:"false",style:{background:"#e53e3e",border:"2px solid ".concat(A.colors.surface),width:"12px",height:"12px",bottom:"-6px",left:"75%",transform:"translateX(-50%)"}})]}),(0,s.jsxs)(g.aF,{isOpen:M,onClose:()=>{R&&x&&R(x,I),W()},size:"4xl",children:[(0,s.jsx)(b.m,{bg:"blackAlpha.600"}),(0,s.jsxs)(j.$,{bg:A.colors.background,border:"2px solid",borderColor:"orange.400",maxW:"1200px",children:[(0,s.jsx)(f.r,{color:A.colors.text,children:"❓ Configure Condition"}),(0,s.jsx)(v.s,{}),(0,s.jsx)(y.c,{pb:6,children:(0,s.jsxs)(a.T,{spacing:6,align:"stretch",children:[(0,s.jsxs)(i.a,{children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",mb:2,children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:A.colors.text,children:"Available Variables"}),(0,s.jsxs)(u.$,{size:"sm",variant:"ghost",leftIcon:U?(0,s.jsx)(B._NO,{}):(0,s.jsx)(B.Vap,{}),onClick:()=>N(!U),children:[U?"Hide":"Show"," Variables"]})]}),(0,s.jsxs)(C.F,{status:"info",borderRadius:"md",mb:2,children:[(0,s.jsx)(S._,{}),(0,s.jsx)(w.T,{fontSize:"sm",children:"\uD83D\uDCA1 Use variables in your conditions! Click any variable below to copy it. Conditions determine which path (TRUE or FALSE) the flow takes."})]}),(0,s.jsx)(K.S,{in:U,animateOpacity:!0,children:(0,s.jsx)(i.a,{bg:A.colors.surface,border:"1px solid",borderColor:A.colors.border,borderRadius:"md",p:4,mt:3,maxH:"400px",overflowY:"auto",children:(0,s.jsx)($.n,{allowMultiple:!0,children:Object.entries(eE).map(e=>{let[o,r]=e;return(0,s.jsxs)(X.A,{border:"none",children:[(0,s.jsxs)(Y.J,{px:0,py:2,children:[(0,s.jsx)(i.a,{flex:"1",textAlign:"left",children:(0,s.jsxs)(m.E,{fontSize:"sm",fontWeight:"bold",color:A.colors.text,textTransform:"capitalize",children:[o," Variables"]})}),(0,s.jsx)(Q.Q,{})]}),(0,s.jsx)(Z.v,{px:0,py:2,children:(0,s.jsx)(a.T,{spacing:2,align:"stretch",children:r.map(e=>(0,s.jsxs)(c.z,{spacing:2,p:2,bg:A.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:A.colors.surface},onClick:()=>_(e.name),children:[(0,s.jsx)(m.E,{fontSize:"sm",children:e.icon}),(0,s.jsx)(ee.C,{fontSize:"xs",colorScheme:"orange",children:e.name}),(0,s.jsx)(m.E,{fontSize:"xs",color:A.colors.textSecondary,flex:"1",children:e.description}),(0,s.jsx)(d.K,{icon:(0,s.jsx)(B.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable",onClick:o=>{o.stopPropagation(),_(e.name)}})]},e.name))})})]},o)})})})})]}),(0,s.jsx)(eo.c,{}),(0,s.jsxs)(er.t,{variant:"enclosed",colorScheme:"orange",children:[(0,s.jsxs)(es.w,{children:[(0,s.jsx)(en.o,{children:"Basic Condition"}),(0,s.jsx)(en.o,{children:"Advanced"})]}),(0,s.jsxs)(el.T,{children:[(0,s.jsx)(et.K,{children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(T.MJ,{isRequired:!0,children:[(0,s.jsx)(z.l,{color:A.colors.text,children:"Condition Type"}),(0,s.jsx)(ec.l,{value:I.conditionType||"",onChange:e=>L({conditionType:e.target.value}),placeholder:"Select a condition type",bg:A.colors.background,color:A.colors.text,borderColor:A.colors.border,children:Object.entries(eT.reduce((e,o)=>(e[o.category]||(e[o.category]=[]),e[o.category].push(o),e),{})).map(e=>{let[o,r]=e;return(0,s.jsx)("optgroup",{label:o,children:r.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))},o)})})]}),I.conditionType&&(0,s.jsxs)(C.F,{status:"info",borderRadius:"md",children:[(0,s.jsx)(S._,{}),(0,s.jsxs)(i.a,{children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",mb:1,children:null==(o=eT.find(e=>e.value===I.conditionType))?void 0:o.label}),(0,s.jsx)(m.E,{fontSize:"sm",children:null==(r=eT.find(e=>e.value===I.conditionType))?void 0:r.description})]})]}),(0,s.jsxs)(E.r,{columns:2,spacing:4,children:[(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:A.colors.text,children:"Operator"}),(0,s.jsx)(ec.l,{value:I.operator||"equals",onChange:e=>L({operator:e.target.value}),bg:A.colors.background,color:A.colors.text,borderColor:A.colors.border,children:ez.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:A.colors.text,children:"Compare Value"}),(0,s.jsx)(k.p,{value:I.value||"",onChange:e=>L({value:e.target.value}),placeholder:"userHasRole"===I.conditionType?"Member or {user.roles}":"messageContains"===I.conditionType?"hello or {message.content}":"serverMemberCount"===I.conditionType?"100 or {server.memberCount}":"timeOfDay"===I.conditionType?"14 (for 2 PM)":"Value to compare against",bg:A.colors.background,color:A.colors.text,borderColor:A.colors.border})]})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:A.colors.text,children:"Description"}),(0,s.jsx)(D.T,{value:I.description||"",onChange:e=>L({description:e.target.value}),placeholder:"Describe what this condition checks for",bg:A.colors.background,color:A.colors.text,borderColor:A.colors.border,minH:"80px"})]}),(0,s.jsxs)(C.F,{status:"warning",borderRadius:"md",children:[(0,s.jsx)(S._,{}),(0,s.jsxs)(i.a,{children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",mb:1,children:"Understanding TRUE/FALSE Paths"}),(0,s.jsx)(m.E,{fontSize:"sm",children:"• **TRUE (Left)**: Actions that run when the condition passes • **FALSE (Right)**: Actions that run when the condition fails • You can connect different actions to each path"})]})]})]})}),(0,s.jsx)(et.K,{children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsx)(m.E,{fontSize:"lg",fontWeight:"bold",color:A.colors.text,children:"Advanced Settings"}),(0,s.jsx)(a.T,{spacing:4,align:"stretch",children:(0,s.jsxs)(c.z,{spacing:4,children:[(0,s.jsx)(ed.d,{isChecked:I.caseSensitive,onChange:e=>L({caseSensitive:e.target.checked}),colorScheme:"orange"}),(0,s.jsxs)(a.T,{align:"start",spacing:0,children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:A.colors.text,children:"Case Sensitive"}),(0,s.jsx)(m.E,{fontSize:"xs",color:A.colors.textSecondary,children:"Match exact capitalization for text comparisons"})]})]})}),(0,s.jsxs)(C.F,{status:"info",borderRadius:"md",children:[(0,s.jsx)(S._,{}),(0,s.jsxs)(i.a,{children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",mb:2,children:"\uD83D\uDCA1 Condition Examples:"}),(0,s.jsxs)(a.T,{align:"start",spacing:1,fontSize:"sm",children:[(0,s.jsx)(m.E,{children:'• Check if user has "Admin" role: **User Has Role** equals "Admin"'}),(0,s.jsx)(m.E,{children:'• Check if message contains swear word: **Message Contains** contains "badword"'}),(0,s.jsx)(m.E,{children:"• Check if server has many members: **Server Member Count** greater than 1000"}),(0,s.jsx)(m.E,{children:"• Check if it's nighttime: **Time of Day** greater than 22"}),(0,s.jsx)(m.E,{children:"• Check API status: **API Response Status** equals 200"})]})]})]}),(0,s.jsxs)(C.F,{status:"warning",borderRadius:"md",children:[(0,s.jsx)(S._,{}),(0,s.jsxs)(i.a,{children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",mb:2,children:"⚠️ Important Notes:"}),(0,s.jsxs)(a.T,{align:"start",spacing:1,fontSize:"sm",children:[(0,s.jsxs)(m.E,{children:["• Use variables like ","{user.roles}"," to check dynamic values"]}),(0,s.jsx)(m.E,{children:"• Number comparisons work with Greater/Less Than operators"}),(0,s.jsx)(m.E,{children:"• Text comparisons work with Contains, Starts With, etc."}),(0,s.jsx)(m.E,{children:"• Always connect both TRUE and FALSE paths for complete logic"})]})]})]})]})})]})]}),(0,s.jsx)(u.$,{colorScheme:"orange",onClick:()=>{t.conditionType=I.conditionType,t.operator=I.operator,t.value=I.value,t.caseSensitive=I.caseSensitive,t.description=I.description,t.logicalOperator=I.logicalOperator,t.label=I.conditionType?P(I.conditionType):"Condition",W()},size:"lg",width:"full",children:"Save Configuration"})]})})]})]})]})});ek.displayName="ConditionNode";let eD=(0,n.memo)(e=>{let{data:o,selected:r}=e,{currentScheme:n}=(0,V.DP)();return(0,s.jsxs)(i.a,{bg:n.colors.surface,border:"2px solid ".concat(r?n.colors.primary:n.colors.border),borderRadius:"full",p:2,minW:"80px",minH:"80px",boxShadow:"lg",position:"relative",display:"flex",alignItems:"center",justifyContent:"center",_hover:{boxShadow:"xl",transform:"scale(1.05)",borderColor:n.colors.primary},transition:"all 0.2s",children:[(0,s.jsxs)(a.T,{spacing:1,align:"center",children:[(0,s.jsx)(i.a,{bg:n.colors.primary,color:"white",borderRadius:"full",p:1,fontSize:"sm",boxShadow:"sm",children:(0,s.jsx)(B.aze,{})}),(0,s.jsx)(m.E,{fontSize:"xs",fontWeight:"bold",color:n.colors.text,textAlign:"center",children:o.label})]}),(0,s.jsx)(q.h7,{type:"source",position:q.yX.Bottom,style:{background:n.colors.background,border:"2px solid ".concat(n.colors.primary),width:"16px",height:"16px",boxShadow:"0 2px 4px rgba(0,0,0,0.2)",bottom:"-8px",left:"50%",transform:"translateX(-50%)"}})]})});eD.displayName="TriggerNode";var eR=r(98703),eA=r(57561);let eM={user:[{name:"{user.id}",description:"User ID for authentication",icon:"\uD83C\uDD94"},{name:"{user.username}",description:"Username for requests",icon:"\uD83D\uDC64"},{name:"{user.token}",description:"User auth token",icon:"\uD83D\uDD11"},{name:"{user.email}",description:"User email address",icon:"\uD83D\uDCE7"}],server:[{name:"{server.id}",description:"Server ID",icon:"\uD83C\uDFE0"},{name:"{server.name}",description:"Server name",icon:"\uD83D\uDCDD"},{name:"{server.memberCount}",description:"Member count",icon:"\uD83D\uDC65"},{name:"{server.region}",description:"Server region",icon:"\uD83C\uDF0D"}],message:[{name:"{message.id}",description:"Message ID",icon:"\uD83D\uDCAC"},{name:"{message.content}",description:"Message content",icon:"\uD83D\uDCDD"},{name:"{message.channelId}",description:"Channel ID",icon:"\uD83D\uDCFA"},{name:"{message.authorId}",description:"Author ID",icon:"\uD83D\uDC64"}],response:[{name:"{response.data}",description:"Full response data",icon:"\uD83D\uDCCA"},{name:"{response.status}",description:"HTTP status code",icon:"\uD83D\uDD22"},{name:"{response.headers}",description:"Response headers",icon:"\uD83D\uDCCB"},{name:"{response.error}",description:"Error message if failed",icon:"❌"}],time:[{name:"{time.now}",description:"Current timestamp",icon:"⏰"},{name:"{time.iso}",description:"ISO timestamp",icon:"\uD83D\uDCC5"},{name:"{time.unix}",description:"Unix timestamp",icon:"\uD83D\uDD50"}],random:[{name:"{random.uuid}",description:"Random UUID",icon:"\uD83C\uDFB2"},{name:"{random.number}",description:"Random number",icon:"\uD83D\uDD22"},{name:"{random.string}",description:"Random string",icon:"\uD83D\uDD24"}]},eO=[{value:"GET",label:"GET",description:"Retrieve data from server",color:"green"},{value:"POST",label:"POST",description:"Send data to create new resource",color:"blue"},{value:"PUT",label:"PUT",description:"Update existing resource",color:"orange"},{value:"PATCH",label:"PATCH",description:"Partially update resource",color:"yellow"},{value:"DELETE",label:"DELETE",description:"Remove resource from server",color:"red"},{value:"HEAD",label:"HEAD",description:"Get headers only",color:"purple"},{value:"OPTIONS",label:"OPTIONS",description:"Get allowed methods",color:"gray"}],eW=[{value:"json",label:"JSON",description:"JavaScript Object Notation"},{value:"form",label:"Form Data",description:"URL-encoded form data"},{value:"text",label:"Plain Text",description:"Raw text content"},{value:"xml",label:"XML",description:"Extensible Markup Language"}],eI=[{value:"ignore",label:"Ignore Errors",description:"Continue flow on API errors"},{value:"log",label:"Log Errors",description:"Log errors but continue"},{value:"throw",label:"Throw Errors",description:"Stop flow on API errors"},{value:"retry",label:"Retry on Error",description:"Retry failed requests"}],eF=[{key:"Authorization",value:"Bearer {user.token}"},{key:"Content-Type",value:"application/json"},{key:"User-Agent",value:"Discord Bot API Client"},{key:"Accept",value:"application/json"},{key:"X-API-Key",value:"{api.key}"}],eU=(0,n.memo)(e=>{var o,r,h,x,R,A,M;let{data:O,selected:W,id:I,updateNodeData:F}=e,{currentScheme:U}=(0,V.DP)(),{isOpen:N,onOpen:L,onClose:P}=(0,l.j)(),_=(0,t.d)(),[H,J]=(0,n.useState)(()=>({method:"GET",headers:[],bodyType:"json",timeout:5e3,errorHandling:"log",retryCount:0,retryDelay:1e3,followRedirects:!0,validateSSL:!0,...O})),[G,ei]=(0,n.useState)(!1),[ea,eh]=(0,n.useState)(null),[em,eu]=(0,n.useState)(null),[ep,eg]=(0,n.useState)([]),[eb,ej]=(0,n.useState)(!1),ef=e=>{J(o=>({...o,...e}))},ev=e=>{navigator.clipboard.writeText(e),_({title:"Copied!",description:"Variable ".concat(e," copied to clipboard"),status:"success",duration:2e3,isClosable:!0})},ey=async()=>{if(!H.url){eu("Please enter a URL first"),_({title:"Test Failed",description:"Please enter a URL first",status:"error",duration:3e3,isClosable:!0});return}ei(!0),eu(null),eh(null);try{var e;let o,r={};null==(e=H.headers)||e.forEach(e=>{e.key&&e.value&&(r[e.key]=e.value)}),H.body&&("POST"===H.method||"PUT"===H.method||"PATCH"===H.method)&&("json"===H.bodyType?r["Content-Type"]="application/json":"form"===H.bodyType?r["Content-Type"]="application/x-www-form-urlencoded":"xml"===H.bodyType&&(r["Content-Type"]="application/xml"));let s={method:H.method||"GET",headers:r};if(H.body&&("POST"===H.method||"PUT"===H.method||"PATCH"===H.method))if("json"===H.bodyType)try{JSON.parse(H.body),s.body=H.body}catch(e){throw Error("Invalid JSON in request body")}else s.body=H.body;let n=await fetch(H.url,s),l=await n.text();try{o=JSON.parse(l)}catch(e){o=l}if(!n.ok)throw Error("HTTP ".concat(n.status,": ").concat(n.statusText));eh({status:n.status,statusText:n.statusText,headers:Object.fromEntries(n.headers.entries()),data:o});let t=eC(o);eg(t),_({title:"API Test Successful!",description:"Request completed with status ".concat(n.status),status:"success",duration:3e3,isClosable:!0})}catch(o){let e=o instanceof Error?o.message:"Request failed";eu(e),_({title:"API Test Failed",description:e,status:"error",duration:5e3,isClosable:!0})}finally{ei(!1)}},eC=function(e){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=[];return e&&"object"==typeof e&&(Array.isArray(e)?e.forEach((e,s)=>{let n=o?"".concat(o,".").concat(s):"".concat(s);r.push(n),"object"==typeof e&&null!==e&&r.push(...eC(e,n))}):Object.keys(e).forEach(s=>{let n=o?"".concat(o,".").concat(s):s;r.push(n),"object"==typeof e[s]&&null!==e[s]&&r.push(...eC(e[s],n))})),r},eS=(e,o,r)=>{let s=[...H.headers||[]];s[e][o]=r,ef({headers:s})},ew=e=>{ef({headers:(H.headers||[]).filter((o,r)=>r!==e)})},eE=e=>{ef({headers:[...H.headers||[],e]})};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(i.a,{bg:U.colors.surface,border:"2px solid ".concat(W?"#06b6d4":U.colors.border),borderRadius:"md",p:2,minW:"140px",maxW:"180px",boxShadow:"sm",position:"relative",_hover:{boxShadow:"md",transform:"translateY(-1px)"},transition:"all 0.2s",children:[(0,s.jsx)(q.h7,{type:"target",position:q.yX.Top,style:{background:"#06b6d4",border:"2px solid ".concat(U.colors.surface),width:"12px",height:"12px",top:"-6px",left:"50%",transform:"translateX(-50%)"}}),(0,s.jsxs)(a.T,{spacing:1,align:"stretch",children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",children:[(0,s.jsxs)(c.z,{spacing:1,children:[(0,s.jsx)(i.a,{bg:"teal.500",color:"white",borderRadius:"full",p:.5,fontSize:"xs",children:(0,s.jsx)(B.VeH,{})}),(0,s.jsx)(m.E,{fontSize:"xs",fontWeight:"bold",color:U.colors.text,children:"API Request"})]}),(0,s.jsx)(d.K,{icon:(0,s.jsx)(B.VSk,{}),size:"xs",variant:"ghost",onClick:L,"aria-label":"Configure API request"})]}),(0,s.jsx)(i.a,{children:(0,s.jsxs)(c.z,{spacing:1,children:[H.method&&(0,s.jsx)(m.E,{fontSize:"xs",children:(e=>{switch(e){case"GET":return"\uD83D\uDCE5";case"POST":return"\uD83D\uDCE4";case"PUT":return"\uD83D\uDD04";case"PATCH":return"✏️";case"DELETE":return"\uD83D\uDDD1️";default:return"\uD83C\uDF10"}})(H.method)}),(0,s.jsxs)(m.E,{fontSize:"xs",color:U.colors.text,noOfLines:1,children:[H.method||"GET"," Request"]})]})}),H.url&&(0,s.jsx)(i.a,{children:(0,s.jsx)(m.E,{fontSize:"xs",color:U.colors.textSecondary,noOfLines:1,children:H.url.length>25?H.url.substring(0,25)+"...":H.url})}),(0,s.jsxs)(c.z,{spacing:1,flexWrap:"wrap",children:[H.method&&(0,s.jsx)(p.E,{size:"xs",colorScheme:(e=>{let o=eO.find(o=>o.value===e);return(null==o?void 0:o.color)||"gray"})(H.method),children:H.method}),(null!=(A=null==(o=H.headers)?void 0:o.length)?A:0)>0&&(0,s.jsxs)(p.E,{size:"xs",colorScheme:"blue",children:[null==(r=H.headers)?void 0:r.length," header",(null!=(M=null==(h=H.headers)?void 0:h.length)?M:0)!==1?"s":""]}),H.saveToVariable&&(0,s.jsx)(p.E,{size:"xs",colorScheme:"green",children:"Saves Data"})]})]}),(0,s.jsx)(q.h7,{type:"source",position:q.yX.Bottom,style:{background:"#06b6d4",border:"2px solid ".concat(U.colors.surface),width:"12px",height:"12px",bottom:"-6px",left:"50%",transform:"translateX(-50%)"}})]}),(0,s.jsxs)(g.aF,{isOpen:N,onClose:()=>{F&&I&&F(I,H),P()},size:"6xl",children:[(0,s.jsx)(b.m,{bg:"blackAlpha.600"}),(0,s.jsxs)(j.$,{bg:U.colors.background,border:"2px solid",borderColor:"teal.400",maxW:"1400px",children:[(0,s.jsx)(f.r,{color:U.colors.text,children:"\uD83C\uDF10 Configure API Request"}),(0,s.jsx)(v.s,{}),(0,s.jsx)(y.c,{pb:6,children:(0,s.jsxs)(a.T,{spacing:6,align:"stretch",children:[(0,s.jsxs)(i.a,{children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",mb:2,children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:U.colors.text,children:"Available Variables"}),(0,s.jsxs)(u.$,{size:"sm",variant:"ghost",leftIcon:eb?(0,s.jsx)(B._NO,{}):(0,s.jsx)(B.Vap,{}),onClick:()=>ej(!eb),children:[eb?"Hide":"Show"," Variables"]})]}),(0,s.jsxs)(C.F,{status:"info",borderRadius:"md",mb:2,children:[(0,s.jsx)(S._,{}),(0,s.jsx)(w.T,{fontSize:"sm",children:"\uD83D\uDCA1 Use variables in your API requests! Click any variable below to copy it. Variables are replaced with actual values when the request executes."})]}),(0,s.jsx)(K.S,{in:eb,animateOpacity:!0,children:(0,s.jsx)(i.a,{bg:U.colors.surface,border:"1px solid",borderColor:U.colors.border,borderRadius:"md",p:4,mt:3,maxH:"400px",overflowY:"auto",children:(0,s.jsx)($.n,{allowMultiple:!0,children:Object.entries(eM).map(e=>{let[o,r]=e;return(0,s.jsxs)(X.A,{border:"none",children:[(0,s.jsxs)(Y.J,{px:0,py:2,children:[(0,s.jsx)(i.a,{flex:"1",textAlign:"left",children:(0,s.jsxs)(m.E,{fontSize:"sm",fontWeight:"bold",color:U.colors.text,textTransform:"capitalize",children:[o," Variables"]})}),(0,s.jsx)(Q.Q,{})]}),(0,s.jsx)(Z.v,{px:0,py:2,children:(0,s.jsx)(a.T,{spacing:2,align:"stretch",children:r.map(e=>(0,s.jsxs)(c.z,{spacing:2,p:2,bg:U.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:U.colors.surface},onClick:()=>ev(e.name),children:[(0,s.jsx)(m.E,{fontSize:"sm",children:e.icon}),(0,s.jsx)(ee.C,{fontSize:"xs",colorScheme:"teal",children:e.name}),(0,s.jsx)(m.E,{fontSize:"xs",color:U.colors.textSecondary,flex:"1",children:e.description}),(0,s.jsx)(d.K,{icon:(0,s.jsx)(B.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable",onClick:o=>{o.stopPropagation(),ev(e.name)}})]},e.name))})})]},o)})})})})]}),(0,s.jsx)(eo.c,{}),(0,s.jsxs)(er.t,{variant:"enclosed",colorScheme:"teal",children:[(0,s.jsxs)(es.w,{children:[(0,s.jsx)(en.o,{children:"Request"}),(0,s.jsx)(en.o,{children:"Headers"}),(0,s.jsx)(en.o,{children:"Body"}),(0,s.jsx)(en.o,{children:"Settings"}),(0,s.jsx)(en.o,{children:"Test"})]}),(0,s.jsxs)(el.T,{children:[(0,s.jsx)(et.K,{children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(T.MJ,{isRequired:!0,children:[(0,s.jsx)(z.l,{color:U.colors.text,children:"Request URL"}),(0,s.jsx)(k.p,{value:H.url||"",onChange:e=>ef({url:e.target.value}),placeholder:"https://api.example.com/data or {server.webhook.url}",bg:U.colors.background,color:U.colors.text,borderColor:U.colors.border})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:U.colors.text,children:"HTTP Method"}),(0,s.jsx)(ec.l,{value:H.method||"GET",onChange:e=>ef({method:e.target.value}),bg:U.colors.background,color:U.colors.text,borderColor:U.colors.border,children:eO.map(e=>(0,s.jsxs)("option",{value:e.value,children:[e.label," - ",e.description]},e.value))})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:U.colors.text,children:"Save Response To Variable"}),(0,s.jsx)(k.p,{value:H.saveToVariable||"",onChange:e=>ef({saveToVariable:e.target.value}),placeholder:"response_data (access with {response_data.field})",bg:U.colors.background,color:U.colors.text,borderColor:U.colors.border}),(0,s.jsx)(m.E,{fontSize:"xs",color:U.colors.textSecondary,mt:1,children:"Variable name to store the API response. Leave empty if you don't need the response."})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:U.colors.text,children:"Description"}),(0,s.jsx)(D.T,{value:H.description||"",onChange:e=>ef({description:e.target.value}),placeholder:"Describe what this API request does",bg:U.colors.background,color:U.colors.text,borderColor:U.colors.border,minH:"80px"})]})]})}),(0,s.jsx)(et.K,{children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",children:[(0,s.jsx)(m.E,{fontSize:"lg",fontWeight:"bold",color:U.colors.text,children:"Request Headers"}),(0,s.jsx)(u.$,{leftIcon:(0,s.jsx)(B.GGD,{}),onClick:()=>{ef({headers:[...H.headers||[],{key:"",value:""}]})},colorScheme:"teal",size:"sm",children:"Add Header"})]}),(0,s.jsxs)(C.F,{status:"info",borderRadius:"md",children:[(0,s.jsx)(S._,{}),(0,s.jsx)(w.T,{fontSize:"sm",children:"Headers provide additional information about the request. Common headers are automatically set based on content type."})]}),(0,s.jsxs)(i.a,{children:[(0,s.jsx)(m.E,{fontSize:"md",fontWeight:"bold",color:U.colors.text,mb:2,children:"Quick Add Common Headers:"}),(0,s.jsx)(eR.B,{spacing:2,children:eF.map((e,o)=>(0,s.jsx)(eR.Q,{children:(0,s.jsx)(u.$,{size:"sm",variant:"outline",onClick:()=>eE(e),leftIcon:(0,s.jsx)(B.GGD,{}),children:e.key})},o))})]}),(0,s.jsxs)(a.T,{spacing:3,align:"stretch",children:[null==(x=H.headers)?void 0:x.map((e,o)=>(0,s.jsxs)(i.a,{p:3,bg:U.colors.surface,borderRadius:"md",border:"1px solid",borderColor:U.colors.border,children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",mb:2,children:[(0,s.jsxs)(m.E,{fontSize:"sm",fontWeight:"bold",color:U.colors.text,children:["Header ",o+1]}),(0,s.jsx)(d.K,{icon:(0,s.jsx)(B.IXo,{}),size:"sm",colorScheme:"red",variant:"ghost",onClick:()=>ew(o),"aria-label":"Remove header"})]}),(0,s.jsxs)(E.r,{columns:2,spacing:3,children:[(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{fontSize:"sm",color:U.colors.text,children:"Header Name"}),(0,s.jsx)(k.p,{value:e.key,onChange:e=>eS(o,"key",e.target.value),placeholder:"Authorization, Content-Type, etc.",bg:U.colors.background,color:U.colors.text,borderColor:U.colors.border,size:"sm"})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{fontSize:"sm",color:U.colors.text,children:"Header Value"}),(0,s.jsx)(k.p,{value:e.value,onChange:e=>eS(o,"value",e.target.value),placeholder:"Bearer {user.token}, application/json, etc.",bg:U.colors.background,color:U.colors.text,borderColor:U.colors.border,size:"sm"})]})]})]},o)),(!H.headers||0===H.headers.length)&&(0,s.jsxs)(C.F,{status:"info",borderRadius:"md",children:[(0,s.jsx)(S._,{}),(0,s.jsx)(w.T,{children:"No custom headers configured. Default headers will be set automatically based on request type."})]})]})]})}),(0,s.jsx)(et.K,{children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",children:[(0,s.jsx)(m.E,{fontSize:"lg",fontWeight:"bold",color:U.colors.text,children:"Request Body"}),(0,s.jsx)(m.E,{fontSize:"sm",color:U.colors.textSecondary,children:"Only used for POST, PUT, PATCH requests"})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:U.colors.text,children:"Body Type"}),(0,s.jsx)(ec.l,{value:H.bodyType||"json",onChange:e=>ef({bodyType:e.target.value}),bg:U.colors.background,color:U.colors.text,borderColor:U.colors.border,children:eW.map(e=>(0,s.jsxs)("option",{value:e.value,children:[e.label," - ",e.description]},e.value))})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:U.colors.text,children:"Request Body"}),(0,s.jsx)(D.T,{value:H.body||"",onChange:e=>ef({body:e.target.value}),placeholder:"json"===H.bodyType?'{"key": "value", "user": "{user.id}"}':"form"===H.bodyType?"key=value&user={user.id}":"Raw text content with {variables}",bg:U.colors.background,color:U.colors.text,borderColor:U.colors.border,minH:"200px",fontFamily:"monospace",fontSize:"sm"}),(0,s.jsxs)(m.E,{fontSize:"xs",color:U.colors.textSecondary,mt:1,children:["json"===H.bodyType&&"Must be valid JSON format","form"===H.bodyType&&"Use key=value&key2=value2 format","text"===H.bodyType&&"Plain text content"]})]}),(0,s.jsxs)(C.F,{status:"warning",borderRadius:"md",children:[(0,s.jsx)(S._,{}),(0,s.jsxs)(i.a,{children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",mb:1,children:"\uD83D\uDCA1 Body Examples:"}),(0,s.jsxs)(a.T,{align:"start",spacing:1,fontSize:"sm",fontFamily:"monospace",children:[(0,s.jsxs)(m.E,{children:["JSON: ",'{"message": "{message.content}", "user_id": "{user.id}"}']}),(0,s.jsxs)(m.E,{children:["Form: user_id=","{user.id}","&message=","{message.content}"]}),(0,s.jsxs)(m.E,{children:["Text: User ","{user.username}"," said: ","{message.content}"]})]})]})]})]})}),(0,s.jsx)(et.K,{children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsx)(m.E,{fontSize:"lg",fontWeight:"bold",color:U.colors.text,children:"Advanced Settings"}),(0,s.jsxs)(E.r,{columns:2,spacing:4,children:[(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:U.colors.text,children:"Timeout (milliseconds)"}),(0,s.jsxs)(ex.Q7,{value:H.timeout||5e3,onChange:e=>ef({timeout:parseInt(e)||5e3}),min:1e3,max:6e4,children:[(0,s.jsx)(ex.OO,{bg:U.colors.background,color:U.colors.text,borderColor:U.colors.border}),(0,s.jsxs)(ex.lw,{children:[(0,s.jsx)(ex.Q0,{}),(0,s.jsx)(ex.Sh,{})]})]}),(0,s.jsx)(m.E,{fontSize:"xs",color:U.colors.textSecondary,mt:1,children:"Maximum time to wait for response (5000ms = 5 seconds)"})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:U.colors.text,children:"Error Handling"}),(0,s.jsx)(ec.l,{value:H.errorHandling||"log",onChange:e=>ef({errorHandling:e.target.value}),bg:U.colors.background,color:U.colors.text,borderColor:U.colors.border,children:eI.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))}),(0,s.jsx)(m.E,{fontSize:"xs",color:U.colors.textSecondary,mt:1,children:null==(R=eI.find(e=>e.value===H.errorHandling))?void 0:R.description})]})]}),"retry"===H.errorHandling&&(0,s.jsxs)(E.r,{columns:2,spacing:4,children:[(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:U.colors.text,children:"Retry Count"}),(0,s.jsxs)(ex.Q7,{value:H.retryCount||0,onChange:e=>ef({retryCount:parseInt(e)||0}),min:0,max:5,children:[(0,s.jsx)(ex.OO,{bg:U.colors.background,color:U.colors.text,borderColor:U.colors.border}),(0,s.jsxs)(ex.lw,{children:[(0,s.jsx)(ex.Q0,{}),(0,s.jsx)(ex.Sh,{})]})]})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:U.colors.text,children:"Retry Delay (ms)"}),(0,s.jsxs)(ex.Q7,{value:H.retryDelay||1e3,onChange:e=>ef({retryDelay:parseInt(e)||1e3}),min:500,max:1e4,children:[(0,s.jsx)(ex.OO,{bg:U.colors.background,color:U.colors.text,borderColor:U.colors.border}),(0,s.jsxs)(ex.lw,{children:[(0,s.jsx)(ex.Q0,{}),(0,s.jsx)(ex.Sh,{})]})]})]})]}),(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(c.z,{spacing:4,children:[(0,s.jsx)(ed.d,{isChecked:H.followRedirects,onChange:e=>ef({followRedirects:e.target.checked}),colorScheme:"teal"}),(0,s.jsxs)(a.T,{align:"start",spacing:0,children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:U.colors.text,children:"Follow Redirects"}),(0,s.jsx)(m.E,{fontSize:"xs",color:U.colors.textSecondary,children:"Automatically follow HTTP redirects (3xx responses)"})]})]}),(0,s.jsxs)(c.z,{spacing:4,children:[(0,s.jsx)(ed.d,{isChecked:H.validateSSL,onChange:e=>ef({validateSSL:e.target.checked}),colorScheme:"red"}),(0,s.jsxs)(a.T,{align:"start",spacing:0,children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:U.colors.text,children:"Validate SSL Certificates"}),(0,s.jsx)(m.E,{fontSize:"xs",color:U.colors.textSecondary,children:"Verify SSL certificates (disable only for testing)"})]})]})]})]})}),(0,s.jsx)(et.K,{children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",children:[(0,s.jsx)(m.E,{fontSize:"lg",fontWeight:"bold",color:U.colors.text,children:"Test API Request"}),(0,s.jsx)(u.$,{leftIcon:G?(0,s.jsx)(eA.y,{size:"sm"}):(0,s.jsx)(B.aze,{}),onClick:ey,colorScheme:"teal",isLoading:G,loadingText:"Testing...",isDisabled:!H.url,children:"Test Request"})]}),(0,s.jsxs)(C.F,{status:"warning",borderRadius:"md",children:[(0,s.jsx)(S._,{}),(0,s.jsx)(w.T,{fontSize:"sm",children:"⚠️ This will make a real API request! Variables will not be replaced during testing. Make sure your URL and credentials are correct."})]}),em&&(0,s.jsxs)(C.F,{status:"error",borderRadius:"md",children:[(0,s.jsx)(S._,{}),(0,s.jsxs)(i.a,{children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",mb:1,children:"Request Failed"}),(0,s.jsx)(m.E,{fontSize:"sm",children:em})]})]}),ea&&(0,s.jsxs)(i.a,{children:[(0,s.jsx)(m.E,{fontSize:"md",fontWeight:"bold",color:U.colors.text,mb:2,children:"Response:"}),(0,s.jsx)(i.a,{p:4,bg:U.colors.surface,borderRadius:"md",border:"1px solid",borderColor:U.colors.border,maxH:"400px",overflowY:"auto",children:(0,s.jsxs)(a.T,{spacing:3,align:"stretch",children:[(0,s.jsxs)(c.z,{justify:"space-between",children:[(0,s.jsxs)(p.E,{colorScheme:"green",size:"lg",children:[ea.status," ",ea.statusText]}),(0,s.jsxs)(c.z,{spacing:2,children:[(0,s.jsx)(B.YrT,{color:"green"}),(0,s.jsx)(m.E,{fontSize:"sm",color:"green.500",children:"Success"})]})]}),(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:U.colors.text,children:"Response Data:"}),(0,s.jsx)(i.a,{bg:U.colors.background,p:3,borderRadius:"md",fontFamily:"monospace",fontSize:"xs",overflowX:"auto",children:(0,s.jsx)("pre",{children:JSON.stringify(ea.data,null,2)})}),ep.length>0&&(0,s.jsxs)(i.a,{children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:U.colors.text,mb:2,children:"Available Response Variables:"}),(0,s.jsxs)(a.T,{spacing:1,align:"stretch",maxH:"150px",overflowY:"auto",children:[ep.slice(0,20).map(e=>(0,s.jsxs)(c.z,{spacing:2,p:2,bg:U.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:U.colors.surface},onClick:()=>ev("{response.".concat(e,"}")),children:[(0,s.jsx)(ee.C,{fontSize:"xs",colorScheme:"teal",children:"{response.".concat(e,"}")}),(0,s.jsx)(d.K,{icon:(0,s.jsx)(B.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable"})]},e)),ep.length>20&&(0,s.jsxs)(m.E,{fontSize:"xs",color:U.colors.textSecondary,children:["...and ",ep.length-20," more variables"]})]})]})]})})]})]})})]})]}),(0,s.jsx)(u.$,{colorScheme:"teal",onClick:()=>{O.url=H.url,O.method=H.method,O.headers=H.headers,O.body=H.body,O.bodyType=H.bodyType,O.timeout=H.timeout,O.saveToVariable=H.saveToVariable,O.errorHandling=H.errorHandling,O.description=H.description,O.retryCount=H.retryCount,O.retryDelay=H.retryDelay,O.followRedirects=H.followRedirects,O.validateSSL=H.validateSSL,O.label="".concat(H.method||"GET"," Request"),P()},size:"lg",width:"full",children:"Save Configuration"})]})})]})]})]})});eU.displayName="ApiRequestNode";let eN={gradient:e=>{let{id:o,sourceX:r,sourceY:n,targetX:l,targetY:t,sourcePosition:i,targetPosition:a,style:c,markerEnd:d,data:h,animated:m}=e,x="gradient-".concat(o),[u]=(0,q.oN)({sourceX:r,sourceY:n,sourcePosition:i,targetX:l,targetY:t,targetPosition:a,borderRadius:20});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("defs",{children:(0,s.jsxs)("linearGradient",{id:x,x1:r,y1:n,x2:l,y2:t,gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{offset:"0%",stopColor:(null==h?void 0:h.sourceColor)||"#6b7280"}),(0,s.jsx)("stop",{offset:"100%",stopColor:(null==h?void 0:h.targetColor)||"#6b7280"})]})}),(0,s.jsx)("path",{d:u,stroke:"url(#".concat(x,")"),strokeWidth:2,fill:"none",markerEnd:d,style:c,className:m?"react-flow__edge-path-animated":""})]})}},eL=e=>n.useMemo(()=>{var o;return{trigger:(null==e||null==(o=e.colors)?void 0:o.primary)||"#6b7280",command:"#3b82f6",event:"#10b981",action:"#a855f7",apiRequest:"#06b6d4",condition:"#f59e0b"}},[e]),eP=[{id:"1",type:"trigger",position:{x:100,y:200},data:{label:"Start Here"}}],e_=[],eB=()=>{var e;let{data:o}=(0,H.useSession)(),r=(0,J.useRouter)(),{currentScheme:K}=(0,V.DP)(),$=eL(K),[X,Y,Q]=(0,q.ck)(eP),[Z,ee,eo]=(0,q.fM)(e_),[er,es]=(0,n.useState)(null),[en,el]=(0,n.useState)(!1),et=(0,n.useRef)(null),{isOpen:ei,onOpen:ea,onClose:ec}=(0,l.j)(),{isOpen:ed,onOpen:eh,onClose:em}=(0,l.j)(),{isOpen:ex,onOpen:eu,onClose:ep}=(0,l.j)(),{isOpen:eg,onOpen:ej,onClose:ef}=(0,l.j)(),{isOpen:ev,onOpen:eC,onClose:eS}=(0,l.j)(),{isOpen:eE,onOpen:eT,onClose:ez}=(0,l.j)(),[eR,eA]=(0,n.useState)([]),[eM,eO]=(0,n.useState)([]),[eW,eI]=(0,n.useState)([]),[eF,eB]=(0,n.useState)({name:"",description:"",author:"",version:"1.0.0",tags:[],isPublic:!1}),[eH,eJ]=(0,n.useState)(""),[eV,eq]=(0,n.useState)(null),[eG,eK]=(0,n.useState)(""),[e$,eX]=(0,n.useState)(""),eY=(0,n.useRef)(null),eQ=(0,t.d)(),eZ=(null==o||null==(e=o.user)?void 0:e.id)==="933023999770918932";if(n.useEffect(()=>{o&&!eZ&&r.push("/")},[o,eZ,r]),o&&!eZ)return null;let e0=(0,n.useCallback)((e,o)=>{Y(r=>r.map(r=>r.id===e?{...r,data:{...r.data,...o}}:r))},[Y]),e1=(0,n.useMemo)(()=>({command:e=>(0,s.jsx)(eb,{...e,updateNodeData:e0}),event:e=>(0,s.jsx)(ey,{...e,updateNodeData:e0}),action:e=>(0,s.jsx)(ew,{...e,updateNodeData:e0}),condition:e=>(0,s.jsx)(ek,{...e,updateNodeData:e0}),trigger:eD,apiRequest:e=>(0,s.jsx)(eU,{...e,updateNodeData:e0})}),[e0]),e2=(0,n.useCallback)(e=>{let o=X.find(o=>o.id===e.source),r=X.find(o=>o.id===e.target);if(!o||!r)return;if("trigger"===o.type){let o=Z.filter(o=>o.source===e.source);if(o.length>0){var s;let e=null==(s=X.find(e=>e.id===o[0].target))?void 0:s.type;if(e&&e!==r.type)return void alert("❌ Flow Rule Violation!\n\nYou can only connect Start to EITHER:\n• Command (for slash commands)\n• Event (for automatic triggers)\n\nNot both! Please choose one path.")}if("command"!==r.type&&"event"!==r.type)return void alert("❌ Invalid Connection!\n\nStart can only connect to:\n• Command blocks\n• Event blocks")}if("action"===o.type)return void alert("❌ Invalid Connection!\n\nAction blocks should be at the end of your flow.\nThey cannot connect to other blocks.");let n=$[o.type]||"#6b7280",l=$[r.type]||"#6b7280",t={...e,type:"gradient",animated:!0,markerEnd:{type:q.TG.ArrowClosed,color:l},style:{strokeWidth:2},data:{sourceColor:n,targetColor:l}};ee(e=>(0,q.rN)(t,e))},[ee,X,Z]),e3=(0,n.useCallback)(e=>{e.preventDefault(),e.dataTransfer.dropEffect="move"},[]),e4=(0,n.useCallback)(e=>{var o;e.preventDefault();let r=null==(o=et.current)?void 0:o.getBoundingClientRect(),s=e.dataTransfer.getData("application/reactflow");if(void 0===s||!s||!r)return;let n=null==er?void 0:er.project({x:e.clientX-r.left,y:e.clientY-r.top}),l={id:"".concat(s,"-").concat(Date.now()),type:s,position:n||{x:0,y:0},data:{label:"New ".concat(s)}};Y(e=>e.concat(l))},[er,Y]),e5=async e=>{try{let o={name:e.name,description:e.description,author:e.author,version:e.version,nodes:X,edges:Z},r=await fetch("/api/admin/experimental/addon-builder/build",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});if(r.ok)eQ({title:"Addon Built Successfully!",description:"Your addon has been built and is now available in the bot.",status:"success",duration:5e3,isClosable:!0}),e6();else{let e=await r.json();if(e.errors&&Array.isArray(e.errors)){let o=e.errors.map((e,o)=>"".concat(o+1,". ").concat(e)).join("\n");eQ({title:"Configuration Issues Found",description:"Please fix these issues:\n\n".concat(o),status:"warning",duration:1e4,isClosable:!0})}else eQ({title:"Build Failed",description:e.details||e.message||"Failed to build addon. Please check your flow and try again.",status:"error",duration:5e3,isClosable:!0})}}catch(e){eQ({title:"Build Error",description:"An error occurred while building the addon.",status:"error",duration:5e3,isClosable:!0})}finally{el(!1)}},e6=async()=>{let e=localStorage.getItem("addon-builder-saved-flows"),o=[];if(e)try{o=JSON.parse(e)}catch(e){}let r=[];try{let e=await fetch("/api/admin/addons");if(e.ok)for(let o of((await e.json()).customAddons||[]).filter(e=>e.isCustomAddon))try{var s,n,l;let e=await fetch("/api/admin/addons/".concat(o.name,"/flow")),t=null,i=!1;e.ok&&(t=await e.json(),i=!0),r.push({id:"built-".concat(o.name),name:o.name,description:o.description||"Generated addon from visual builder",author:"Addon Builder",version:o.version||"1.0.0",tags:i?["built-addon","recoverable"]:["built-addon","template-only"],isBuilt:!0,hasOriginalFlow:i,isPublic:!1,createdAt:(null==t||null==(s=t.metadata)?void 0:s.createdAt)||new Date().toISOString(),updatedAt:new Date().toISOString(),nodeCount:(null==t||null==(n=t.nodes)?void 0:n.length)||0,edgeCount:(null==t||null==(l=t.edges)?void 0:l.length)||0,nodes:(null==t?void 0:t.nodes)||[],edges:(null==t?void 0:t.edges)||[]})}catch(e){r.push({id:"built-".concat(o.name),name:o.name,description:o.description||"Generated addon from visual builder",author:"Addon Builder",version:o.version||"1.0.0",tags:["built-addon","template-only"],isBuilt:!0,hasOriginalFlow:!1,isPublic:!1,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),nodeCount:0,edgeCount:0,nodes:[],edges:[]})}}catch(e){}eI([...o,...r])},e8=e=>{let o=(e,o)=>e.map(e=>{let r=o.find(o=>o.id===e.source),s=o.find(o=>o.id===e.target);if(r&&s){let o=$[r.type]||"#6b7280",n=$[s.type]||"#6b7280";return{...e,type:"gradient",animated:!0,markerEnd:{type:q.TG.ArrowClosed,color:n},style:{strokeWidth:2},data:{sourceColor:o,targetColor:n}}}return e});if(e.isBuilt)if(e.hasOriginalFlow&&e.nodes.length>0)Y(e.nodes),ee(o(e.edges,e.nodes)),eQ({title:"Original Flow Recovered!",description:'"'.concat(e.name,'" original flow loaded successfully. You can now edit and rebuild it.'),status:"success",duration:5e3,isClosable:!0});else{let o=[{id:"1",type:"trigger",position:{x:100,y:200},data:{label:"Start Here"}},{id:"info-note",type:"command",position:{x:300,y:200},data:{label:"".concat(e.name," (Template)"),commandName:"example",description:"This is a basic template. The original flow data was not saved when this addon was built."}}],r=[{id:"start-to-command",source:"1",target:"info-note",type:"gradient",animated:!0,markerEnd:{type:q.TG.ArrowClosed,color:$.command},style:{strokeWidth:2},data:{sourceColor:$.trigger,targetColor:$.command}}];Y(o),ee(r),eQ({title:"Template Loaded",description:'"'.concat(e.name,'" basic template loaded. Original flow data not available - this addon was built before flow recovery was implemented.'),status:"warning",duration:6e3,isClosable:!0})}else Y(e.nodes),ee(o(e.edges,e.nodes)),eQ({title:"Flow Loaded!",description:'"'.concat(e.name,'" has been loaded successfully.'),status:"success",duration:3e3,isClosable:!0});ef(),setTimeout(()=>{er&&er.fitView({padding:.2,includeHiddenNodes:!1,minZoom:.5,maxZoom:1.5})},100)},e7=e=>{eq(e)},e9=async()=>{if(eV)if(eq(null),eV.isBuilt)try{let e=await fetch("/api/admin/addons/".concat(eV.name),{method:"DELETE",credentials:"include"});if(!e.ok){let o=await e.json();throw Error(o.details||o.error||"Failed to delete addon")}eI(e=>e.filter(e=>e.id!==eV.id)),eQ({title:"Addon Deleted",description:"The built addon has been deleted successfully.",status:"success",duration:3e3,isClosable:!0})}catch(e){eQ({title:"Delete Failed",description:e.message,status:"error",duration:5e3,isClosable:!0})}else{let e=localStorage.getItem("addon-builder-saved-flows");if(e)try{let o=JSON.parse(e).filter(e=>e.id!==eV.id);localStorage.setItem("addon-builder-saved-flows",JSON.stringify(o)),eI(e=>e.filter(e=>e.id!==eV.id)),eQ({title:"Flow Deleted",description:"The saved flow has been deleted successfully.",status:"info",duration:3e3,isClosable:!0})}catch(e){eQ({title:"Delete Failed",description:"Failed to delete the saved flow.",status:"error",duration:3e3,isClosable:!0})}}},oe=()=>{eH.trim()&&!eF.tags.includes(eH.trim())&&(eB(e=>({...e,tags:[...e.tags,eH.trim()]})),eJ(""))},oo=e=>{eB(o=>({...o,tags:o.tags.filter(o=>o!==e)}))},or=async()=>{try{let e=await fetch("/api/admin/experimental/addon-builder/templates");if(e.ok){let o=await e.json();eA(o.templates),eO(o.categories)}}catch(e){}},os=e=>{Y(e.nodes),ee(((e,o)=>e.map(e=>{let r=o.find(o=>o.id===e.source),s=o.find(o=>o.id===e.target);if(r&&s){let o=$[r.type]||"#6b7280",n=$[s.type]||"#6b7280";return{...e,type:"gradient",animated:!0,markerEnd:{type:q.TG.ArrowClosed,color:n},style:{strokeWidth:2},data:{sourceColor:o,targetColor:n}}}return e}))(e.edges,e.nodes)),em(),setTimeout(()=>{er&&er.fitView({padding:.2,includeHiddenNodes:!1,minZoom:.5,maxZoom:1.5})},100),eQ({title:"Template Loaded!",description:'"'.concat(e.name,'" has been loaded successfully.'),status:"success",duration:3e3,isClosable:!0})},on=e=>{let o=btoa(JSON.stringify(e)),r=[];r.push((e.name||"addon").toLowerCase().replace(/[^a-z0-9]/g,""));for(let e=0;e<o.length;e+=8)r.push(o.slice(e,e+8));return r.join("_")},ol=async e=>{try{await navigator.clipboard.writeText(e),eQ({title:"Copied!",description:"Flow data copied to clipboard.",status:"success",duration:2e3,isClosable:!0})}catch(e){eQ({title:"Copy Failed",description:"Failed to copy to clipboard. Please manually copy the text.",status:"error",duration:3e3,isClosable:!0})}},ot=e=>{try{let o=e.split("_");o[0];let r=o.slice(1).join(""),s=atob(r);return JSON.parse(s)}catch(e){throw Error("Invalid flow code format")}},oi=async()=>{if(!e$.trim())return void eQ({title:"Import Error",description:"Please paste the flow code to import.",status:"error",duration:3e3,isClosable:!0});try{let o;if(!(o=e$.trim().startsWith("{")?JSON.parse(e$):ot(e$.trim())).nodes||!o.edges||!Array.isArray(o.nodes)||!Array.isArray(o.edges))throw Error("Invalid flow data structure. Missing nodes or edges.");if(0===o.nodes.length)throw Error("Flow appears to be empty. No nodes found.");let r=null;if(o.name&&o.isBuilt)try{let s=await fetch("/api/admin/addons");if(s.ok){var e;r=(null==(e=(await s.json()).customAddons)?void 0:e.find(e=>e.name===o.name))?"exists":"deleted"}}catch(e){}Y(o.nodes),ee(((e,o)=>e.map(e=>{let r=o.find(o=>o.id===e.source),s=o.find(o=>o.id===e.target);if(r&&s){let o=$[r.type]||"#6b7280",n=$[s.type]||"#6b7280";return{...e,type:"gradient",animated:!0,markerEnd:{type:q.TG.ArrowClosed,color:n},style:{strokeWidth:2},data:{sourceColor:o,targetColor:n}}}return e}))(o.edges,o.nodes)),eB({name:o.name||"Imported Flow",description:o.description||"Imported from shared flow",author:o.author||"Unknown",version:o.version||"1.0.0",tags:o.tags||[],isPublic:!1});let s='Successfully imported "'.concat(o.name,'" with ').concat(o.nodeCount||o.nodes.length," nodes."),n="success";"deleted"===r?(s='Flow imported from "'.concat(o.name,'" but the original addon has been deleted. You have the flow structure and can rebuild it.'),n="warning"):"exists"===r?s='Flow imported from "'.concat(o.name,'". The original addon still exists, so you can compare or make modifications.'):o.isBuilt&&(s='Flow imported from "'.concat(o.name,'" (built addon). Status of original addon unknown.'),n="warning"),eQ({title:"Flow Imported!",description:s,status:n,duration:5e3,isClosable:!0}),eX(""),ez(),setTimeout(()=>{er&&er.fitView({padding:.2,includeHiddenNodes:!1,minZoom:.5,maxZoom:1.5})},100)}catch(e){eQ({title:"Import Failed",description:"Failed to import flow: ".concat(e.message),status:"error",duration:5e3,isClosable:!0})}},oa=(0,n.useCallback)(()=>{let e=X.filter(e=>e.selected);e.some(e=>"trigger"===e.type)&&1===e.length||(Y(e=>e.filter(e=>!e.selected||"trigger"===e.type)),ee(e=>e.filter(e=>!e.selected)))},[Y,ee,X]),oc=(0,n.useCallback)(e=>{let o=e.target;"INPUT"===o.tagName||"TEXTAREA"===o.tagName||o.isContentEditable||("Delete"===e.key&&(e.preventDefault(),oa()),"Backspace"===e.key&&(e.preventDefault(),e.stopPropagation()))},[oa]),od=(0,n.useCallback)(()=>{},[]),oh=(0,n.useCallback)(()=>{},[]),om=(0,n.useCallback)(()=>{ee(e=>e.map(e=>{if("gradient"!==e.type){let o=X.find(o=>o.id===e.source),r=X.find(o=>o.id===e.target);if(o&&r){let s=$[o.type]||"#6b7280",n=$[r.type]||"#6b7280";return{...e,type:"gradient",animated:!0,markerEnd:{type:q.TG.ArrowClosed,color:n},style:{strokeWidth:2},data:{sourceColor:s,targetColor:n}}}}return e}))},[X,ee]);return n.useEffect(()=>{ee(e=>e.map(e=>{let o=X.find(o=>o.id===e.source),r=X.find(o=>o.id===e.target);if(!o||!r)return e;let s=$[o.type]||"#6b7280",n=$[r.type]||"#6b7280";return{...e,data:{...e.data,sourceColor:s,targetColor:n}}}))},[K,X,$,ee]),n.useEffect(()=>{or(),e6()},[]),n.useEffect(()=>{om()},[om]),n.useEffect(()=>(document.addEventListener("keydown",oc),()=>{document.removeEventListener("keydown",oc)}),[oc]),(0,s.jsxs)(i.a,{h:"100vh",w:"100%",bg:K.colors.background,children:[(0,s.jsxs)(a.T,{spacing:0,h:"full",children:[(0,s.jsx)(i.a,{w:"full",bg:K.colors.surface,borderBottom:"1px solid",borderColor:K.colors.border,px:6,py:4,children:(0,s.jsxs)(c.z,{justify:"space-between",align:"center",children:[(0,s.jsxs)(a.T,{align:"start",spacing:1,children:[(0,s.jsxs)(c.z,{align:"center",spacing:3,children:[(0,s.jsx)(d.K,{icon:(0,s.jsx)(B.kRp,{}),"aria-label":"Go Back",size:"md",colorScheme:"blue",variant:"solid",onClick:()=>r.back(),bg:"blue.500",color:"white",_hover:{bg:"blue.600"},_active:{bg:"blue.700"},border:"1px solid",borderColor:"blue.400"}),(0,s.jsx)(h.D,{size:"lg",color:K.colors.text,children:"\uD83C\uDFA8 Visual Addon Builder"})]}),(0,s.jsx)(m.E,{fontSize:"sm",color:K.colors.textSecondary,children:"Build addons with drag-and-drop building blocks"})]}),(0,s.jsxs)(c.z,{spacing:2,children:[(0,s.jsx)(x.m,{label:"Help & Documentation",children:(0,s.jsx)(d.K,{icon:(0,s.jsx)(B.lrG,{}),"aria-label":"Help",size:"sm",variant:"ghost",colorScheme:"yellow",onClick:ea})}),(0,s.jsx)(u.$,{leftIcon:(0,s.jsx)(B.B88,{}),size:"sm",variant:"ghost",colorScheme:"blue",onClick:()=>{e6(),ej()},children:"Load"}),(0,s.jsx)(u.$,{leftIcon:(0,s.jsx)(B.Pum,{}),size:"sm",variant:"ghost",colorScheme:"purple",onClick:()=>{var e,r;if(!er)return void eQ({title:"Error",description:"Flow instance not available.",status:"error",duration:3e3,isClosable:!0});let s=er.toObject();eK(on({name:eF.name||"Untitled Flow",description:eF.description||"Exported flow from Visual Addon Builder",author:eF.author||(null==o||null==(e=o.user)?void 0:e.email)||"Unknown",version:eF.version||"1.0.0",tags:eF.tags||[],exportedAt:new Date().toISOString(),exportedBy:(null==o||null==(r=o.user)?void 0:r.email)||"Unknown",nodeCount:s.nodes.length,edgeCount:s.edges.length,builderVersion:"1.0.0",nodes:s.nodes,edges:s.edges})),eC()},children:"Export Code"}),(0,s.jsx)(u.$,{leftIcon:(0,s.jsx)(B.a4x,{}),size:"sm",variant:"ghost",colorScheme:"green",onClick:eT,children:"Import Code"}),(0,s.jsx)(u.$,{leftIcon:(0,s.jsx)(B.aze,{}),size:"sm",colorScheme:"green",isLoading:en,loadingText:"Building...",onClick:()=>{var e;el(!0),eB({name:"Custom Addon",description:"Generated addon from visual builder",author:(null==o||null==(e=o.user)?void 0:e.name)||"Addon Builder",version:"1.0.0",tags:[],isPublic:!1}),eu()},children:"Build Addon"})]})]})}),(0,s.jsx)(i.a,{flex:"1",w:"full",position:"relative",children:(0,s.jsx)(q.Ln,{children:(0,s.jsx)("div",{ref:et,style:{width:"100%",height:"100%"},children:(0,s.jsxs)(q.Gc,{nodes:X,edges:Z,onNodesChange:Q,onEdgesChange:eo,onNodesDelete:od,onEdgesDelete:oh,onConnect:e2,onInit:e=>{es(e)},onDrop:e4,onDragOver:e3,nodeTypes:e1,edgeTypes:eN,fitView:!0,proOptions:{hideAttribution:!0},deleteKeyCode:null,multiSelectionKeyCode:null,style:{backgroundColor:K.colors.background},children:[(0,s.jsx)(G.V,{color:K.colors.border,gap:16}),(0,s.jsx)(q.Zk,{position:"top-left",children:(0,s.jsxs)(a.T,{bg:K.colors.surface,border:"1px solid",borderColor:K.colors.border,borderRadius:"lg",p:4,spacing:3,align:"stretch",minW:"220px",maxH:"80vh",overflowY:"auto",children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:K.colors.text,children:"\uD83C\uDFA8 Building Blocks"}),(0,s.jsxs)(c.z,{spacing:1,children:[(0,s.jsx)(x.m,{label:"Load pre-built templates",children:(0,s.jsx)(d.K,{icon:(0,s.jsx)(B.est,{}),size:"xs",variant:"ghost",colorScheme:"teal","aria-label":"Templates",onClick:eh})}),(0,s.jsx)(x.m,{label:"Hover blocks for detailed info",children:(0,s.jsx)(d.K,{icon:(0,s.jsx)(B.lrG,{}),size:"xs",variant:"ghost","aria-label":"Help"})})]})]}),[{type:"command",label:"Command",icon:"⚡",color:"blue",description:"Slash commands users can execute\n• /ping, /ban, /kick\n• Click to configure options",rules:"Connect directly from Start (Command path)"},{type:"event",label:"Event",icon:"\uD83D\uDCE1",color:"green",description:"Discord events that trigger actions\n• Member joins/leaves\n• Message reactions\n• Voice changes",rules:"Connect directly from Start (Event path)"},{type:"action",label:"Action",icon:"\uD83C\uDFAF",color:"purple",description:"What happens when triggered\n• Send messages/embeds\n• Add/remove roles\n• Kick/ban users",rules:"Must connect to Command/Event/Condition"},{type:"apiRequest",label:"API Request",icon:"\uD83C\uDF10",color:"teal",description:"Make HTTP requests to external APIs\n• GET, POST, PUT, DELETE\n• Custom headers and body\n• Save response to variable",rules:"Can be used anywhere in your flow"},{type:"condition",label:"Condition",icon:"❓",color:"orange",description:"Check if something is true\n• User has role/permission\n• Message contains text\n• Two outputs: TRUE/FALSE",rules:"Connect TRUE/FALSE paths to different actions"}].map(e=>(0,s.jsx)(x.m,{label:(0,s.jsxs)(i.a,{p:2,children:[(0,s.jsx)(m.E,{fontWeight:"bold",mb:1,children:e.label}),(0,s.jsx)(m.E,{fontSize:"xs",mb:2,whiteSpace:"pre-line",children:e.description}),(0,s.jsxs)(m.E,{fontSize:"xs",color:"yellow.300",fontWeight:"bold",children:["\uD83D\uDCA1 ",e.rules]})]}),placement:"right",hasArrow:!0,bg:K.colors.surface,color:K.colors.text,borderRadius:"md",p:0,children:(0,s.jsx)(i.a,{draggable:!0,onDragStart:o=>{o.dataTransfer.setData("application/reactflow",e.type),o.dataTransfer.effectAllowed="move"},bg:K.colors.background,border:"1px solid",borderColor:K.colors.border,borderRadius:"md",p:3,cursor:"grab",_hover:{bg:K.colors.surface,transform:"scale(1.02)",borderColor:K.colors.primary},_active:{cursor:"grabbing",transform:"scale(0.98)"},transition:"all 0.2s",children:(0,s.jsxs)(a.T,{spacing:2,children:[(0,s.jsxs)(c.z,{spacing:2,width:"full",children:[(0,s.jsx)(m.E,{fontSize:"lg",children:e.icon}),(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"medium",color:K.colors.text,children:e.label})]}),(0,s.jsx)(p.E,{size:"sm",colorScheme:e.color,width:"full",children:"Drag to canvas"})]})})},e.type))]})})]})})})})]}),(0,s.jsxs)(g.aF,{isOpen:ei,onClose:ec,size:"xl",children:[(0,s.jsx)(b.m,{}),(0,s.jsxs)(j.$,{bg:K.colors.background,children:[(0,s.jsx)(f.r,{color:K.colors.text,children:"\uD83C\uDFA8 How to Use the Visual Addon Builder"}),(0,s.jsx)(v.s,{}),(0,s.jsx)(y.c,{pb:6,children:(0,s.jsxs)(a.T,{spacing:4,align:"start",children:[(0,s.jsxs)(C.F,{status:"info",borderRadius:"md",children:[(0,s.jsx)(S._,{}),(0,s.jsxs)(a.T,{align:"start",spacing:1,children:[(0,s.jsx)(m.E,{fontWeight:"bold",children:"Welcome to the Visual Addon Builder!"}),(0,s.jsx)(m.E,{fontSize:"sm",children:"Create Discord bot addons using intuitive drag-and-drop building blocks."})]})]}),(0,s.jsxs)(i.a,{children:[(0,s.jsx)(m.E,{fontWeight:"bold",mb:2,color:K.colors.text,children:"\uD83D\uDD27 Building Blocks:"}),(0,s.jsxs)(a.T,{align:"start",spacing:1,pl:4,children:[(0,s.jsxs)(m.E,{fontSize:"sm",children:["⚡ ",(0,s.jsx)("strong",{children:"Command"})," - Slash commands users can execute"]}),(0,s.jsxs)(m.E,{fontSize:"sm",children:["\uD83D\uDCE1 ",(0,s.jsx)("strong",{children:"Event"})," - Discord events like messages or reactions"]}),(0,s.jsxs)(m.E,{fontSize:"sm",children:["\uD83C\uDFAF ",(0,s.jsx)("strong",{children:"Action"})," - What happens when triggered"]}),(0,s.jsxs)(m.E,{fontSize:"sm",children:["❓ ",(0,s.jsx)("strong",{children:"Condition"})," - Check if something is true"]})]})]}),(0,s.jsxs)(i.a,{children:[(0,s.jsx)(m.E,{fontWeight:"bold",mb:2,color:K.colors.text,children:"\uD83C\uDFAE How to Create Flows:"}),(0,s.jsxs)(a.T,{align:"start",spacing:2,pl:4,children:[(0,s.jsxs)(m.E,{fontSize:"sm",children:["1. ",(0,s.jsx)("strong",{children:"Start:"}),' Every flow begins with the "Start Here" trigger']}),(0,s.jsxs)(m.E,{fontSize:"sm",children:["2. ",(0,s.jsx)("strong",{children:"Add Logic:"})," Drag Command or Event blocks to define when things happen"]}),(0,s.jsxs)(m.E,{fontSize:"sm",children:["3. ",(0,s.jsx)("strong",{children:"Connect:"})," Drag from the bottom of one block to the top of another"]}),(0,s.jsxs)(m.E,{fontSize:"sm",children:["4. ",(0,s.jsx)("strong",{children:"Configure:"})," Click the ⚙️ icon to set up each block"]}),(0,s.jsxs)(m.E,{fontSize:"sm",children:["5. ",(0,s.jsx)("strong",{children:"Add Actions:"})," End with Action blocks to define what happens"]}),(0,s.jsxs)(m.E,{fontSize:"sm",children:["6. ",(0,s.jsx)("strong",{children:"Save & Build:"}),' Save your work and click "Build Addon"']})]})]}),(0,s.jsxs)(i.a,{children:[(0,s.jsx)(m.E,{fontWeight:"bold",mb:2,color:K.colors.text,children:"\uD83D\uDD17 Connection Rules:"}),(0,s.jsxs)(a.T,{align:"start",spacing:1,pl:4,children:[(0,s.jsxs)(m.E,{fontSize:"sm",children:["• ",(0,s.jsx)("strong",{children:"Choose ONE path:"})," Either Command OR Event from Start"]}),(0,s.jsxs)(m.E,{fontSize:"sm",children:["• ",(0,s.jsx)("strong",{children:"Commands:"})," For slash commands (/ping, /ban)"]}),(0,s.jsxs)(m.E,{fontSize:"sm",children:["• ",(0,s.jsx)("strong",{children:"Events:"})," For automatic triggers (joins, reactions)"]}),(0,s.jsxs)(m.E,{fontSize:"sm",children:["• ",(0,s.jsx)("strong",{children:"Conditions:"})," Have TWO outputs (True/False)"]}),(0,s.jsxs)(m.E,{fontSize:"sm",children:["• ",(0,s.jsx)("strong",{children:"Actions:"})," Should be at the end of each path"]})]})]}),(0,s.jsxs)(i.a,{children:[(0,s.jsx)(m.E,{fontWeight:"bold",mb:2,color:K.colors.text,children:"\uD83D\uDDD1️ Deleting Blocks:"}),(0,s.jsxs)(a.T,{align:"start",spacing:1,pl:4,children:[(0,s.jsx)(m.E,{fontSize:"sm",children:"1. Click a block to select it (it will highlight)"}),(0,s.jsxs)(m.E,{fontSize:"sm",children:["2. Press ",(0,s.jsx)("strong",{children:"Delete"})," key (not Backspace)"]}),(0,s.jsx)(m.E,{fontSize:"sm",children:"3. The block and its connections will be removed"}),(0,s.jsx)(m.E,{fontSize:"sm",color:"green.400",children:"✅ Backspace works in text fields for typing"}),(0,s.jsx)(m.E,{fontSize:"sm",color:"yellow.400",children:"\uD83D\uDCA1 You cannot delete the Start block"})]})]}),(0,s.jsxs)(i.a,{children:[(0,s.jsx)(m.E,{fontWeight:"bold",mb:2,color:K.colors.text,children:"\uD83D\uDCA1 Pro Tips:"}),(0,s.jsxs)(a.T,{align:"start",spacing:1,pl:4,children:[(0,s.jsx)(m.E,{fontSize:"sm",children:"• Use Templates for quick starts"}),(0,s.jsx)(m.E,{fontSize:"sm",children:"• Hover over blocks in the palette for help"}),(0,s.jsx)(m.E,{fontSize:"sm",children:"• Conditions let you create smart logic"}),(0,s.jsx)(m.E,{fontSize:"sm",children:"• Always test with simple flows first"})]})]})]})})]})]}),(0,s.jsxs)(g.aF,{isOpen:ed,onClose:em,size:"4xl",children:[(0,s.jsx)(b.m,{}),(0,s.jsxs)(j.$,{bg:K.colors.background,children:[(0,s.jsx)(f.r,{color:K.colors.text,children:"\uD83C\uDFA8 Choose a Template"}),(0,s.jsx)(v.s,{}),(0,s.jsx)(y.c,{pb:6,children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsx)(m.E,{fontSize:"sm",color:K.colors.textSecondary,children:"Start with a pre-built template to create your addon faster"}),(0,s.jsx)(i.a,{maxH:"500px",overflowY:"auto",pr:2,children:(0,s.jsx)(a.T,{spacing:6,align:"stretch",children:eM.map(e=>(0,s.jsxs)(i.a,{children:[(0,s.jsx)(m.E,{fontSize:"lg",fontWeight:"bold",color:K.colors.text,mb:4,children:e}),(0,s.jsx)(i.a,{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(300px, 1fr))",gap:4,children:eR.filter(o=>o.category===e).map(e=>(0,s.jsx)(i.a,{bg:K.colors.background,border:"1px solid",borderColor:K.colors.border,borderRadius:"lg",p:4,cursor:"pointer",position:"relative",_hover:{bg:K.colors.surface,borderColor:K.colors.primary,transform:"translateY(-2px)",boxShadow:"lg"},transition:"all 0.2s",onClick:()=>os(e),children:(0,s.jsxs)(a.T,{spacing:3,align:"stretch",children:[(0,s.jsx)(c.z,{justify:"space-between",align:"start",children:(0,s.jsxs)(c.z,{spacing:3,children:[(0,s.jsx)(i.a,{bg:K.colors.primary,color:"white",borderRadius:"lg",p:2,fontSize:"xl",children:e.thumbnail}),(0,s.jsxs)(a.T,{align:"start",spacing:1,children:[(0,s.jsx)(m.E,{fontSize:"md",fontWeight:"bold",color:K.colors.text,children:e.name}),(0,s.jsxs)(p.E,{colorScheme:"blue",size:"sm",children:[e.nodes.length," blocks"]})]})]})}),(0,s.jsx)(m.E,{fontSize:"sm",color:K.colors.textSecondary,lineHeight:"1.4",children:e.description}),(0,s.jsx)(u.$,{size:"sm",colorScheme:"blue",variant:"ghost",width:"full",onClick:o=>{o.stopPropagation(),os(e)},children:"Use Template"})]})},e.id))})]},e))})})]})})]})]}),(0,s.jsxs)(g.aF,{isOpen:ex,onClose:ep,size:"lg",children:[(0,s.jsx)(b.m,{}),(0,s.jsxs)(j.$,{bg:K.colors.background,children:[(0,s.jsx)(f.r,{color:K.colors.text,children:"\uD83D\uDCBE Save Addon Flow"}),(0,s.jsx)(v.s,{}),(0,s.jsx)(y.c,{pb:6,children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(C.F,{status:"info",borderRadius:"md",children:[(0,s.jsx)(S._,{}),(0,s.jsx)(w.T,{fontSize:"sm",children:"Save your addon flow with metadata for easy organization and sharing."})]}),(0,s.jsxs)(E.r,{columns:2,spacing:4,children:[(0,s.jsxs)(T.MJ,{isRequired:!0,children:[(0,s.jsx)(z.l,{color:K.colors.text,children:"Name"}),(0,s.jsx)(k.p,{value:eF.name,onChange:e=>eB(o=>({...o,name:e.target.value})),placeholder:"My Awesome Addon",bg:K.colors.background,color:K.colors.text,borderColor:K.colors.border})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:K.colors.text,children:"Version"}),(0,s.jsx)(k.p,{value:eF.version,onChange:e=>eB(o=>({...o,version:e.target.value})),placeholder:"1.0.0",bg:K.colors.background,color:K.colors.text,borderColor:K.colors.border})]})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:K.colors.text,children:"Author"}),(0,s.jsx)(k.p,{value:eF.author,onChange:e=>eB(o=>({...o,author:e.target.value})),placeholder:"Your Name",bg:K.colors.background,color:K.colors.text,borderColor:K.colors.border})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:K.colors.text,children:"Description"}),(0,s.jsx)(D.T,{value:eF.description,onChange:e=>eB(o=>({...o,description:e.target.value})),placeholder:"Describe what your addon does...",bg:K.colors.background,color:K.colors.text,borderColor:K.colors.border,rows:3})]}),(0,s.jsxs)(T.MJ,{children:[(0,s.jsx)(z.l,{color:K.colors.text,children:"Tags"}),(0,s.jsxs)(c.z,{spacing:2,children:[(0,s.jsx)(k.p,{value:eH,onChange:e=>eJ(e.target.value),placeholder:"Add tags (press Enter)",onKeyDown:e=>"Enter"===e.key&&oe(),bg:K.colors.background,color:K.colors.text,borderColor:K.colors.border}),(0,s.jsx)(u.$,{size:"sm",onClick:oe,children:"Add"})]}),(0,s.jsx)(R.s,{wrap:"wrap",gap:2,mt:2,children:eF.tags.map(e=>(0,s.jsxs)(A.vw,{size:"sm",colorScheme:"blue",variant:"solid",children:[(0,s.jsx)(A.d1,{children:e}),(0,s.jsx)(A.TV,{onClick:()=>oo(e)})]},e))})]}),(0,s.jsxs)(C.F,{status:"warning",borderRadius:"md",children:[(0,s.jsx)(S._,{}),(0,s.jsx)(w.T,{fontSize:"sm",children:"If an addon with the same name exists, it will be overwritten."})]})]})}),(0,s.jsxs)(M.j,{children:[(0,s.jsx)(u.$,{variant:"ghost",mr:3,onClick:ep,children:"Cancel"}),(0,s.jsx)(u.$,{colorScheme:"blue",onClick:()=>{if(!eF.name.trim())return void eQ({title:"Name Required",description:"Please enter a name for your addon.",status:"error",duration:3e3,isClosable:!0});if(en){e5(eF),ep();return}if(!er)return void eQ({title:"Error",description:"Flow instance not available.",status:"error",duration:3e3,isClosable:!0});let e=er.toObject(),o={id:Date.now().toString(),name:eF.name,description:eF.description,author:eF.author,version:eF.version,tags:eF.tags,isPublic:eF.isPublic,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),nodeCount:e.nodes.length,edgeCount:e.edges.length,nodes:e.nodes,edges:e.edges},r=localStorage.getItem("addon-builder-saved-flows"),s=[];if(r)try{s=JSON.parse(r)}catch(e){}let n=s.findIndex(e=>e.name===o.name);-1!==n?s[n]={...s[n],...o,updatedAt:new Date().toISOString()}:s.push(o),localStorage.setItem("addon-builder-saved-flows",JSON.stringify(s)),eQ({title:"Flow Saved!",description:'"'.concat(o.name,'" has been saved successfully.'),status:"success",duration:3e3,isClosable:!0}),ep()},children:"Save Flow"})]})]})]}),(0,s.jsxs)(g.aF,{isOpen:eg,onClose:ef,size:"4xl",children:[(0,s.jsx)(b.m,{}),(0,s.jsxs)(j.$,{bg:K.colors.background,children:[(0,s.jsx)(f.r,{color:K.colors.text,children:"\uD83D\uDCC2 Load Addon Flow"}),(0,s.jsx)(v.s,{}),(0,s.jsx)(y.c,{pb:6,children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",children:[(0,s.jsx)(m.E,{fontSize:"sm",color:K.colors.textSecondary,children:"Choose a saved addon flow to load"}),(0,s.jsxs)(m.E,{fontSize:"sm",color:K.colors.textSecondary,children:[eW.length," saved flows"]})]}),0===eW.length?(0,s.jsxs)(C.F,{status:"info",borderRadius:"md",children:[(0,s.jsx)(S._,{}),(0,s.jsx)(w.T,{children:"No saved flows found. Create and save your first addon flow to see it here!"})]}):(0,s.jsx)(i.a,{maxH:"500px",overflowY:"auto",pr:2,children:(0,s.jsx)(E.r,{columns:[1,2],spacing:4,children:eW.map(e=>(0,s.jsxs)(O.Z,{bg:K.colors.background,borderColor:K.colors.border,children:[(0,s.jsx)(W.a,{pb:2,children:(0,s.jsxs)(c.z,{justify:"space-between",align:"start",children:[(0,s.jsxs)(a.T,{align:"start",spacing:1,children:[(0,s.jsx)(m.E,{fontSize:"md",fontWeight:"bold",color:K.colors.text,children:e.name}),(0,s.jsxs)(c.z,{spacing:2,children:[(0,s.jsxs)(p.E,{size:"sm",colorScheme:"blue",children:["v",e.version]}),e.isBuilt?e.hasOriginalFlow?(0,s.jsx)(p.E,{size:"sm",colorScheme:"green",children:"✓ Recoverable"}):(0,s.jsx)(p.E,{size:"sm",colorScheme:"orange",children:"Template Only"}):(0,s.jsxs)(p.E,{size:"sm",colorScheme:"blue",children:[e.nodeCount," blocks"]})]})]}),(0,s.jsxs)(I.W,{children:[(0,s.jsx)(F.I,{as:d.K,icon:(0,s.jsx)(B.ZZB,{}),variant:"ghost",size:"sm"}),(0,s.jsx)(U.c,{bg:K.colors.surface,children:(0,s.jsx)(N.D,{icon:(0,s.jsx)(B.IXo,{}),onClick:()=>e7(e),color:"red.400",children:e.isBuilt?"Delete Addon":"Delete Flow"})})]})]})}),(0,s.jsx)(L.b,{pt:0,children:(0,s.jsxs)(a.T,{align:"start",spacing:2,children:[(0,s.jsx)(m.E,{fontSize:"sm",color:K.colors.textSecondary,noOfLines:2,children:e.isBuilt?e.hasOriginalFlow?"".concat(e.description," • Original flow data available! You can recover and edit the exact flow that was used to build this addon."):"".concat(e.description," • This addon was built before flow recovery was implemented. Only a basic template can be provided."):e.description||"No description available"}),(0,s.jsx)(c.z,{justify:"space-between",align:"center",width:"full",children:(0,s.jsxs)(c.z,{spacing:2,children:[(0,s.jsxs)(c.z,{spacing:1,children:[(0,s.jsx)(B.JXP,{size:12}),(0,s.jsx)(m.E,{fontSize:"xs",color:K.colors.textSecondary,children:e.author})]}),(0,s.jsxs)(c.z,{spacing:1,children:[(0,s.jsx)(B.Ohp,{size:12}),(0,s.jsx)(m.E,{fontSize:"xs",color:K.colors.textSecondary,children:new Date(e.updatedAt).toLocaleDateString()})]})]})}),e.tags&&e.tags.length>0&&(0,s.jsxs)(R.s,{wrap:"wrap",gap:1,children:[e.tags.slice(0,3).map(e=>(0,s.jsx)(A.vw,{size:"sm",colorScheme:"gray",variant:"outline",children:e},e)),e.tags.length>3&&(0,s.jsxs)(A.vw,{size:"sm",colorScheme:"gray",variant:"outline",children:["+",e.tags.length-3]})]})]})}),(0,s.jsx)(P.w,{pt:0,children:(0,s.jsx)(x.m,{label:e.isBuilt?e.hasOriginalFlow?"Load the original flow - fully recoverable!":"Load a basic template - original flow not saved":"Load the exact saved flow",placement:"top",children:(0,s.jsx)(u.$,{size:"sm",colorScheme:e.isBuilt?e.hasOriginalFlow?"green":"orange":"blue",width:"full",onClick:()=>e8(e),children:e.isBuilt?e.hasOriginalFlow?"Recover Flow":"Load Template":"Load Flow"})})})]},e.id))})})]})})]})]}),(0,s.jsxs)(g.aF,{isOpen:ev,onClose:eS,size:"3xl",children:[(0,s.jsx)(b.m,{}),(0,s.jsxs)(j.$,{bg:K.colors.background,children:[(0,s.jsx)(f.r,{color:K.colors.text,children:"\uD83D\uDE80 Export Addon Flow"}),(0,s.jsx)(v.s,{}),(0,s.jsx)(y.c,{pb:6,children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(C.F,{status:"info",borderRadius:"md",children:[(0,s.jsx)(S._,{}),(0,s.jsx)(w.T,{children:"Share your addon flow with others! Copy the flow code below and share it anywhere. Others can import this code to reproduce your exact flow."})]}),(0,s.jsxs)(a.T,{align:"stretch",spacing:3,children:[(0,s.jsxs)(c.z,{justify:"space-between",align:"center",children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:K.colors.text,children:"Flow Code"}),(0,s.jsx)(c.z,{spacing:2,children:(0,s.jsx)(u.$,{size:"sm",leftIcon:(0,s.jsx)(B.nxz,{}),colorScheme:"blue",onClick:()=>ol(eG),children:"Copy Code"})})]}),(0,s.jsx)(i.a,{p:4,bg:K.colors.surface,borderColor:K.colors.border,borderWidth:"2px",borderRadius:"md",borderStyle:"dashed",children:(0,s.jsx)(m.E,{fontFamily:"monospace",fontSize:"lg",fontWeight:"bold",color:K.colors.primary,textAlign:"center",wordBreak:"break-all",userSelect:"all",children:eG})})]}),(0,s.jsxs)(i.a,{p:3,bg:K.colors.surface,borderRadius:"md",borderColor:K.colors.border,borderWidth:"1px",children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:K.colors.text,mb:2,children:"\uD83D\uDCCB How to Share:"}),(0,s.jsxs)(a.T,{align:"start",spacing:1,fontSize:"sm",color:K.colors.textSecondary,children:[(0,s.jsx)(m.E,{children:"• Copy the flow code and paste it in Discord, email, or any text platform"}),(0,s.jsx)(m.E,{children:"• Much easier to share than long JSON data!"}),(0,s.jsx)(m.E,{children:'• The recipient can use the "Import Code" button to load your flow'}),(0,s.jsx)(m.E,{children:"• All your block configurations and connections will be preserved"})]})]})]})})]})]}),(0,s.jsxs)(g.aF,{isOpen:eE,onClose:ez,size:"2xl",children:[(0,s.jsx)(b.m,{}),(0,s.jsxs)(j.$,{bg:K.colors.background,children:[(0,s.jsx)(f.r,{color:K.colors.text,children:"\uD83D\uDCE5 Import Addon Flow"}),(0,s.jsx)(v.s,{}),(0,s.jsx)(y.c,{pb:6,children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(C.F,{status:"info",borderRadius:"md",children:[(0,s.jsx)(S._,{}),(0,s.jsx)(w.T,{children:"Import an addon flow from someone else! Paste the flow code below to load their flow configuration. This will replace your current flow."})]}),(0,s.jsxs)(a.T,{align:"stretch",spacing:3,children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:K.colors.text,children:"Paste Flow Code"}),(0,s.jsx)(k.p,{value:e$,onChange:e=>eX(e.target.value),fontFamily:"monospace",fontSize:"sm",bg:K.colors.surface,borderColor:K.colors.border,placeholder:"addon_xxxxxx_xxxxxx_xxxxxx...",size:"lg"})]}),(0,s.jsxs)(i.a,{p:3,bg:K.colors.surface,borderRadius:"md",borderColor:K.colors.border,borderWidth:"1px",children:[(0,s.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",color:K.colors.text,mb:2,children:"\uD83D\uDD27 Import Instructions:"}),(0,s.jsxs)(a.T,{align:"start",spacing:1,fontSize:"sm",color:K.colors.textSecondary,children:[(0,s.jsx)(m.E,{children:"• Paste the flow code (looks like: addon_xxxxxx_xxxxxx_xxxxxx)"}),(0,s.jsx)(m.E,{children:"• Much simpler than JSON - just a single line of code!"}),(0,s.jsx)(m.E,{children:'• Click "Import Flow" to load the configuration'}),(0,s.jsx)(m.E,{children:"• Your current flow will be replaced with the imported one"}),(0,s.jsx)(m.E,{children:"• Legacy JSON format is still supported for backward compatibility"})]})]})]})}),(0,s.jsxs)(M.j,{children:[(0,s.jsx)(u.$,{variant:"ghost",mr:3,onClick:ez,children:"Cancel"}),(0,s.jsx)(u.$,{colorScheme:"green",onClick:oi,isDisabled:!e$.trim(),children:"Import Flow"})]})]})]}),(0,s.jsx)(_.Lt,{isOpen:!!eV,onClose:()=>eq(null),leastDestructiveRef:eY,children:(0,s.jsx)(b.m,{children:(0,s.jsxs)(_.EO,{bg:K.colors.background,children:[(0,s.jsx)(f.r,{fontSize:"lg",fontWeight:"bold",color:K.colors.text,children:(null==eV?void 0:eV.isBuilt)?"Delete Addon":"Delete Flow"}),(0,s.jsx)(y.c,{color:K.colors.textSecondary,children:(null==eV?void 0:eV.isBuilt)?(0,s.jsxs)(s.Fragment,{children:["Are you sure you want to delete the ",(0,s.jsxs)("strong",{children:['"',null==eV?void 0:eV.name,'"']})," addon? This will permanently remove it from both the source code and the bot. This action cannot be undone."]}):(0,s.jsxs)(s.Fragment,{children:["Are you sure you want to delete the ",(0,s.jsxs)("strong",{children:['"',null==eV?void 0:eV.name,'"']})," flow? This will permanently remove it from your saved flows. This action cannot be undone."]})}),(0,s.jsxs)(M.j,{children:[(0,s.jsx)(u.$,{ref:eY,onClick:()=>eq(null),children:"Cancel"}),(0,s.jsx)(u.$,{colorScheme:"red",onClick:e9,ml:3,children:(null==eV?void 0:eV.isBuilt)?"Delete Addon":"Delete Flow"})]})]})})})]})}}},e=>{var o=o=>e(e.s=o);e.O(0,[4108,5906,9998,4976,217,3177,3035,6476,5116,7762,636,6593,8792],()=>o(14691)),_N_E=e.O()}]);