// @ts-nocheck
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>dal<PERSON>ontent,
    <PERSON>dal<PERSON>eader,
    Modal<PERSON>ooter,
    ModalBody,
    ModalCloseButton,
    Button,
    useToast,
    VStack,
    Text,
    Spinner,
    FormControl,
    FormLabel,
    Switch,
    Select,
    Textarea,
    Tabs,
    TabList,
    Tab,
    TabPanels,
    TabPanel,
    SimpleGrid,
    Checkbox,
    CheckboxGroup,
    Input,
    HStack,
    Box,
    Icon,
    Card,
    CardBody,
    CardHeader,
    Heading,
    Badge,
    IconButton,
    Tooltip,
    Accordion,
    AccordionItem,
    AccordionButton,
    AccordionPanel,
    AccordionIcon,
    Divider,
    NumberInput,
    NumberInputField,
    NumberInputStepper,
    NumberIncrementStepper,
    NumberDecrementStepper,
    Alert,
    AlertIcon,
    AlertTitle,
    AlertDescription,
    Flex,
    Spacer,
  } from '@chakra-ui/react';
  import { useState, useEffect } from 'react';
  import { FiMessageSquare, FiLogOut, FiPlus, FiTrash2, FiEdit2, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>huffle, <PERSON>Settings, FiHash } from 'react-icons/fi';
  
  // Message template interface
  interface MessageTemplate {
    title?: string;
    description: string;
    color?: string;
    footer?: string;
  }

  const defaultSettings = {
    welcome: {
      enabled: false,
      channelId: '',
      messages: [
        {
          title: "🎮 Welcome to {guild}, {userName}!",
          description: "Pull up a chair and make yourself comfortable. We've got games, chaos, and questionable life choices waiting for you!\n\n📅 Account Created: {UserCreation}\n🎯 Join Date: {joinDate}",
          color: "#FF6B35",
          footer: "You're our {memberCount} member - let's get weird!"
        },
        {
          title: "🚨 Error 404: Chill Not Found! 🚨",
          description: "Hey {user}, looks like you've stumbled into our chaotic corner of the internet. Hope you brought snacks and a sense of humor!\n\n🎮 Member #{memberCountNumeric}\n📅 Joined: {joinTime}",
          color: "#8B0000",
          footer: "Welcome to the madness!"
        },
        {
          title: "🎯 Player {userName} has entered the game!",
          description: "Welcome to {server} where the games are wild and the conversations are... well, let's just say we're not your typical Discord server.\n\n📅 Account Age: {UserCreation}\n🎯 Member Since: {user-joinedAt}",
          color: "#1E90FF",
          footer: "Time to level up your social life!"
        },
        {
          title: "🔥 New challenger has appeared!",
          description: "It's {user}! Welcome to our adult gaming sanctuary where we take games seriously but ourselves... not so much.\n\n🎮 Member #{memberCountNumeric}\n⏰ Joined: {joinTime}",
          color: "#DC143C",
          footer: "Ready to game and chill?"
        },
        {
          title: "🎲 Welcome to the chaos, {userName}!",
          description: "You've joined {server} - where good decisions come to die and epic gaming moments are born. Buckle up!\n\n📅 Account Created: {user-createdAt}\n🎯 Join Time: {joinTime}",
          color: "#9932CC",
          footer: "Let the games begin!"
        },
        {
          title: "⚡ Level Up! New member unlocked!",
          description: "{user} just joined the {guild} crew! We promise we're friendlier than our name suggests... mostly.\n\n🎮 You're member #{memberCountNumeric}\n⏰ Account Age: {UserCreation}",
          color: "#FFD700",
          footer: "Achievement unlocked: Found the cool kids table"
        },
        {
          title: "🎪 Welcome to the circus, {userName}!",
          description: "Hope you're ready for some adult gaming fun and conversations that would make your mother question your life choices.\n\n📅 Joined: {joinDate}\n🎯 Member #{memberCountNumeric}",
          color: "#FF1493",
          footer: "We're all mad here, but in a good way!"
        },
        {
          title: "🎨 Another wild gamer appears!",
          description: "Hey {user}! Welcome to {server} where we game hard, laugh harder, and occasionally make sense.\n\n📅 Account Created: {UserCreation}\n⏰ Join Time: {joinTime}",
          color: "#20B2AA",
          footer: "Time to make some questionable decisions together!"
        },
        {
          title: "🚀 Mission accepted: Welcome {userName}!",
          description: "You've successfully infiltrated {guild}. Your mission: have fun, play games, and embrace the chaos.\n\n🎮 Member #{memberCountNumeric}\n📅 Join Date: {joinDate}",
          color: "#4169E1",
          footer: "Good luck, you'll need it!"
        },
        {
          title: "🎯 Critical hit! New member joined!",
          description: "Welcome {user} to our den of gaming degeneracy and adult conversation. Check your sanity at the door!\n\n📅 Account Age: {UserCreation}\n⏰ Member Since: {user-joinedAt}",
          color: "#32CD32",
          footer: "RIP your free time"
        }
      ],
      autoRole: {
        enabled: false,
        roleIds: [],
        delay: 1000,
        retry: {
          enabled: true,
          maxAttempts: 3,
          delayBetweenAttempts: 5000
        }
      },
      nekosGif: {
        enabled: true,
        type: 'wave' // wave, hug, pat, etc.
      }
    },
    goodbye: {
      enabled: false,
      channelId: '',
      messages: [
        {
          title: "💀 Game Over for {userName}",
          description: "Looks like {userName} has rage quit from {server}.\nThanks for the memories and questionable life choices!\n\n{kickStatus}\n⏰ Member Since: {user-joinedAt}",
          color: "#8B0000",
          footer: "Press F to pay respects"
        },
        {
          title: "🚪 {userName} has left the building",
          description: "Another one bites the dust! {userName} decided our chaos wasn't for them.\nCan't win 'em all, I guess.\n\n{kickStatus}\n🕒 Left: {leaveTime}",
          color: "#696969",
          footer: "The door's always open... maybe"
        },
        {
          title: "📤 Connection lost: {userName}",
          description: "{userName} has disconnected from {guild}.\nHope they found what they were looking for!\n\n{kickStatus}\n⏰ Member Since: {user-joinedAt}",
          color: "#FF6347",
          footer: "Thanks for gaming with us!"
        },
        {
          title: "🎭 Plot twist: {userName} vanished!",
          description: "In a shocking turn of events, {userName} has left {server}.\nThe show must go on!\n\n{kickStatus}\n🕒 Left: {leaveTime}",
          color: "#4B0082",
          footer: "Until we meet again in another lobby"
        },
        {
          title: "🏃‍♂️ {userName} speed-ran their exit",
          description: "Well, that was quick! {userName} decided to bounce from {server}.\nNo hard feelings... probably.\n\n{kickStatus}\n⏰ Member Since: {user-joinedAt}",
          color: "#FF8C00",
          footer: "See ya, wouldn't wanna be ya!"
        },
        {
          title: "🎪 The circus lost a performer",
          description: "{userName} has left the madness that is {server}.\nHope they find their chill somewhere else!\n\n{kickStatus}\n🕒 Left: {leaveTime}",
          color: "#20B2AA",
          footer: "Thanks for adding to the chaos!"
        },
        {
          title: "💥 {userName} signed off",
          description: "Another gamer has left {guild}.\nMay your framerate be high and your ping be low!\n\n{kickStatus}\n⏰ Member Since: {user-joinedAt}",
          color: "#FF69B4",
          footer: "GG, no re"
        },
        {
          title: "🎮 Player {userName} has disconnected",
          description: "Looks like {userName} found the exit door in {server}.\nHope our brand of chaos was entertaining!\n\n{kickStatus}\n🕒 Left: {leaveTime}",
          color: "#32CD32",
          footer: "Thanks for playing with us!"
        },
        {
          title: "🚀 {userName} has left orbit",
          description: "Mission complete! {userName} has successfully escaped {server}.\nSafe travels, space cadet!\n\n{kickStatus}\n⏰ Member Since: {user-joinedAt}",
          color: "#1E90FF",
          footer: "Houston, we have a departure"
        },
        {
          title: "🎯 Target eliminated: {userName}",
          description: "{userName} has been removed from the game... wait, they left voluntarily.\nWell, that's less dramatic than expected.\n\n{kickStatus}\n🕒 Left: {leaveTime}",
          color: "#B22222",
          footer: "Better luck next server!"
        }
      ],
      nekosGif: {
        enabled: true,
        type: 'cry' // cry, wave, sad, etc.
      }
    },
  };
  
  // Placeholder information for help
  const PLACEHOLDERS = {
    user: [
      { name: '{user}', description: 'Mentions the user, e.g., @Username' },
      { name: '{userName}', description: 'The user\'s name, e.g., Username' },
      { name: '{userTag}', description: 'The user\'s tag, e.g., Username#1234' },
      { name: '{userId}', description: 'The user\'s ID, e.g., 123456789012345678' },
      { name: '{userBanner}', description: 'URL of the user\'s banner (if they have one)' },
      { name: '{user-createdAt}', description: 'The date the user\'s account was created' },
      { name: '{UserCreation}', description: 'How long ago the user\'s account was created (e.g., "2 years ago")' },
    ],
    guild: [
      { name: '{server} / {guild}', description: 'The server\'s name' },
      { name: '{guildIcon}', description: 'URL of the server\'s icon' },
      { name: '{memberCount}', description: 'Total members with ordinal suffix, e.g., "100th"' },
      { name: '{memberCountNumeric}', description: 'Total members as a number, e.g., "100"' },
    ],
    time: [
      { name: '{longTime}', description: 'Current date and time, e.g., "June 1, 2024 12:00 PM"' },
      { name: '{shortTime}', description: 'Current time, e.g., "12:00 PM"' },
    ],
    welcome: [
      { name: '{joinDate}', description: 'The date the user joined' },
      { name: '{joinTime}', description: 'The time the user joined' },
      { name: '{user-joinedAt}', description: 'Full date and time the user joined' },
    ],
    goodbye: [
      { name: '{leaveDate}', description: 'The date the user left' },
      { name: '{leaveTime}', description: 'The time the user left' },
      { name: '{kickStatus}', description: 'If the user was kicked/banned, shows the status. Otherwise, hidden.' },
    ]
  };

  export default function WelcomeSystemDialog({ isOpen, onClose, channels = [], roles = [] }) {
    const toast = useToast();
    const [settings, setSettings] = useState(defaultSettings);
    const [isLoading, setIsLoading] = useState(true);
    const [isSaving, setIsSaving] = useState(false);
    const [selectedWelcomeMessage, setSelectedWelcomeMessage] = useState(0);
    const [selectedGoodbyeMessage, setSelectedGoodbyeMessage] = useState(0);
    const [showPlaceholders, setShowPlaceholders] = useState(false);

    // Include text channels and announcement channels as they can both receive messages
    // Channel types can be either numbers or strings depending on the API response
    const textChannels = channels.filter(c =>
      c.type === 0 || c.type === 5 ||
      c.type === 'GUILD_TEXT' || c.type === 'GUILD_ANNOUNCEMENT'
    );
    const manageableRoles = roles.filter(r => r.name !== '@everyone');
  
    const fetchSettings = async () => {
      setIsLoading(true);
      try {
        const res = await fetch('/api/automation/welcome');
        if (!res.ok) throw new Error('Failed to fetch settings');
        const data = await res.json();
        setSettings(data);
      } catch (error) {
        toast({
          title: 'Error loading settings',
          description: error.message,
          status: 'error',
          duration: 5000,
        });
      } finally {
        setIsLoading(false);
      }
    };
  
    const handleSave = async () => {
      setIsSaving(true);
      try {
        const res = await fetch('/api/automation/welcome', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(settings),
        });
        if (!res.ok) throw new Error('Failed to save settings');
        toast({
          title: 'Settings Saved',
          status: 'success',
          duration: 3000,
        });
        onClose();
      } catch (error) {
        toast({
          title: 'Error saving settings',
          description: error.message,
          status: 'error',
          duration: 5000,
        });
      } finally {
        setIsSaving(false);
      }
    };
  
    useEffect(() => {
      if (isOpen) {
        fetchSettings();
      }
    }, [isOpen]);
  
    // Helper functions for managing settings
    const handleWelcomeChange = (field, value) => {
      setSettings(prev => ({ ...prev, welcome: { ...prev.welcome, [field]: value } }));
    };

    const handleGoodbyeChange = (field, value) => {
      setSettings(prev => ({ ...prev, goodbye: { ...prev.goodbye, [field]: value } }));
    };

    const handleWelcomeNekosChange = (field, value) => {
      setSettings(prev => ({
        ...prev,
        welcome: {
          ...prev.welcome,
          nekosGif: { ...prev.welcome.nekosGif, [field]: value }
        }
      }));
    };

    const handleGoodbyeNekosChange = (field, value) => {
      setSettings(prev => ({
        ...prev,
        goodbye: {
          ...prev.goodbye,
          nekosGif: { ...prev.goodbye.nekosGif, [field]: value }
        }
      }));
    };

    const handleWelcomeAutoRoleChange = (field, value) => {
      setSettings(prev => ({
        ...prev,
        welcome: {
          ...prev.welcome,
          autoRole: { ...prev.welcome.autoRole, [field]: value }
        }
      }));
    };

    const handleWelcomeAutoRoleRetryChange = (field, value) => {
      setSettings(prev => ({
        ...prev,
        welcome: {
          ...prev.welcome,
          autoRole: {
            ...prev.welcome.autoRole,
            retry: { ...prev.welcome.autoRole.retry, [field]: value }
          }
        }
      }));
    };

    // Message template management
    const addWelcomeMessage = () => {
      const newMessage = {
        title: "🎮 Welcome to {guild}, {userName}!",
        description: "Welcome to our server! We're glad to have you here.\n\n📅 Account Created: {UserCreation}\n🎯 Join Date: {joinDate}",
        color: "#00FF00",
        footer: "You're our {memberCount} member!"
      };
      setSettings(prev => ({
        ...prev,
        welcome: {
          ...prev.welcome,
          messages: [...prev.welcome.messages, newMessage]
        }
      }));
      setSelectedWelcomeMessage(settings.welcome.messages.length);
    };

    const addGoodbyeMessage = () => {
      const newMessage = {
        title: "👋 Goodbye {userName}",
        description: "Thanks for being part of {guild}! We'll miss you.\n\n{kickStatus}\n⏰ Member Since: {user-joinedAt}",
        color: "#FF0000",
        footer: "Safe travels!"
      };
      setSettings(prev => ({
        ...prev,
        goodbye: {
          ...prev.goodbye,
          messages: [...prev.goodbye.messages, newMessage]
        }
      }));
      setSelectedGoodbyeMessage(settings.goodbye.messages.length);
    };

    const removeWelcomeMessage = (index) => {
      if (settings.welcome.messages.length <= 1) {
        toast({
          title: 'Cannot delete',
          description: 'You must have at least one welcome message.',
          status: 'warning',
          duration: 3000,
        });
        return;
      }
      setSettings(prev => ({
        ...prev,
        welcome: {
          ...prev.welcome,
          messages: prev.welcome.messages.filter((_, i) => i !== index)
        }
      }));
      if (selectedWelcomeMessage >= settings.welcome.messages.length - 1) {
        setSelectedWelcomeMessage(Math.max(0, selectedWelcomeMessage - 1));
      }
    };

    const removeGoodbyeMessage = (index) => {
      if (settings.goodbye.messages.length <= 1) {
        toast({
          title: 'Cannot delete',
          description: 'You must have at least one goodbye message.',
          status: 'warning',
          duration: 3000,
        });
        return;
      }
      setSettings(prev => ({
        ...prev,
        goodbye: {
          ...prev.goodbye,
          messages: prev.goodbye.messages.filter((_, i) => i !== index)
        }
      }));
      if (selectedGoodbyeMessage >= settings.goodbye.messages.length - 1) {
        setSelectedGoodbyeMessage(Math.max(0, selectedGoodbyeMessage - 1));
      }
    };

    const updateWelcomeMessage = (index, field, value) => {
      setSettings(prev => ({
        ...prev,
        welcome: {
          ...prev.welcome,
          messages: prev.welcome.messages.map((msg, i) =>
            i === index ? { ...msg, [field]: value } : msg
          )
        }
      }));
    };

    const updateGoodbyeMessage = (index, field, value) => {
      setSettings(prev => ({
        ...prev,
        goodbye: {
          ...prev.goodbye,
          messages: prev.goodbye.messages.map((msg, i) =>
            i === index ? { ...msg, [field]: value } : msg
          )
        }
      }));
    };

    const duplicateWelcomeMessage = (index) => {
      const messageToDuplicate = { ...settings.welcome.messages[index] };
      messageToDuplicate.title = `${messageToDuplicate.title} (Copy)`;
      setSettings(prev => ({
        ...prev,
        welcome: {
          ...prev.welcome,
          messages: [...prev.welcome.messages, messageToDuplicate]
        }
      }));
      setSelectedWelcomeMessage(settings.welcome.messages.length);
    };

    const duplicateGoodbyeMessage = (index) => {
      const messageToDuplicate = { ...settings.goodbye.messages[index] };
      messageToDuplicate.title = `${messageToDuplicate.title} (Copy)`;
      setSettings(prev => ({
        ...prev,
        goodbye: {
          ...prev.goodbye,
          messages: [...prev.goodbye.messages, messageToDuplicate]
        }
      }));
      setSelectedGoodbyeMessage(settings.goodbye.messages.length);
    };
  
    return (
      <Modal isOpen={isOpen} onClose={onClose} size="6xl" scrollBehavior="inside">
        <ModalOverlay />
        <ModalContent maxH="90vh">
          <ModalHeader>
            <HStack>
              <Icon as={FiMessageSquare} />
              <Text>Advanced Welcome & Goodbye System</Text>
            </HStack>
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            {isLoading ? (
              <VStack justify="center" h="400px">
                <Spinner size="xl" />
                <Text>Loading Settings...</Text>
              </VStack>
            ) : (
              <Tabs isFitted variant="enclosed" colorScheme="blue">
                <TabList>
                  <Tab><Icon as={FiMessageSquare} mr={2} /> Welcome Messages</Tab>
                  <Tab><Icon as={FiLogOut} mr={2} /> Goodbye Messages</Tab>
                  <Tab><Icon as={FiSettings} mr={2} /> Placeholders</Tab>
                </TabList>
                <TabPanels>
                  <TabPanel>
                    <VStack spacing={6} align="stretch">
                      {/* Welcome System Toggle */}
                      <Card>
                        <CardHeader>
                          <HStack justify="space-between">
                            <Heading size="md">Welcome System</Heading>
                            <Switch
                              size="lg"
                              isChecked={settings.welcome.enabled}
                              onChange={(e) => handleWelcomeChange('enabled', e.target.checked)}
                            />
                          </HStack>
                        </CardHeader>
                        <CardBody>
                          <VStack spacing={4} align="stretch">
                            <FormControl isDisabled={!settings.welcome.enabled}>
                              <FormLabel>
                                <HStack>
                                  <Icon as={FiHash} />
                                  <Text>Welcome Channel</Text>
                                </HStack>
                              </FormLabel>
                              <Select
                                placeholder="Select a channel"
                                value={settings.welcome.channelId || ''}
                                onChange={(e) => handleWelcomeChange('channelId', e.target.value)}
                              >
                                {textChannels.map(channel => (
                                  <option key={channel.id} value={channel.id}>
                                    #{channel.name}
                                  </option>
                                ))}
                              </Select>
                              {textChannels.length === 0 && (
                                <Text fontSize="sm" color="gray.500" mt={1}>
                                  No text channels found. Make sure the bot has permission to view channels.
                                </Text>
                              )}
                            </FormControl>
                          </VStack>
                        </CardBody>
                      </Card>

                      {/* Message Templates */}
                      <Card opacity={!settings.welcome.enabled ? 0.6 : 1}>
                        <CardHeader>
                          <HStack justify="space-between">
                            <VStack align="start" spacing={1}>
                              <Heading size="md">Welcome Message Templates</Heading>
                              <Text fontSize="sm" color="gray.500">
                                Create multiple templates - one will be randomly selected for each new member
                              </Text>
                            </VStack>
                            <HStack>
                              <Badge colorScheme="green" variant="subtle">
                                {settings.welcome.messages.length} template{settings.welcome.messages.length !== 1 ? 's' : ''}
                              </Badge>
                              <Button
                                leftIcon={<FiPlus />}
                                colorScheme="blue"
                                size="sm"
                                onClick={addWelcomeMessage}
                                isDisabled={!settings.welcome.enabled}
                              >
                                Add Template
                              </Button>
                            </HStack>
                          </HStack>
                        </CardHeader>
                        <CardBody>
                          <VStack spacing={4} align="stretch">
                            {/* Template Selector */}
                            <HStack spacing={2} wrap="wrap">
                              {settings.welcome.messages.map((_, index) => (
                                <Button
                                  key={index}
                                  size="sm"
                                  variant={selectedWelcomeMessage === index ? "solid" : "outline"}
                                  colorScheme="blue"
                                  onClick={() => setSelectedWelcomeMessage(index)}
                                  isDisabled={!settings.welcome.enabled}
                                >
                                  Template {index + 1}
                                </Button>
                              ))}
                            </HStack>

                            {/* Current Template Editor */}
                            {settings.welcome.messages[selectedWelcomeMessage] && (
                              <Box p={4} borderWidth={1} borderRadius="md" bg="gray.50" _dark={{ bg: "gray.800" }}>
                                <VStack spacing={4} align="stretch">
                                  <HStack justify="space-between">
                                    <Text fontWeight="bold">Template {selectedWelcomeMessage + 1}</Text>
                                    <HStack>
                                      <Tooltip label="Duplicate Template">
                                        <IconButton
                                          aria-label="Duplicate template"
                                          icon={<FiCopy />}
                                          size="sm"
                                          variant="ghost"
                                          onClick={() => duplicateWelcomeMessage(selectedWelcomeMessage)}
                                          isDisabled={!settings.welcome.enabled}
                                        />
                                      </Tooltip>
                                      <Tooltip label="Delete Template">
                                        <IconButton
                                          aria-label="Delete template"
                                          icon={<FiTrash2 />}
                                          size="sm"
                                          variant="ghost"
                                          colorScheme="red"
                                          onClick={() => removeWelcomeMessage(selectedWelcomeMessage)}
                                          isDisabled={!settings.welcome.enabled || settings.welcome.messages.length <= 1}
                                        />
                                      </Tooltip>
                                    </HStack>
                                  </HStack>

                                  <FormControl isDisabled={!settings.welcome.enabled}>
                                    <FormLabel>Embed Title</FormLabel>
                                    <Input
                                      value={settings.welcome.messages[selectedWelcomeMessage]?.title || ''}
                                      onChange={(e) => updateWelcomeMessage(selectedWelcomeMessage, 'title', e.target.value)}
                                      placeholder="e.g., 🎮 Welcome to {guild}, {userName}!"
                                    />
                                  </FormControl>

                                  <FormControl isDisabled={!settings.welcome.enabled}>
                                    <FormLabel>Embed Description</FormLabel>
                                    <Textarea
                                      value={settings.welcome.messages[selectedWelcomeMessage]?.description || ''}
                                      onChange={(e) => updateWelcomeMessage(selectedWelcomeMessage, 'description', e.target.value)}
                                      placeholder="Enter your welcome message description..."
                                      rows={6}
                                    />
                                  </FormControl>

                                  <HStack spacing={4}>
                                    <FormControl isDisabled={!settings.welcome.enabled}>
                                      <FormLabel>Embed Color</FormLabel>
                                      <Input
                                        type="color"
                                        value={settings.welcome.messages[selectedWelcomeMessage]?.color || '#00FF00'}
                                        onChange={(e) => updateWelcomeMessage(selectedWelcomeMessage, 'color', e.target.value)}
                                        w="100px"
                                      />
                                    </FormControl>
                                    <FormControl isDisabled={!settings.welcome.enabled}>
                                      <FormLabel>Embed Footer</FormLabel>
                                      <Input
                                        value={settings.welcome.messages[selectedWelcomeMessage]?.footer || ''}
                                        onChange={(e) => updateWelcomeMessage(selectedWelcomeMessage, 'footer', e.target.value)}
                                        placeholder="e.g., You're our {memberCount} member!"
                                      />
                                    </FormControl>
                                  </HStack>
                                </VStack>
                              </Box>
                            )}
                          </VStack>
                        </CardBody>
                      </Card>

                      {/* Auto-Role Assignment */}
                      <Card opacity={!settings.welcome.enabled ? 0.6 : 1}>
                        <CardHeader>
                          <HStack justify="space-between">
                            <VStack align="start" spacing={1}>
                              <Heading size="md">Auto-Role Assignment</Heading>
                              <Text fontSize="sm" color="gray.500">
                                Automatically assign roles to new members when they join
                              </Text>
                            </VStack>
                            <Switch
                              isChecked={settings.welcome.autoRole.enabled}
                              onChange={(e) => handleWelcomeAutoRoleChange('enabled', e.target.checked)}
                              isDisabled={!settings.welcome.enabled}
                            />
                          </HStack>
                        </CardHeader>
                        <CardBody>
                          <VStack spacing={4} align="stretch">
                            <FormControl isDisabled={!settings.welcome.enabled || !settings.welcome.autoRole.enabled}>
                              <FormLabel>Roles to Assign</FormLabel>
                              <Box p={3} borderWidth={1} borderRadius="md" maxH="200px" overflowY="auto">
                                <CheckboxGroup
                                  value={settings.welcome.autoRole.roleIds}
                                  onChange={(values) => handleWelcomeAutoRoleChange('roleIds', values)}
                                >
                                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={2}>
                                    {manageableRoles.map(role => (
                                      <Checkbox key={role.id} value={role.id}>
                                        <HStack>
                                          <Box
                                            w={3}
                                            h={3}
                                            borderRadius="full"
                                            bg={`#${role.color.toString(16).padStart(6, '0')}`}
                                          />
                                          <Text>{role.name}</Text>
                                        </HStack>
                                      </Checkbox>
                                    ))}
                                  </SimpleGrid>
                                </CheckboxGroup>
                              </Box>
                              {manageableRoles.length === 0 && (
                                <Text fontSize="sm" color="gray.500" mt={1}>
                                  No manageable roles found. Make sure the bot has permission to manage roles.
                                </Text>
                              )}
                            </FormControl>

                            <Accordion allowToggle>
                              <AccordionItem>
                                <AccordionButton>
                                  <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">Advanced Auto-Role Settings</Text>
                                  </Box>
                                  <AccordionIcon />
                                </AccordionButton>
                                <AccordionPanel pb={4}>
                                  <VStack spacing={4} align="stretch">
                                    <FormControl isDisabled={!settings.welcome.enabled || !settings.welcome.autoRole.enabled}>
                                      <FormLabel>Assignment Delay (milliseconds)</FormLabel>
                                      <NumberInput
                                        value={settings.welcome.autoRole.delay}
                                        onChange={(_, value) => handleWelcomeAutoRoleChange('delay', value)}
                                        min={0}
                                        max={60000}
                                        step={100}
                                      >
                                        <NumberInputField />
                                        <NumberInputStepper>
                                          <NumberIncrementStepper />
                                          <NumberDecrementStepper />
                                        </NumberInputStepper>
                                      </NumberInput>
                                      <Text fontSize="xs" color="gray.500" mt={1}>
                                        Delay before assigning roles (useful if other bots need to process first)
                                      </Text>
                                    </FormControl>

                                    <FormControl display="flex" alignItems="center" isDisabled={!settings.welcome.enabled || !settings.welcome.autoRole.enabled}>
                                      <FormLabel htmlFor="retry-enabled" mb="0">
                                        Enable Retry on Failure
                                      </FormLabel>
                                      <Switch
                                        id="retry-enabled"
                                        isChecked={settings.welcome.autoRole.retry.enabled}
                                        onChange={(e) => handleWelcomeAutoRoleRetryChange('enabled', e.target.checked)}
                                      />
                                    </FormControl>

                                    {settings.welcome.autoRole.retry.enabled && (
                                      <HStack spacing={4}>
                                        <FormControl isDisabled={!settings.welcome.enabled || !settings.welcome.autoRole.enabled}>
                                          <FormLabel>Max Retry Attempts</FormLabel>
                                          <NumberInput
                                            value={settings.welcome.autoRole.retry.maxAttempts}
                                            onChange={(_, value) => handleWelcomeAutoRoleRetryChange('maxAttempts', value)}
                                            min={1}
                                            max={10}
                                          >
                                            <NumberInputField />
                                            <NumberInputStepper>
                                              <NumberIncrementStepper />
                                              <NumberDecrementStepper />
                                            </NumberInputStepper>
                                          </NumberInput>
                                        </FormControl>

                                        <FormControl isDisabled={!settings.welcome.enabled || !settings.welcome.autoRole.enabled}>
                                          <FormLabel>Retry Delay (ms)</FormLabel>
                                          <NumberInput
                                            value={settings.welcome.autoRole.retry.delayBetweenAttempts}
                                            onChange={(_, value) => handleWelcomeAutoRoleRetryChange('delayBetweenAttempts', value)}
                                            min={1000}
                                            max={30000}
                                            step={1000}
                                          >
                                            <NumberInputField />
                                            <NumberInputStepper>
                                              <NumberIncrementStepper />
                                              <NumberDecrementStepper />
                                            </NumberInputStepper>
                                          </NumberInput>
                                        </FormControl>
                                      </HStack>
                                    )}
                                  </VStack>
                                </AccordionPanel>
                              </AccordionItem>
                            </Accordion>
                          </VStack>
                        </CardBody>
                      </Card>

                      {/* Nekos Best GIF Settings */}
                      <Card opacity={!settings.welcome.enabled ? 0.6 : 1}>
                        <CardHeader>
                          <HStack justify="space-between">
                            <VStack align="start" spacing={1}>
                              <Heading size="md">Random GIF Images</Heading>
                              <Text fontSize="sm" color="gray.500">
                                Show random anime GIFs for users without Nitro banners (powered by Nekos.best)
                              </Text>
                            </VStack>
                            <Switch
                              isChecked={settings.welcome.nekosGif?.enabled || false}
                              onChange={(e) => handleWelcomeNekosChange('enabled', e.target.checked)}
                              isDisabled={!settings.welcome.enabled}
                            />
                          </HStack>
                        </CardHeader>
                        <CardBody>
                          <VStack spacing={4} align="stretch">
                            <FormControl isDisabled={!settings.welcome.enabled || !settings.welcome.nekosGif?.enabled}>
                              <FormLabel>GIF Type</FormLabel>
                              <Select
                                value={settings.welcome.nekosGif?.type || 'wave'}
                                onChange={(e) => handleWelcomeNekosChange('type', e.target.value)}
                              >
                                <option value="wave">👋 Wave (Greeting)</option>
                                <option value="hug">🤗 Hug (Welcoming)</option>
                                <option value="pat">👋 Pat (Friendly)</option>
                                <option value="happy">😊 Happy (Cheerful)</option>
                                <option value="dance">💃 Dance (Celebration)</option>
                                <option value="thumbsup">👍 Thumbs Up (Approval)</option>
                              </Select>
                              <Text fontSize="xs" color="gray.500" mt={1}>
                                Choose the type of GIF to show when users don't have a custom banner
                              </Text>
                            </FormControl>

                            <Alert status="info" size="sm">
                              <AlertIcon />
                              <Box>
                                <AlertDescription fontSize="sm">
                                  GIFs are only shown for users without Nitro banners. Users with custom banners will display their banner instead.
                                </AlertDescription>
                              </Box>
                            </Alert>
                          </VStack>
                        </CardBody>
                      </Card>
                    </VStack>
                  </TabPanel>
                  <TabPanel>
                    <VStack spacing={6} align="stretch">
                      {/* Goodbye System Toggle */}
                      <Card>
                        <CardHeader>
                          <HStack justify="space-between">
                            <Heading size="md">Goodbye System</Heading>
                            <Switch
                              size="lg"
                              isChecked={settings.goodbye.enabled}
                              onChange={(e) => handleGoodbyeChange('enabled', e.target.checked)}
                            />
                          </HStack>
                        </CardHeader>
                        <CardBody>
                          <VStack spacing={4} align="stretch">
                            <FormControl isDisabled={!settings.goodbye.enabled}>
                              <FormLabel>
                                <HStack>
                                  <Icon as={FiHash} />
                                  <Text>Goodbye Channel</Text>
                                </HStack>
                              </FormLabel>
                              <Select
                                placeholder="Select a channel"
                                value={settings.goodbye.channelId || ''}
                                onChange={(e) => handleGoodbyeChange('channelId', e.target.value)}
                              >
                                {textChannels.map(channel => (
                                  <option key={channel.id} value={channel.id}>
                                    #{channel.name}
                                  </option>
                                ))}
                              </Select>
                              {textChannels.length === 0 && (
                                <Text fontSize="sm" color="gray.500" mt={1}>
                                  No text channels found. Make sure the bot has permission to view channels.
                                </Text>
                              )}
                            </FormControl>
                          </VStack>
                        </CardBody>
                      </Card>

                      {/* Goodbye Message Templates */}
                      <Card opacity={!settings.goodbye.enabled ? 0.6 : 1}>
                        <CardHeader>
                          <HStack justify="space-between">
                            <VStack align="start" spacing={1}>
                              <Heading size="md">Goodbye Message Templates</Heading>
                              <Text fontSize="sm" color="gray.500">
                                Create multiple templates - one will be randomly selected for each departing member
                              </Text>
                            </VStack>
                            <HStack>
                              <Badge colorScheme="red" variant="subtle">
                                {settings.goodbye.messages.length} template{settings.goodbye.messages.length !== 1 ? 's' : ''}
                              </Badge>
                              <Button
                                leftIcon={<FiPlus />}
                                colorScheme="red"
                                size="sm"
                                onClick={addGoodbyeMessage}
                                isDisabled={!settings.goodbye.enabled}
                              >
                                Add Template
                              </Button>
                            </HStack>
                          </HStack>
                        </CardHeader>
                        <CardBody>
                          <VStack spacing={4} align="stretch">
                            {/* Template Selector */}
                            <HStack spacing={2} wrap="wrap">
                              {settings.goodbye.messages.map((_, index) => (
                                <Button
                                  key={index}
                                  size="sm"
                                  variant={selectedGoodbyeMessage === index ? "solid" : "outline"}
                                  colorScheme="red"
                                  onClick={() => setSelectedGoodbyeMessage(index)}
                                  isDisabled={!settings.goodbye.enabled}
                                >
                                  Template {index + 1}
                                </Button>
                              ))}
                            </HStack>

                            {/* Current Template Editor */}
                            {settings.goodbye.messages[selectedGoodbyeMessage] && (
                              <Box p={4} borderWidth={1} borderRadius="md" bg="gray.50" _dark={{ bg: "gray.800" }}>
                                <VStack spacing={4} align="stretch">
                                  <HStack justify="space-between">
                                    <Text fontWeight="bold">Template {selectedGoodbyeMessage + 1}</Text>
                                    <HStack>
                                      <Tooltip label="Duplicate Template">
                                        <IconButton
                                          aria-label="Duplicate template"
                                          icon={<FiCopy />}
                                          size="sm"
                                          variant="ghost"
                                          onClick={() => duplicateGoodbyeMessage(selectedGoodbyeMessage)}
                                          isDisabled={!settings.goodbye.enabled}
                                        />
                                      </Tooltip>
                                      <Tooltip label="Delete Template">
                                        <IconButton
                                          aria-label="Delete template"
                                          icon={<FiTrash2 />}
                                          size="sm"
                                          variant="ghost"
                                          colorScheme="red"
                                          onClick={() => removeGoodbyeMessage(selectedGoodbyeMessage)}
                                          isDisabled={!settings.goodbye.enabled || settings.goodbye.messages.length <= 1}
                                        />
                                      </Tooltip>
                                    </HStack>
                                  </HStack>

                                  <FormControl isDisabled={!settings.goodbye.enabled}>
                                    <FormLabel>Embed Title</FormLabel>
                                    <Input
                                      value={settings.goodbye.messages[selectedGoodbyeMessage]?.title || ''}
                                      onChange={(e) => updateGoodbyeMessage(selectedGoodbyeMessage, 'title', e.target.value)}
                                      placeholder="e.g., 👋 Goodbye {userName}"
                                    />
                                  </FormControl>

                                  <FormControl isDisabled={!settings.goodbye.enabled}>
                                    <FormLabel>Embed Description</FormLabel>
                                    <Textarea
                                      value={settings.goodbye.messages[selectedGoodbyeMessage]?.description || ''}
                                      onChange={(e) => updateGoodbyeMessage(selectedGoodbyeMessage, 'description', e.target.value)}
                                      placeholder="Enter your goodbye message description..."
                                      rows={6}
                                    />
                                  </FormControl>

                                  <HStack spacing={4}>
                                    <FormControl isDisabled={!settings.goodbye.enabled}>
                                      <FormLabel>Embed Color</FormLabel>
                                      <Input
                                        type="color"
                                        value={settings.goodbye.messages[selectedGoodbyeMessage]?.color || '#FF0000'}
                                        onChange={(e) => updateGoodbyeMessage(selectedGoodbyeMessage, 'color', e.target.value)}
                                        w="100px"
                                      />
                                    </FormControl>
                                    <FormControl isDisabled={!settings.goodbye.enabled}>
                                      <FormLabel>Embed Footer</FormLabel>
                                      <Input
                                        value={settings.goodbye.messages[selectedGoodbyeMessage]?.footer || ''}
                                        onChange={(e) => updateGoodbyeMessage(selectedGoodbyeMessage, 'footer', e.target.value)}
                                        placeholder="e.g., Safe travels!"
                                      />
                                    </FormControl>
                                  </HStack>
                                </VStack>
                              </Box>
                            )}
                          </VStack>
                        </CardBody>
                      </Card>

                      {/* Nekos Best GIF Settings for Goodbye */}
                      <Card opacity={!settings.goodbye.enabled ? 0.6 : 1}>
                        <CardHeader>
                          <HStack justify="space-between">
                            <VStack align="start" spacing={1}>
                              <Heading size="md">Random GIF Images</Heading>
                              <Text fontSize="sm" color="gray.500">
                                Show random anime GIFs for users without Nitro banners (powered by Nekos.best)
                              </Text>
                            </VStack>
                            <Switch
                              isChecked={settings.goodbye.nekosGif?.enabled || false}
                              onChange={(e) => handleGoodbyeNekosChange('enabled', e.target.checked)}
                              isDisabled={!settings.goodbye.enabled}
                            />
                          </HStack>
                        </CardHeader>
                        <CardBody>
                          <VStack spacing={4} align="stretch">
                            <FormControl isDisabled={!settings.goodbye.enabled || !settings.goodbye.nekosGif?.enabled}>
                              <FormLabel>GIF Type</FormLabel>
                              <Select
                                value={settings.goodbye.nekosGif?.type || 'cry'}
                                onChange={(e) => handleGoodbyeNekosChange('type', e.target.value)}
                              >
                                <option value="cry">😢 Cry (Sad Goodbye)</option>
                                <option value="wave">👋 Wave (Farewell)</option>
                                <option value="sad">😔 Sad (Melancholy)</option>
                                <option value="sleep">😴 Sleep (Peaceful)</option>
                                <option value="pat">👋 Pat (Comforting)</option>
                                <option value="hug">🤗 Hug (Supportive)</option>
                              </Select>
                              <Text fontSize="xs" color="gray.500" mt={1}>
                                Choose the type of GIF to show when users don't have a custom banner
                              </Text>
                            </FormControl>

                            <Alert status="info" size="sm">
                              <AlertIcon />
                              <Box>
                                <AlertDescription fontSize="sm">
                                  GIFs are only shown for users without Nitro banners. Users with custom banners will display their banner instead.
                                </AlertDescription>
                              </Box>
                            </Alert>
                          </VStack>
                        </CardBody>
                      </Card>
                    </VStack>
                  </TabPanel>

                  {/* Placeholders Tab */}
                  <TabPanel>
                    <VStack spacing={6} align="stretch">
                      <Alert status="info">
                        <AlertIcon />
                        <Box>
                          <AlertTitle>Placeholder Guide</AlertTitle>
                          <AlertDescription>
                            Use these placeholders in your message templates. They will be automatically replaced with actual values when messages are sent.
                          </AlertDescription>
                        </Box>
                      </Alert>

                      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
                        {/* User Placeholders */}
                        <Card>
                          <CardHeader>
                            <Heading size="md" color="blue.500">👤 User Placeholders</Heading>
                          </CardHeader>
                          <CardBody>
                            <VStack spacing={3} align="stretch">
                              {PLACEHOLDERS.user.map((placeholder, index) => (
                                <Box key={index} p={3} borderWidth={1} borderRadius="md" bg="blue.50" _dark={{ bg: "blue.900" }}>
                                  <HStack justify="space-between">
                                    <VStack align="start" spacing={1}>
                                      <Text fontFamily="mono" fontWeight="bold" color="blue.600" _dark={{ color: "blue.300" }}>
                                        {placeholder.name}
                                      </Text>
                                      <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>
                                        {placeholder.description}
                                      </Text>
                                    </VStack>
                                    <IconButton
                                      aria-label="Copy placeholder"
                                      icon={<FiCopy />}
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => {
                                        navigator.clipboard.writeText(placeholder.name);
                                        toast({
                                          title: 'Copied!',
                                          description: `${placeholder.name} copied to clipboard`,
                                          status: 'success',
                                          duration: 2000,
                                        });
                                      }}
                                    />
                                  </HStack>
                                </Box>
                              ))}
                            </VStack>
                          </CardBody>
                        </Card>

                        {/* Guild Placeholders */}
                        <Card>
                          <CardHeader>
                            <Heading size="md" color="green.500">🏰 Server Placeholders</Heading>
                          </CardHeader>
                          <CardBody>
                            <VStack spacing={3} align="stretch">
                              {PLACEHOLDERS.guild.map((placeholder, index) => (
                                <Box key={index} p={3} borderWidth={1} borderRadius="md" bg="green.50" _dark={{ bg: "green.900" }}>
                                  <HStack justify="space-between">
                                    <VStack align="start" spacing={1}>
                                      <Text fontFamily="mono" fontWeight="bold" color="green.600" _dark={{ color: "green.300" }}>
                                        {placeholder.name}
                                      </Text>
                                      <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>
                                        {placeholder.description}
                                      </Text>
                                    </VStack>
                                    <IconButton
                                      aria-label="Copy placeholder"
                                      icon={<FiCopy />}
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => {
                                        navigator.clipboard.writeText(placeholder.name);
                                        toast({
                                          title: 'Copied!',
                                          description: `${placeholder.name} copied to clipboard`,
                                          status: 'success',
                                          duration: 2000,
                                        });
                                      }}
                                    />
                                  </HStack>
                                </Box>
                              ))}
                            </VStack>
                          </CardBody>
                        </Card>

                        {/* Time Placeholders */}
                        <Card>
                          <CardHeader>
                            <Heading size="md" color="purple.500">⏰ Time Placeholders</Heading>
                          </CardHeader>
                          <CardBody>
                            <VStack spacing={3} align="stretch">
                              {PLACEHOLDERS.time.map((placeholder, index) => (
                                <Box key={index} p={3} borderWidth={1} borderRadius="md" bg="purple.50" _dark={{ bg: "purple.900" }}>
                                  <HStack justify="space-between">
                                    <VStack align="start" spacing={1}>
                                      <Text fontFamily="mono" fontWeight="bold" color="purple.600" _dark={{ color: "purple.300" }}>
                                        {placeholder.name}
                                      </Text>
                                      <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>
                                        {placeholder.description}
                                      </Text>
                                    </VStack>
                                    <IconButton
                                      aria-label="Copy placeholder"
                                      icon={<FiCopy />}
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => {
                                        navigator.clipboard.writeText(placeholder.name);
                                        toast({
                                          title: 'Copied!',
                                          description: `${placeholder.name} copied to clipboard`,
                                          status: 'success',
                                          duration: 2000,
                                        });
                                      }}
                                    />
                                  </HStack>
                                </Box>
                              ))}
                            </VStack>
                          </CardBody>
                        </Card>

                        {/* Welcome-specific Placeholders */}
                        <Card>
                          <CardHeader>
                            <Heading size="md" color="orange.500">🎉 Welcome Only</Heading>
                          </CardHeader>
                          <CardBody>
                            <VStack spacing={3} align="stretch">
                              {PLACEHOLDERS.welcome.map((placeholder, index) => (
                                <Box key={index} p={3} borderWidth={1} borderRadius="md" bg="orange.50" _dark={{ bg: "orange.900" }}>
                                  <HStack justify="space-between">
                                    <VStack align="start" spacing={1}>
                                      <Text fontFamily="mono" fontWeight="bold" color="orange.600" _dark={{ color: "orange.300" }}>
                                        {placeholder.name}
                                      </Text>
                                      <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>
                                        {placeholder.description}
                                      </Text>
                                    </VStack>
                                    <IconButton
                                      aria-label="Copy placeholder"
                                      icon={<FiCopy />}
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => {
                                        navigator.clipboard.writeText(placeholder.name);
                                        toast({
                                          title: 'Copied!',
                                          description: `${placeholder.name} copied to clipboard`,
                                          status: 'success',
                                          duration: 2000,
                                        });
                                      }}
                                    />
                                  </HStack>
                                </Box>
                              ))}
                            </VStack>
                          </CardBody>
                        </Card>

                        {/* Goodbye-specific Placeholders */}
                        <Card>
                          <CardHeader>
                            <Heading size="md" color="red.500">👋 Goodbye Only</Heading>
                          </CardHeader>
                          <CardBody>
                            <VStack spacing={3} align="stretch">
                              {PLACEHOLDERS.goodbye.map((placeholder, index) => (
                                <Box key={index} p={3} borderWidth={1} borderRadius="md" bg="red.50" _dark={{ bg: "red.900" }}>
                                  <HStack justify="space-between">
                                    <VStack align="start" spacing={1}>
                                      <Text fontFamily="mono" fontWeight="bold" color="red.600" _dark={{ color: "red.300" }}>
                                        {placeholder.name}
                                      </Text>
                                      <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>
                                        {placeholder.description}
                                      </Text>
                                    </VStack>
                                    <IconButton
                                      aria-label="Copy placeholder"
                                      icon={<FiCopy />}
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => {
                                        navigator.clipboard.writeText(placeholder.name);
                                        toast({
                                          title: 'Copied!',
                                          description: `${placeholder.name} copied to clipboard`,
                                          status: 'success',
                                          duration: 2000,
                                        });
                                      }}
                                    />
                                  </HStack>
                                </Box>
                              ))}
                            </VStack>
                          </CardBody>
                        </Card>
                      </SimpleGrid>
                    </VStack>
                  </TabPanel>
                </TabPanels>
              </Tabs>
            )}
          </ModalBody>
          <ModalFooter>
            <HStack spacing={3}>
              <Button variant="ghost" onClick={onClose}>
                Cancel
              </Button>
              <Button
                colorScheme="blue"
                onClick={handleSave}
                isLoading={isSaving}
                isDisabled={isLoading}
                leftIcon={<FiShuffle />}
              >
                Save Welcome & Goodbye System
              </Button>
            </HStack>
          </ModalFooter>
        </ModalContent>
      </Modal>
    );
  }
  