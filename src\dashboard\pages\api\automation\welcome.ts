// @ts-nocheck
import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { DatabaseManager } from '@/core/DatabaseManager';
import { ConfigManager } from '@/core/ConfigManager';
import { dashboardConfig } from '../../../core/config';

const config = ConfigManager.getConfig();
const dbManager = new DatabaseManager(config);

const defaultSettings = {
  welcome: {
    enabled: false,
    channelId: null,
    messages: [
      {
        title: "🎮 Welcome to {guild}, {userName}!",
        description: "Pull up a chair and make yourself comfortable. We've got games, chaos, and questionable life choices waiting for you!\n\n📅 Account Created: {UserCreation}\n🎯 Join Date: {joinDate}",
        color: "#FF6B35",
        footer: "You're our {memberCount} member - let's get weird!"
      },
      {
        title: "🚨 Error 404: Chill Not Found! 🚨",
        description: "Hey {user}, looks like you've stumbled into our chaotic corner of the internet. Hope you brought snacks and a sense of humor!\n\n🎮 Member #{memberCountNumeric}\n📅 Joined: {joinTime}",
        color: "#8B0000",
        footer: "Welcome to the madness!"
      },
      {
        title: "🎯 Player {userName} has entered the game!",
        description: "Welcome to {server} where the games are wild and the conversations are... well, let's just say we're not your typical Discord server.\n\n📅 Account Age: {UserCreation}\n🎯 Member Since: {user-joinedAt}",
        color: "#1E90FF",
        footer: "Time to level up your social life!"
      },
      {
        title: "🔥 New challenger has appeared!",
        description: "It's {user}! Welcome to our adult gaming sanctuary where we take games seriously but ourselves... not so much.\n\n🎮 Member #{memberCountNumeric}\n⏰ Joined: {joinTime}",
        color: "#DC143C",
        footer: "Ready to game and chill?"
      },
      {
        title: "🎲 Welcome to the chaos, {userName}!",
        description: "You've joined {server} - where good decisions come to die and epic gaming moments are born. Buckle up!\n\n📅 Account Created: {user-createdAt}\n🎯 Join Time: {joinTime}",
        color: "#9932CC",
        footer: "Let the games begin!"
      }
    ],
    autoRole: {
      enabled: false,
      roleIds: [],
      delay: 1000,
      retry: {
        enabled: true,
        maxAttempts: 3,
        delayBetweenAttempts: 5000
      }
    },
    nekosGif: {
      enabled: true,
      type: 'wave'
    },
    // Legacy fields for backward compatibility
    message: 'Welcome {user} to {guild}! You are the {memberCount}th member.',
    autoRoles: [],
    embedColor: '#00FF00',
  },
  goodbye: {
    enabled: false,
    channelId: null,
    messages: [
      {
        title: "💀 Game Over for {userName}",
        description: "Looks like {userName} has rage quit from {server}.\nThanks for the memories and questionable life choices!\n\n{kickStatus}\n⏰ Member Since: {user-joinedAt}",
        color: "#8B0000",
        footer: "Press F to pay respects"
      },
      {
        title: "🚪 {userName} has left the building",
        description: "Another one bites the dust! {userName} decided our chaos wasn't for them.\nCan't win 'em all, I guess.\n\n{kickStatus}\n🕒 Left: {leaveTime}",
        color: "#696969",
        footer: "The door's always open... maybe"
      },
      {
        title: "📤 Connection lost: {userName}",
        description: "{userName} has disconnected from {guild}.\nHope they found what they were looking for!\n\n{kickStatus}\n⏰ Member Since: {user-joinedAt}",
        color: "#FF6347",
        footer: "Thanks for gaming with us!"
      }
    ],
    nekosGif: {
      enabled: true,
      type: 'cry'
    },
    // Legacy fields for backward compatibility
    message: 'Goodbye {user}! We will miss you.',
    embedColor: '#FF0000',
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // Get guild ID from dashboard config instead of session
  const guildId = dashboardConfig.bot.guildId;
  if (!guildId) {
    return res.status(400).json({ error: 'Guild ID not found in configuration.' });
  }

  try {
    await dbManager.connect();

    if (req.method === 'GET') {
      const guildConfig = await dbManager.findOne('guild_configs', { guildId });

      // Handle backward compatibility for old message format
      const processWelcomeSettings = (stored) => {
        if (!stored) return defaultSettings.welcome;

        const processed = { ...defaultSettings.welcome, ...stored };

        // Convert old single message format to new template format if needed
        if (stored.message && !stored.messages) {
          processed.messages = [
            {
              title: "Welcome to {guild}!",
              description: stored.message,
              color: stored.embedColor || "#00FF00",
              footer: "Welcome to our server!"
            }
          ];
        }

        // Convert old autoRoles array to new autoRole structure if needed
        if (stored.autoRoles && !stored.autoRole) {
          processed.autoRole = {
            ...defaultSettings.welcome.autoRole,
            roleIds: stored.autoRoles
          };
        }

        return processed;
      };

      const processGoodbyeSettings = (stored) => {
        if (!stored) return defaultSettings.goodbye;

        const processed = { ...defaultSettings.goodbye, ...stored };

        // Convert old single message format to new template format if needed
        if (stored.message && !stored.messages) {
          processed.messages = [
            {
              title: "Goodbye!",
              description: stored.message,
              color: stored.embedColor || "#FF0000",
              footer: "Thanks for being part of our server!"
            }
          ];
        }

        return processed;
      };

      const settings = {
        welcome: processWelcomeSettings(guildConfig?.welcome),
        goodbye: processGoodbyeSettings(guildConfig?.goodbye),
      };

      return res.status(200).json(settings);
    }

    if (req.method === 'POST') {
      const { welcome, goodbye } = req.body;

      // Basic validation
      if (typeof welcome?.enabled !== 'boolean' || typeof goodbye?.enabled !== 'boolean') {
        return res.status(400).json({ error: 'Invalid data format.' });
      }

      // Validate message templates
      if (welcome.enabled && (!welcome.messages || !Array.isArray(welcome.messages) || welcome.messages.length === 0)) {
        return res.status(400).json({ error: 'Welcome messages are required when welcome system is enabled.' });
      }

      if (goodbye.enabled && (!goodbye.messages || !Array.isArray(goodbye.messages) || goodbye.messages.length === 0)) {
        return res.status(400).json({ error: 'Goodbye messages are required when goodbye system is enabled.' });
      }

      // Validate message template structure
      const validateMessageTemplate = (template, type) => {
        if (!template.description) {
          throw new Error(`${type} message template must have a description.`);
        }
        if (template.color && !/^#[0-9A-Fa-f]{6}$/.test(template.color)) {
          throw new Error(`${type} message template color must be a valid hex color.`);
        }
      };

      try {
        if (welcome.enabled) {
          welcome.messages.forEach((template, index) => {
            validateMessageTemplate(template, `Welcome template ${index + 1}`);
          });
        }

        if (goodbye.enabled) {
          goodbye.messages.forEach((template, index) => {
            validateMessageTemplate(template, `Goodbye template ${index + 1}`);
          });
        }
      } catch (validationError) {
        return res.status(400).json({ error: validationError.message });
      }

      const updateData = {
        guildId,
        welcome: {
          enabled: welcome.enabled,
          channelId: welcome.channelId || null,
          messages: welcome.messages || defaultSettings.welcome.messages,
          autoRole: {
            enabled: welcome.autoRole?.enabled || false,
            roleIds: welcome.autoRole?.roleIds || [],
            delay: welcome.autoRole?.delay || 1000,
            retry: {
              enabled: welcome.autoRole?.retry?.enabled || true,
              maxAttempts: welcome.autoRole?.retry?.maxAttempts || 3,
              delayBetweenAttempts: welcome.autoRole?.retry?.delayBetweenAttempts || 5000
            }
          },
          nekosGif: {
            enabled: welcome.nekosGif?.enabled || true,
            type: welcome.nekosGif?.type || 'wave'
          },
          // Keep legacy fields for backward compatibility
          message: welcome.messages?.[0]?.description || defaultSettings.welcome.message,
          autoRoles: welcome.autoRole?.roleIds || [],
          embedColor: welcome.messages?.[0]?.color || defaultSettings.welcome.embedColor,
        },
        goodbye: {
          enabled: goodbye.enabled,
          channelId: goodbye.channelId || null,
          messages: goodbye.messages || defaultSettings.goodbye.messages,
          nekosGif: {
            enabled: goodbye.nekosGif?.enabled || true,
            type: goodbye.nekosGif?.type || 'cry'
          },
          // Keep legacy fields for backward compatibility
          message: goodbye.messages?.[0]?.description || defaultSettings.goodbye.message,
          embedColor: goodbye.messages?.[0]?.color || defaultSettings.goodbye.embedColor,
        },
      };

      await dbManager.updateOne(
        'guild_configs',
        { guildId },
        { $set: updateData },
        { upsert: true }
      );

      return res.status(200).json({
        message: 'Welcome & Goodbye system updated successfully.',
        welcomeTemplates: welcome.messages?.length || 0,
        goodbyeTemplates: goodbye.messages?.length || 0
      });
    }

    res.setHeader('Allow', ['GET', 'POST']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  } catch (error) {
    console.error(`[API/automation/welcome] Error:`, error);
    return res.status(500).json({ error: 'Internal Server Error' });
  }
}
